"use client";
import React from "react";
import "./shimmer.scss";
import PriceShimmer from "@/app/HotelSearchResult/components/HotelCard/PriceShimmer";

interface HotelCardShimmerProps {
  type?: "list" | "grid";
}

const HotelCardShimmer: React.FC<HotelCardShimmerProps> = ({ type = "list" }) => {
  const isGrid = type === "grid";

  return (
    <div className={`hotel-card-shimmer relative bg-white rounded-lg shadow-lg border border-gray-100 overflow-hidden ${
      isGrid
        ? "hotel-card-shimmer--grid max-w-sm mx-auto mb-6"
        : "hotel-card-shimmer--list mb-6"
    }`}>

      <div className={`flex ${isGrid ? "flex-col" : "flex-col md:flex-row"} ${isGrid ? "min-h-auto" : "min-h-[280px]"}`}>
        {/* Image Section - Responsive for both layouts */}
        <div className={`relative ${
          isGrid
            ? "w-full h-48"
            : "w-full md:w-[30%] h-48 md:h-auto"
        }`}>
          <div className="shimmer w-full h-full bg-gray-200"></div>
          {/* Heart Icon Shimmer */}
          <div className="absolute top-3 right-3 bg-white bg-opacity-70 p-2 rounded-full">
            <div className="shimmer w-5 h-5 rounded bg-gray-200"></div>
          </div>
        </div>

        {/* Content Section - Flexible for both layouts */}
        <div className={`p-4 ${
          isGrid
            ? "w-full border-b border-gray-200"
            : "w-full md:w-[45%] md:p-6 border-b md:border-b-0 md:border-r border-gray-200"
        }`}>
          {/* Hotel Name & Type */}
          <div className="mb-3">
            <div className="flex items-center gap-2 mb-1">
              <div className={`shimmer h-6 bg-gray-200 rounded ${isGrid ? "w-40" : "w-48"}`}></div>
              <div className="shimmer h-5 w-16 bg-gray-200 rounded"></div>
            </div>
            <div className="flex items-center mt-1">
              <div className="flex gap-1 mr-2">
                {[...Array(4)].map((_, index) => (
                  <div key={index} className="shimmer w-4 h-4 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>

          {/* Location */}
          <div className="mb-4">
            <div className="flex items-center mb-1">
              <div className="shimmer w-3.5 h-3.5 bg-gray-200 rounded mr-2"></div>
              <div className={`shimmer h-4 bg-gray-200 rounded ${isGrid ? "w-28" : "w-32"}`}></div>
            </div>
            <div className="flex items-center">
              <div className="shimmer w-3.5 h-3.5 bg-gray-200 rounded mr-2"></div>
              <div className={`shimmer h-4 bg-gray-200 rounded ${isGrid ? "w-36" : "w-40"}`}></div>
            </div>
          </div>

          {/* Badges */}
          <div className="flex flex-wrap gap-2 mb-4">
            <div className="shimmer h-6 w-24 bg-gray-200 rounded-full"></div>
            <div className="shimmer h-6 w-20 bg-gray-200 rounded-full"></div>
            {!isGrid && <div className="shimmer h-6 w-18 bg-gray-200 rounded-full"></div>}
          </div>

          {/* Amenities */}
          <div className={`flex flex-wrap gap-x-4 gap-y-1 mb-2 ${isGrid ? "gap-x-2" : ""}`}>
            {[
              { iconWidth: "w-4", textWidth: isGrid ? "w-14" : "w-16" },
              { iconWidth: "w-4", textWidth: isGrid ? "w-16" : "w-20" },
              { iconWidth: "w-4", textWidth: isGrid ? "w-20" : "w-24" }
            ].map((item, index) => (
              <div key={index} className="flex items-center">
                <div className={`shimmer ${item.iconWidth} h-4 bg-gray-200 rounded mr-2`}></div>
                <div className={`shimmer h-4 ${item.textWidth} bg-gray-200 rounded`}></div>
              </div>
            ))}
          </div>
        </div>

        {/* Pricing Section - Flexible for both layouts */}
        <div className={`p-4 ${
          isGrid
            ? "w-full"
            : "w-full md:w-[25%] md:p-6"
        } flex flex-col justify-between`}>
          {isGrid ? (
            /* Grid Layout - Split container with rating and pricing */
            <div className="flex justify-between items-start mb-3">
              {/* Left side - Rating */}
              <div className="flex items-center gap-2">
                <div>
                  <div className="shimmer h-4 w-12 bg-gray-200 rounded mb-1"></div>
                  <div className="shimmer h-3 w-16 bg-gray-200 rounded"></div>
                </div>
                <div className="shimmer h-8 w-10 bg-gray-200 rounded"></div>
              </div>

              {/* Right side - Pricing */}
              <div className="text-right">
                <PriceShimmer layout="grid" />
              </div>
            </div>
          ) : (
            /* List Layout - Rating and pricing stacked */
            <>
              <div className="flex items-center justify-end gap-2 mb-3">
                <div className="text-right">
                  <div className="shimmer h-4 w-12 bg-gray-200 rounded mb-1"></div>
                  <div className="shimmer h-3 w-16 bg-gray-200 rounded"></div>
                </div>
                <div className="shimmer h-8 w-10 bg-gray-200 rounded"></div>
              </div>

              {/* Pricing section for list view */}
              <div className="space-y-2">
                <PriceShimmer layout="list" />
                <div className="shimmer h-10 w-full bg-gray-200 rounded-lg"></div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default HotelCardShimmer;
