'use client';

import React, { useRef, useEffect } from 'react';
import mapboxgl from 'mapbox-gl';
import ReactDOMServer from 'react-dom/server';

mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_GL_ACCESS_TOKEN!;

export interface MarkerData {
  lng: number;
  lat: number;
  customElement?: HTMLElement;
  popupData?: any;
}

export interface MapBoxProps {
  center?: [number, number];
  zoom?: number;
  markers?: MarkerData[];
  className?: string;
  style?: React.CSSProperties;
  popupRenderer?: (data: any) => React.ReactNode;
}

const MapBox: React.FC<MapBoxProps> = ({
  center = [76.2673, 9.9312],
  zoom = 12,
  markers = [],
  className = 'w-full h-[500px]',
  style,
  popupRenderer,
}) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const mapRef = useRef<mapboxgl.Map | null>(null);

  // initialize map
  useEffect(() => {
    if (!mapContainer.current) return;

    if (mapRef.current) return;

    mapRef.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/streets-v11',
      center,
      zoom,
    });

    mapRef.current.addControl(new mapboxgl.NavigationControl());

    return () => {
      mapRef.current?.remove();
    };
  }, [center, zoom]);

  // add markers with popups
  useEffect(() => {
    if (!mapRef.current) return;

    markers.forEach(marker => {
      let el: HTMLElement;
      if (marker.customElement) {
        el = marker.customElement;
      } else {
        el = document.createElement('div');
        el.className =
          'bg-blue-700 text-white text-xs font-bold px-2 py-1 rounded';
        el.textContent = '₹'; // default
      }

      const m = new mapboxgl.Marker(el).setLngLat([marker.lng, marker.lat]);

      if (popupRenderer && marker.popupData) {
        const popupContent = ReactDOMServer.renderToString(
          popupRenderer(marker.popupData)
        );

        const popup = new mapboxgl.Popup({ offset: 25, closeButton: true }).setHTML(
          popupContent
        );
        m.setPopup(popup);
      }

      m.addTo(mapRef.current!);
    });
  }, [markers, popupRenderer]);

  return <div ref={mapContainer} className={className} style={style} />;
};

export default MapBox;
