import { Hotel } from '@/models/hotel/list-page.model';
import React from 'react'
import HotelCard from '../../hotel-card/HotelCard';

interface props{
    hotels: Hotel[];
    isLoading: boolean;
}

function MapHotelList({hotels , isLoading}:props) {
  return (
    <div className='flex direction-column gap-6'>
        {hotels.map((item,index)=>(
          <HotelCard 
            key={index}
            hotel={item}
            type="list"
            handleClick={()=>{}}
            onCheckboxChange={()=>{}}
            isChecked={false}
            isSearchCompleted={isLoading}
            isPricingAvailable={isLoading}
          />  
        ))}
    </div>
  )
}

export default MapHotelList