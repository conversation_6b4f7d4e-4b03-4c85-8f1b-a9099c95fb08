import React from 'react';

const ShimmerCard = () => (
  <div className="border border-gray-200 rounded-lg overflow-hidden">
    <div className="flex flex-col lg:flex-row">
      {/* Left Side Shimmer */}
      <div className="flex-1 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="sm:w-28 h-20 sm:h-auto flex-shrink-0 bg-gray-200 rounded-lg"></div>
          <div className="flex-1 space-y-2">
            <div className="h-3.5 bg-gray-200 rounded w-3/4"></div>
            <div className="h-2.5 bg-gray-200 rounded w-1/2"></div>
            <div className="h-2.5 bg-gray-200 rounded w-5/6"></div>
            <div className="h-2.5 bg-gray-200 rounded w-1/3"></div>
          </div>
        </div>
      </div>

      {/* Right Side Shimmer */}
      <div className="lg:w-52 bg-gray-50 p-4 border-l border-gray-200">
        <div className="flex flex-col items-center justify-center h-full space-y-3">
          <div className="h-6 bg-gray-300 rounded w-2/3"></div>
          <div className="h-3 bg-gray-200 rounded w-full"></div>
          <div className="h-9 bg-gray-300 rounded-lg w-full mt-2"></div>
        </div>
      </div>
    </div>
  </div>
);

const RoomRatesDisplayShimmer = () => {
  return (
    <div className="max-w-6xl mx-auto p-2 animate-pulse">
      {/* Header Shimmer */}
      <div className="mb-3">
        <div className="h-5 bg-gray-300 rounded w-1/4 mb-2.5"></div>
        
        {/* Filters Shimmer */}
        <div className="flex flex-wrap items-center gap-3 mb-2.5">
          <div className="h-2.5 bg-gray-200 rounded w-20"></div>
          <div className="flex flex-wrap gap-2">
            <div className="h-4 bg-gray-200 rounded w-24"></div>
            <div className="h-4 bg-gray-200 rounded w-28"></div>
            <div className="h-4 bg-gray-200 rounded w-28"></div>
            <div className="h-4 bg-gray-200 rounded w-24"></div>
          </div>
        </div>

        {/* Search Bar Shimmer */}
        <div className="h-7 bg-gray-200 rounded-md w-full max-w-sm mb-2.5"></div>

        {/* Results Count Shimmer */}
        <div className="h-2.5 bg-gray-200 rounded w-1/6"></div>
      </div>

      {/* Room Group Shimmer */}
      <div className="space-y-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {/* Room Group Header Shimmer */}
            <div className="px-3 py-2 border-b border-gray-200 bg-gray-50">
              <div className="h-4 bg-gray-300 rounded w-1/3"></div>
            </div>

            {/* Room Options Shimmer */}
            <div className="p-3 space-y-3">
              <ShimmerCard />
            </div>
          </div>
      </div>
    </div>
  );
};

export default RoomRatesDisplayShimmer;
