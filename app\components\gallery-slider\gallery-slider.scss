// components/GallerySlider/GallerySlider.scss
@use "/styles/zIndex" as *;
@use "/styles/variable" as *;


.gallery-slider-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  background-color: #f5f5f5;
  margin: 0 auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.gallery-slider-content {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 66.67%; // 3:2 aspect ratio, adjust as needed
}

.gallery-slider-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.gallery-slider-image {
  object-fit: cover;
  transition: opacity 0.3s ease-in-out;
  
  &.transitioning {
    opacity: 0.7;
  }
}

.gallery-slider-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(255, 255, 255, 0.7);
  color: #333;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: z-index(base);

  @media (max-width: $breakpoint-md) {
    width: 30px;
    height: 30px;
  }

  @media (max-width: $breakpoint-sm) {
    width: 24px;
    height: 24px;
  }
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.3);
  }
}

.gallery-slider-button-prev {
  left: 15px;
}

.gallery-slider-button-next {
  right: 15px;
}

.gallery-slider-arrow {
  font-size: 18px;
  font-weight: bold;
  line-height: 1;

  @media (max-width: $breakpoint-md) {
    font-size: 16px;
  }

  @media (max-width: $breakpoint-sm) {
    font-size: 14px;
  }
}

.gallery-slider-indicator {
  position: absolute;
  bottom: 15px;
  right: 15px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 5px 10px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  z-index: z-index(base);
}