"use client";
import React, { useState, useEffect } from "react";
// import Image from "next/image";
import { useTranslation } from "@/app/hooks/useTranslation";
import "./MobileMenuContent.scss";
import Image from "next/image";
import { useLanguage } from "@/app/contexts/languageContext";
import { createPortal } from "react-dom";
import {
  Calendar,
  User,
  Heart,
  Star,
  Headphones,
  Globe,
  DollarSign
} from "lucide-react";
import { CurrencyItem, User as UserDetail } from "@/models/common.model";

// Define types
type NavItem = {
  label: string;
  icon: string;
};

interface MobileMenuContentProps {
  userData?: UserDetail
  activeProfileItem: string;
  isRTL?: boolean;
  handleProfileMenuAtMobileClick: (item: string) => void;
  profileItems: NavItem[];
  settingsItems: NavItem[];
  // Modal props
  isLanguageModalOpen: boolean;
  isPriceModalOpen: boolean;
  setIsLanguageModalOpen: (open: boolean) => void;
  setIsPriceModalOpen: (open: boolean) => void;
  // Current pathname for active state
  currentPathname?: string | null;
}

// Define language mapping for display names
export const languageMap: Record<string, string> = {
  en: 'English',
  ar: 'العربية',
  es: 'Español',
  fr: 'Français',
  hi: 'हिन्दी', // Hindi
};

// Define flag mapping for languages
export const languageFlags: Record<string, string> = {
  en: '/assets/img/country-logo/gb.webp', // UK flag for English
  ar: '/assets/img/country-logo/sa.webp', // Saudi Arabia flag for Arabic
  es: '/assets/img/country-logo/es.webp', // Spain flag for Spanish
  fr: '/assets/img/country-logo/fr.webp', // France flag for French
  hi: '/assets/img/country-logo/in.webp', // India flag for Hindi
};




// Services navigation items
const SERVICES_NAV_ITEMS = [
  {
    id: "hotels",
    icon: "/assets/img/Kindali Service icons/BLUE HOTEL PNG.png",
    label: "Stays"
  },
  {
    id: "flight",
    icon: "/assets/img/Kindali Service icons/BLUE FLIGHT PNG.png",
    label: "Flights"
  },
  {
    id: "visa",
    icon: "/assets/img/Kindali Service icons/BLUE VISA PNG.png",
    label: "Visa"
  },
  {
    id: "cruise",
    icon: "/assets/img/Kindali Service icons/BLUE SHIP PNG.png",
    label: "Cruise"
  }
];

// Function to get appropriate Lucide icon for menu items
const getMenuIcon = (label: string) => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    'Bookings': Calendar,
    'Profile': User,
    'Wishlist': Heart,
    'Reviews': Star,
    'Helpline': Headphones,
  };

  return iconMap[label] || User; // Default to User icon if not found
};

const MobileMenuContent: React.FC<MobileMenuContentProps> = ({
  userData,
  activeProfileItem,
  isRTL,
  handleProfileMenuAtMobileClick,
  profileItems,
  settingsItems,
  isLanguageModalOpen,
  isPriceModalOpen,
  setIsLanguageModalOpen,
  setIsPriceModalOpen,
  currentPathname,
}) => {
  const { t } = useTranslation("common");

  // Function to determine if a service is active based on current pathname
  const isServiceActive = (serviceId: string): boolean => {
    if (!currentPathname) return false;

    switch (serviceId) {
      case "hotels":
        return currentPathname === "/hotel" || currentPathname === "/hotel";
      case "flight":
        return currentPathname === "/flights";
      case "visa":
        return currentPathname === "/visa";
      case "cruise":
        return currentPathname === "/cruise";
      default:
        return false;
    }
  };

  return (
    <div className="mobile-menu-content">
      {/* User greeting if logged in */}
      {userData?.name && (
        <div className="user-greeting">
          <div className="user-avatar">
            <span className="avatar-placeholder">
              {userData.name.charAt(0).toUpperCase()}
            </span>
          </div>
          <div className="user-info">
            <p className="welcome-text">{t("common.welcome", "Welcome")}</p>
            <p className="user-name">{userData.name}</p>
          </div>
        </div>
      )}

      {/* Services section */}
      <div className="mobile-menu-group">
        <h3 className="mobile-menu-group__header">
          {t("common.services", "SERVICES")}
        </h3>
        <div className="mobile-menu-group__content">
          <div className="services-grid">
            {SERVICES_NAV_ITEMS.map((item, index) => {
              const isActive = isServiceActive(item.id);
              return (
                <div
                  key={index}
                  className={`service-item ${isActive ? 'active' : ''}`}
                  onClick={() => {
                  // Handle service navigation
                  switch (item.id) {
                    case "hotels":
                      window.location.href = "/hotel";
                      break;
                    case "flight":
                      window.location.href = "/flights";
                      break;
                    case "visa":
                      window.location.href = "/visa";
                      break;
                    case "cruise":
                      window.location.href = "/cruise";
                      break;
                    default:
                      break;
                  }
                }}
              >
                <div className="service-icon">
                  <Image
                    src={item.icon}
                    alt={item.label}
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                </div>
                <span className="service-label">
                  {t(`header.nav.${item.id === 'hotels' ? 'hotels' : item.id}`, item.label)}
                </span>
              </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Account section */}
      <div className="mobile-menu-group">
        <h3 className="mobile-menu-group__header">
          {t("common.account", "ACCOUNT")}
        </h3>
        <div className="mobile-menu-group__content">
          <ul>
            {profileItems.map((item, index) => {
              const IconComponent = getMenuIcon(item.label);
              return (
                <li
                  className={`menu-item ${
                    activeProfileItem === item.label ? "active" : ""
                  }`}
                  key={index}
                  onClick={() => handleProfileMenuAtMobileClick(item.label)}
                >
                  <IconComponent
                    className={`menu-icon ${isRTL ? "ml-2" : "mr-2"}`}
                    size={20}
                  />
                  <span className="menu-label">
                    {t(`profile.${item.label.toLowerCase()}`, item.label)}
                  </span>
                </li>
              );
            })}
          </ul>
        </div>
      </div>

      {/* Settings section */}
      {/* <div className="mobile-menu-group">
        <h3 className="mobile-menu-group__header">
          {t("common.settings", "Settings")}
        </h3>
        <div className="mobile-menu-group__content">
          <ul>
            {settingsItems.map((item, index) => (
              <>
                <li
                  className="menu-item"
                  onClick={() => handleProfileMenuAtMobileClick(item.label)}
                  key={index}
                >
                  <span
                    className={`menu-icon fa-solid ${item.icon} ${
                      isRTL ? "ml-2" : "mr-2"
                    }`}
                  ></span>
                  <span className="menu-label">
                    {t(`settings.${item.label.toLowerCase()}`, item.label)}
                  </span>
                </li>
              </>
            ))}
          </ul>

    
        </div>
      </div> */}

            <div className="menu-item-type2-wrapper ">

            <div
              className="menu-item-type2"
              onClick={() => setIsLanguageModalOpen(true)}
            >
              <Globe
                className={`menu-icon ${isRTL ? "ml-2" : "mr-2"}`}
                size={20}
              />
              <span className="menu-label">Language</span>
            </div>


            <div
              className="menu-item-type2"
              onClick={() => setIsPriceModalOpen(true)}
            >
              <DollarSign
                className={`menu-icon ${isRTL ? "ml-2" : "mr-2"}`}
                size={20}
              />
              <span className="menu-label">Price Display</span>
            </div>
          </div>
    </div>
  );
};

// Modal Components rendered outside of mobile-menu-content
export const MobileMenuModals: React.FC<{
  isLanguageModalOpen: boolean;
  isPriceModalOpen: boolean;
  setIsLanguageModalOpen: (open: boolean) => void;
  setIsPriceModalOpen: (open: boolean) => void;
  selectedCurrency: CurrencyItem;
  setSelectedCurrency: (currency: CurrencyItem) => void;
  currentLanguage: string;
  changeLanguage: (code: string) => void;
  currencyList: CurrencyItem[];
}> = ({
  isLanguageModalOpen,
  isPriceModalOpen,
  setIsLanguageModalOpen,
  setIsPriceModalOpen,
  selectedCurrency,
  setSelectedCurrency,
  currentLanguage,
  changeLanguage,
  currencyList,
}) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted || typeof window === 'undefined') {
    return null;
  }

  return createPortal(
    <>
      {/* Language Modal */}
      {isLanguageModalOpen && (
        <div className="mobile-menu-modal-overlay" onClick={() => setIsLanguageModalOpen(false)}>
          <div className="mobile-menu-modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="mobile-menu-modal-header">
              <h3>Select Language</h3>
              <button
                className="mobile-menu-modal-close"
                onClick={() => setIsLanguageModalOpen(false)}
              >
                ×
              </button>
            </div>
            <div className="mobile-menu-modal-body">
              {Object.entries(languageMap)
                .filter(([code]) => ['en', 'ar', 'es', 'fr', 'hi'].includes(code))
                .map(([code, name]) => (
                  <button
                    key={code}
                    onClick={() => {
                      changeLanguage(code);
                      setIsLanguageModalOpen(false);
                    }}
                    className={`mobile-menu-language-option ${currentLanguage === code ? 'active' : ''}`}
                  >
                    <div className="mobile-menu-flag-container">
                      <Image
                        src={languageFlags[code]}
                        alt={`${name} flag`}
                        width={24}
                        height={16}
                        className="mobile-menu-flag-image"
                      />
                    </div>
                    <span>{name}</span>
                  </button>
                ))}
            </div>
          </div>
        </div>
      )}

      {/* Price Display Modal */}
      {isPriceModalOpen && (
        <div className="mobile-menu-modal-overlay" onClick={() => setIsPriceModalOpen(false)}>
          <div className="mobile-menu-modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="mobile-menu-modal-header">
              <h3>Select Currency</h3>
              <button
                className="mobile-menu-modal-close"
                onClick={() => setIsPriceModalOpen(false)}
              >
                ×
              </button>
            </div>
            <div className="mobile-menu-modal-body">
              <div className="mobile-menu-currency-grid">
                {currencyList.map((currency, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setSelectedCurrency(currency);
                      setIsPriceModalOpen(false);
                    }}
                    className={`mobile-menu-currency-option ${
                      selectedCurrency.to_currency_code === currency.to_currency_code ? "active" : ""
                    }`}
                  >
                    <span className="mobile-menu-currency-code">{currency.to_currency_code}</span>
                    <span className="mobile-menu-currency-symbol">{currency.currency_symbol}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </>,
    document.body
  );
};

export default MobileMenuContent;
