"use client";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
  Dispatch,
  SetStateAction,
  useCallback,
} from "react";
import "./RoomBookingComponent.scss";
import OverviewComponent from "./OverviewComponent/OverviewComponent";
import ReviewsAndRatings from "./ReviewsAndRatings/ReviewsAndRatings";
import HotelFacilities from "./HotelFacilities/HotelFacilities";
import HotelPolicies from "./HotelPolicies/HotelPolicies";
import { useTranslation } from "@/app/hooks/useTranslation";
import { useCommonContext } from "@/app/contexts/commonContext";
import { Link } from "lucide-react";
import DateRangeDisplay from "./date-range-display/DateRangeDisplay";
import { showToast } from "@/app/components/utilities/SonnerToasterCustom";
import { usePathname, useRouter } from "next/navigation";
import HotelOverviewComponent from "../HotelOverviewComponent/HotelOverviewComponent";
import GallerySlider from "./GallerySlider/GallerySlider";
import { Images } from "../../hotel-detail-api-response.model";
import RoomRatesDisplay from "./RoomRatesDisplay/RoomRatesDisplay";
import RoomRatesDisplayShimmer from "./RoomRatesDisplay/components/RoomRatesDisplayShimmer";
import { TransformedRoomsData } from "@/models/hotel/rooms-and-rates.model";

type RoomBookingComponentProps = {
  setIsMobileFacilitiesActive: Dispatch<SetStateAction<boolean>>;
  images: Images[];
  transformedRoomsData: TransformedRoomsData;
  isLoading: boolean;
};

const RoomBookingComponent = forwardRef(
  ({ setIsMobileFacilitiesActive, transformedRoomsData , isLoading }: RoomBookingComponentProps, ref) => {
    const { t } = useTranslation();
    const currentRoute = usePathname();
    const [activeTab, setActiveTab] = useState<string>(
      t("hotel.detail.navigation.overview")
    );
    const [isSticky, setIsSticky] = useState<boolean>(false);
    const [isMobileHeaderSticky, setIsMobileHeaderSticky] = useState<boolean>(false);
    const [windowWidth, setWindowWidth] = useState<number>(
      typeof window !== "undefined" ? window.innerWidth : 0
    );
    // Track if we're in mobile view for responsive rendering
    const [isMobileView, setIsMobileView] = useState<boolean>(
      typeof window !== "undefined" ? window.innerWidth <= 950 : false
    );

    // Reference for the element BEFORE the navigation tabs
    const headerRef = useRef<HTMLDivElement>(null);
    const navRef = useRef<HTMLDivElement>(null);
    const hotelInfoHeaderRef = useRef<HTMLDivElement>(null);
    const {
      hotelDetailsResponse,
      setHotelSearchFormData,
      hotelSearchFormData,
    } = useCommonContext();
    const router = useRouter();

    // Create individual refs at the top level
    const overviewRef = useRef<HTMLDivElement>(null);
    const roomsRef = useRef<HTMLDivElement>(null);
    const locationRef = useRef<HTMLDivElement>(null);
    const reviewsRef = useRef<HTMLDivElement>(null);
    const facilitiesRef = useRef<HTMLDivElement>(null);
    const policiesRef = useRef<HTMLDivElement>(null);

    // Get translated tab names
    const roomsTabName = t("hotel.detail.navigation.rooms");
    const overviewTabName = t("hotel.detail.navigation.overview");
    const locationTabName = t("hotel.detail.navigation.location");
    const reviewsTabName = t("hotel.detail.navigation.reviews");
    const facilitiesTabName = t("hotel.detail.navigation.facilities");
    const policiesTabName = t("hotel.detail.navigation.policies");

    const navigationTabs = [
      overviewTabName,
      roomsTabName,
      locationTabName,
      reviewsTabName,
      facilitiesTabName,
      policiesTabName,
    ];

    // Then memoize the object that contains them
    const sectionRefs = useMemo(() => {
      return {
        [roomsTabName]: roomsRef,
        [overviewTabName]: overviewRef,
        [locationTabName]: locationRef,
        [reviewsTabName]: reviewsRef,
        [facilitiesTabName]: facilitiesRef,
        [policiesTabName]: policiesRef,
      };
    }, [
      roomsTabName,
      overviewTabName,
      locationTabName,
      reviewsTabName,
      facilitiesTabName,
      policiesTabName,
    ]);

    const filteredTabs = useMemo(() => {
      if (isMobileView) {
        // Remove "Rooms" and reorder to move "Facilities" second
        return [
          overviewTabName,
          facilitiesTabName,
          locationTabName,
          reviewsTabName,
          policiesTabName,
        ];
      }

      // Default order for larger screens
      return navigationTabs;
    }, [
      isMobileView,
      navigationTabs,
      overviewTabName,
      facilitiesTabName,
      locationTabName,
      reviewsTabName,
      policiesTabName,
    ]);

    // Helper function to scroll with offset
    const scrollToSectionWithOffset = useCallback(
      (element: HTMLElement, offset: number = 100) => {
        if (!element) return;

        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;

        window.scrollTo({
          behavior: "smooth",
          top: offsetPosition,
        });
      },
      []
    );

    // Calculate dynamic offset based on sticky header height
    const getDynamicOffset = useCallback(() => {
      const navElement = navRef.current;
      if (navElement && isSticky) {
        const navHeight = navElement.offsetHeight;
        const mobileHeaderHeight = isMobileView && isMobileHeaderSticky ? 60 : 0;
        return navHeight + mobileHeaderHeight + (isMobileView ? 20 : 40); // Different padding for mobile/desktop
      }
      return isMobileView ? 20 : 30; // Fallback offset
    }, [isSticky, isMobileView, isMobileHeaderSticky]);

    const getHotelSearchFormData = useCallback(() => {
      const data = localStorage.getItem("hotelSearchFormData");
      if (data !== null) {
        setHotelSearchFormData(JSON.parse(data));
      }
    }, [setHotelSearchFormData]);

    useEffect(() => {
      getHotelSearchFormData();
    }, [getHotelSearchFormData]);

    // This is the function that will be exposed through the ref
    const scrollToRoomCard = useCallback(() => {
      if (roomsRef.current) {
        const offset = getDynamicOffset();
        scrollToSectionWithOffset(roomsRef.current, offset);
      }
    }, [getDynamicOffset, scrollToSectionWithOffset]);

    // Expose the scrollToRoomCard function through the ref
    useImperativeHandle(
      ref,
      () => ({
        scrollToRoomCard,
      }),
      [scrollToRoomCard]
    );

      


    useEffect(() => {
      const handleResize = () => {
        const newWidth = window.innerWidth;
        setWindowWidth(newWidth);
        setIsMobileView(newWidth <= 950);
      };

      // Set initial values
      handleResize();

      // Add event listener for window resize
      window.addEventListener("resize", handleResize);

      // Clean up
      return () => window.removeEventListener("resize", handleResize);
    }, []);

    // Scroll event handler for sticky navigation
    useEffect(() => {
      const handleScroll = () => {
        if (!headerRef.current) return;

        // Get the bottom position of the header element
        const headerBottom = headerRef.current.getBoundingClientRect().bottom;

        if (isMobileView) {
          // For mobile: mobile header becomes sticky first, then navigation tabs
          setIsMobileHeaderSticky(headerBottom <= 0);
          setIsSticky(headerBottom <= -60); // Navigation tabs sticky after mobile header
        } else {
          // For desktop: keep existing logic
          setIsSticky(headerBottom <= 40);
        }
      };

      window.addEventListener("scroll", handleScroll);
      return () => {
        window.removeEventListener("scroll", handleScroll);
      };
    }, [isMobileView]);

    useEffect(() => {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            const { target, isIntersecting } = entry;

            if (!isIntersecting) return;

            // ✅ Special handling for Facilities tab
            if (
              isMobileView &&
              target === sectionRefs[overviewTabName].current
            ) {
              setActiveTab(facilitiesTabName);
            } else if (
              !isMobileView &&
              target === sectionRefs[facilitiesTabName].current
            ) {
              setActiveTab(facilitiesTabName);
            }
            // ✅ Set Overview tab active when hotel-info-header is visible
            else if (target === hotelInfoHeaderRef?.current) {
              setActiveTab(overviewTabName);
            } else {
              const section = Object.keys(sectionRefs).find(
                (key) =>
                  sectionRefs[key as keyof typeof sectionRefs].current ===
                  target
              );

              if (section && section !== facilitiesTabName) {
                setActiveTab(section);
              }
            }
          });
        },
        { threshold: 0.3 }
      );

      // Observe all current sections + hotel info header
      [...Object.values(sectionRefs), hotelInfoHeaderRef].forEach((ref) => {
        if (ref?.current) observer.observe(ref.current);
      });

      return () => {
        [...Object.values(sectionRefs), hotelInfoHeaderRef].forEach((ref) => {
          if (ref?.current) observer.unobserve(ref.current);
        });
      };
    }, [sectionRefs, facilitiesTabName, overviewTabName, isMobileView]);

    const scrollToSection = useCallback(
      (tab: string) => {
        setActiveTab(tab);

        const offset = getDynamicOffset();

        if (tab === overviewTabName) {
          if (hotelInfoHeaderRef.current) {
            scrollToSectionWithOffset(hotelInfoHeaderRef.current, offset);
          }
          return;
        }

        if (tab === facilitiesTabName && isMobileView) {
          if (sectionRefs[overviewTabName].current) {
            scrollToSectionWithOffset(
              sectionRefs[overviewTabName].current,
              offset
            );
          }
        } else {
          const targetRef = sectionRefs[tab as keyof typeof sectionRefs];
          if (targetRef?.current) {
            scrollToSectionWithOffset(targetRef.current, offset);
          }
        }
      },
      [
        getDynamicOffset,
        scrollToSectionWithOffset,
        overviewTabName,
        facilitiesTabName,
        isMobileView,
        sectionRefs,
      ]
    );

    const scrollToFacilities = useCallback(() => {
      setActiveTab(facilitiesTabName);
      const offset = getDynamicOffset();

      if (isMobileView) {
        if (sectionRefs[overviewTabName].current) {
          scrollToSectionWithOffset(
            sectionRefs[overviewTabName].current,
            offset
          );
        }
      } else {
        if (sectionRefs[facilitiesTabName].current) {
          scrollToSectionWithOffset(
            sectionRefs[facilitiesTabName].current,
            offset
          );
        }
      }
    }, [
      facilitiesTabName,
      getDynamicOffset,
      scrollToSectionWithOffset,
      isMobileView,
      sectionRefs,
      overviewTabName,
    ]);

    const handlaSave = useCallback(() => {
      showToast("Successfully added to wishlist!", "default", "top-right");
    }, []);

    const shareHotel = (hotel: { name: string; url: string }) => {
      if (navigator.share) {
        navigator
          .share({
            title: `Check out this hotel: ${hotel.name}`,
            text: `Found this great hotel: ${hotel.name}`,
            url: hotel.url,
          })
          .then(() => {})
          .catch(() => {});
      } else {
        // Fallback for unsupported browsers
        navigator.clipboard.writeText(hotel.url);
        alert("Link copied to clipboard!");
      }
    };

    const handleReserveClick = () => {
      // Now we're directly calling the scrollToRoomCard function
      if (isMobileView) {
        router.push("/RoomSelection");
      } else {
        scrollToRoomCard();
      }
    };

    const handleBookingClick = () => {
      router.push("/RoomSelection");
    };

   

    return (
      <div className="room-booking-component">
        {/* This div serves as a reference point for when to make navigation sticky */}
        <div ref={headerRef} className="common-container">
          <div className="route-path">
            <span>Home</span> <i className="fa-solid fa-greater-than"></i>
            <span>Hotels</span> <i className="fa-solid fa-greater-than"></i>
            {/* <span>All resorts</span>{" "} */}
            {/* <i className="fa-solid fa-greater-than"></i> */}
            {/* <span>India</span> <i className="fa-solid fa-greater-than"></i>
            <span>Goa</span> <i className="fa-solid fa-greater-than"></i> */}
            <span>{hotelSearchFormData?.searchQuery || "..."}</span>{" "}
            {/* <i className="fa-solid fa-greater-than"></i> */}
            <i className="fa-solid fa-greater-than"></i> Detail
          </div>
        </div>

        {/* Mobile Header - Only show on mobile */}
        {isMobileView && (
          <div className={`mobile-header ${isMobileHeaderSticky ? "sticky" : ""}`}>
            <div className="mobile-header-content">
              <button
                className="back-button"
                onClick={() => router.push('/HotelSearchResult')}
              >
                <i className="fa-solid fa-arrow-left"></i>
              </button>
              <div className="mobile-header-info">
                <div className="hotel-name-with-rating">
                  <h3 className="hotel-name">{hotelDetailsResponse?.name || "..."}</h3>
                  <div className="rating-stars">
                    {Array.from({
                      length: hotelDetailsResponse?.starRating ?? 0,
                    }).map((_, index) => (
                      <i key={index} className="fa-solid fa-star"></i>
                    ))}
                  </div>
                </div>
                <div className="mobile-date-range">
                  <DateRangeDisplay
                    checkInDate={hotelSearchFormData?.checkInDate ?? ""}
                    checkOutDate={hotelSearchFormData?.checkOutDate ?? ""}
                    compact={true}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="navigation-tabs-container-bg"></div>

        <div
          ref={navRef}
          className={`navigation-tabs-container ${isSticky ? "sticky" : ""} ${isMobileView && isMobileHeaderSticky ? "mobile-with-header" : ""}`}
        >
          <div className="navigation-tabs common-container ">
            {filteredTabs.map((label, index) => (
              <div
                className={`navigation-tab ${
                  activeTab === label ? "active" : ""
                }`}
                key={index}
                onClick={() => scrollToSection(label)}
              >
                {label}
              </div>
            ))}
          </div>
        </div>

        <div className="common-container">
          {/* Complex header - Only show on desktop */}
          {!isMobileView && (
            <div ref={hotelInfoHeaderRef} className="hotel-info-header ">
              <div className="info-header">
                <div className="heading">
                  <h2>{hotelDetailsResponse?.name || "..."}</h2>
                  <div className="rating">
                    {Array.from({
                      length: hotelDetailsResponse?.starRating ?? 0,
                    }).map((_, index) => (
                      <i key={index} className="fa-solid fa-star"></i>
                    ))}
                  </div>
                </div>

                <div className="buttons">
                  <div className="hotel-info-header-btn" onClick={handlaSave}>
                    {t("hotel.detail.save")} <i className="fa-solid fa-heart"></i>
                  </div>

                  <div
                    className="hotel-info-header-btn"
                    onClick={() =>
                      shareHotel({
                        name: hotelDetailsResponse?.name || "Hotel",
                        url: `${currentRoute}`,
                      })
                    }
                  >
                    {t("hotel.detail.share")}
                    <i className="fa-solid fa-share-nodes"></i>
                  </div>

                  <div
                    className="hotel-info-header-reserveBtn"
                    onClick={handleReserveClick}
                    style={{ marginLeft: "10px" }}
                  >
                    <i className="fa-solid fa-share-from-square"></i>
                    {t("hotel.detail.reserve")}
                  </div>
                </div>
              </div>

              <div className="review-location">
                <div className="review">
                  <div className="rating">
                    {hotelDetailsResponse?.userRating ?? "0"}
                  </div>

                  <div className="rating-detail">
                    <p className="detail detail1">
                      {hotelDetailsResponse?.userRatingCategoty}
                    </p>
                    <p className="detail detail2">
                      {hotelDetailsResponse?.reviews?.length || 0}{" "}
                      {t("hotel.detail.ratings")}
                    </p>
                  </div>
                </div>
                <div className="location">
                  <div className="icon">
                    <i className="fa-solid fa-location-dot"></i>
                  </div>
                  <div className="details">
                    <div className="detail detail1">
                      {hotelDetailsResponse?.address}
                    </div>

                    <Link href={""}>
                      {" "}
                      <div className="detail detail2">
                        {t("hotel.detail.viewOnMap")}
                      </div>
                    </Link>
                  </div>
                </div>
                <DateRangeDisplay
                  checkInDate={hotelSearchFormData?.checkInDate ?? ""}
                  checkOutDate={hotelSearchFormData?.checkOutDate ?? ""}
                />
              </div>
            </div>
          )}

          {/* Mobile simple header reference for scroll detection */}
          {isMobileView && (
            <div ref={hotelInfoHeaderRef} className="mobile-header-spacer"></div>
          )}

          {/* Dynamic rendering of components based on screen width */}
          {isMobileView ? (
            <GallerySlider
              images={hotelDetailsResponse?.images || []}
            />
          ) : (
            <HotelOverviewComponent images={hotelDetailsResponse?.images || []} hotelData={hotelDetailsResponse} />
          )}

          {/* <div className="searchbar-container-list">
            <HotelSearchBar isModify={true} />
          </div> */}

          {!isMobileView && (
            <div ref={roomsRef}>
              {isLoading ? (
                <RoomRatesDisplayShimmer />
              ):(
                <RoomRatesDisplay transformedRoomsData={ transformedRoomsData } />
              )}
              
            </div>
            
          )}

          <div ref={overviewRef}>
            <OverviewComponent
              scrollToFacilities={scrollToFacilities}
              setIsMobileFacilitiesActive={setIsMobileFacilitiesActive}
              address={hotelDetailsResponse?.address}
              facilities={hotelDetailsResponse?.facilities}
              amenities={hotelDetailsResponse?.amenities}
              hotelData={hotelDetailsResponse}
            />
          </div>

          {/* <div ref={locationRef}>
            <ExploreMap hotelData={hotelDetailsResponse} />
          </div> */}

          <div ref={reviewsRef}>
            <ReviewsAndRatings
              ratingView={(hotelDetailsResponse as any)?.ratingView}
              hotelData={hotelDetailsResponse}
              reviews={hotelDetailsResponse?.reviews}
            />
          </div>

          <div className="hotel-facilites-container">
            <HotelFacilities
              ref={facilitiesRef}
              hotelData={hotelDetailsResponse}
              backendFacilities={hotelDetailsResponse?.facilities}
              amenities={hotelDetailsResponse?.amenities}
            />
          </div>

          <div ref={policiesRef}>
            <HotelPolicies
              hotelData={hotelDetailsResponse}
              policies={hotelDetailsResponse?.policies}
            />
          </div>
        </div>
        {
          isMobileView && (   <div className="booking-section-bottom-0">
          {/* <div className="totalAmtLabelValue">
            <p className="value">
              ₹
              {
                Number(hotelDetailsResponse?.fareDetail?.displayedBaseFare ?? 0)
                  .toFixed(2)
                  .split(".")[0]
              }
              <span className="xs-text">
                .
                {
                  Number(
                    hotelDetailsResponse?.fareDetail?.displayedBaseFare ?? 0
                  )
                    .toFixed(2)
                    .split(".")[1]
                }
              </span>
            </p>

            <span className="label">Total amount</span>
          </div> */}
          <button onClick={handleBookingClick} className="bookingBtn">
            {" "}
            <i className="fa-solid fa-share-from-square"></i> Reserve
          </button>
        </div>)
        }
     
      </div>
    );
  }
);

RoomBookingComponent.displayName = "RoomBookingComponent";

export default RoomBookingComponent;
