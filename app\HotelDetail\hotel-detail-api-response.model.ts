// Interface for the actual hotel detail API response structure
export interface HotelDetailApiResponse {
  id: number
  hotel_id: string
  hotelId : string
  name: string
  city: string
  country: string
  userRating: string | null
  userRatingCategoty: string | null // Note: API has typo "Categoty"
  address: string
  stateName: string | null
  roomDetails: RoomDetails
  comfortRating: string | null
  geoLocationInfo: GeoLocationInfo
  about: string | null
  HotelType: string
  starRating: number | null
  category: string | null
  amenities: string[]
  attributes: Attribute[]
  roomCountLeft: number | null
  isVisible: boolean
  chain_code: string
  chain_name: string | null
  data_source_provider: string
  data_source_updated_at: string | null
  provider_id: string
  provider_name: string
  provider_hotel_id: string
  hero_image: string | null
  image_count: number | null
  language: string | null
  relevance_score: number | null
  phones_json: string[]
  faxes_json: string[] | null
  attributes_json: AttributeJson[]
  available_suppliers: string[] | null
  descriptions: Description[]
  facilities: Facility[]
  reviews: Review[]
  policies: Policy[]
  images: Images[]
  rooms: Room[]
  additional_information: any[]
  recent_pricing: any[]
}

export interface RoomDetails {
  type: string | null
  bedroom: number | null
  livingRoom: number | null
  bathroom: number | null
  size: string | null
  bed: string | null
}

export interface GeoLocationInfo {
  lat: number
  lon: number
}

export interface Attribute {
  id: number
  attribute_key: string
  attribute_value: string
  attribute_category: string
}

export interface AttributeJson {
  key: string
  value: string
}

export interface Description {
  id: number
  description_type: string
  content: DescriptionContent
  language: string
}

export interface DescriptionContent {
  title: string
  description: string
  highlights?: string[]
  amenities?: string[]
  nearby?: string[]
}

export interface Facility {
  id: number
  name: string
  details: FacilityDetail[]
}

export interface FacilityDetail {
  id: number
  content: string
}

export interface Review {
  id: number
  rating: number
  content: string
  type: string
}

export interface Policy {
  id: number
  name: string
  description: string
}

export interface Images {
  id: number
  image_path: string
  alt_text: string
  image_category_type: string
  is_hero_image: boolean
  image_width: number | null
  image_height: number | null
  sort_order: number
}

export interface Room {
  // Define room structure when available
  [key: string]: any
}
