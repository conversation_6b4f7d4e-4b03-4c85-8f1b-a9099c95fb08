@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

// Body scroll management
body.popup-open {
  overflow: hidden;
}

// Main container
.bottom-popup-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1200;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s ease;

  &.visible {
    opacity: 1;
    pointer-events: auto;
  }

  &.show {
    opacity: 1;
    pointer-events: auto;
  }

  &.hide {
    opacity: 0;
    pointer-events: none;
  }
}

// Overlay
.dy-bootom-up-popup-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

// Main popup div
.dy-bootom-up-popup-div {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  max-height: 98vh; // Limit height to 98% of viewport, leaving 2% gap at top
  background: white;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), height 0.3s ease;

  .bottom-popup-container.show & {
    transform: translateY(0);
  }

  .bottom-popup-container.hide & {
    transform: translateY(100%);
  }

  &.dragging {
    transition: none;
  }
}

// Draggable header with heading and close button
.header-menu-div {
  flex-shrink: 0;
  padding: 16px 20px;
  background: var(--primary-color);
  color: white;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  cursor: grab;
  user-select: none;
  position: relative;

  &:active {
    cursor: grabbing;
  }

  // Left section with drag indicator and heading
  .drag-indicator {
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 4px;
    background: var(--border-color2);
    border-radius: 2px;
    transition: background 0.2s ease;
  }

  &:hover .drag-indicator {
    background: var(--black-color2);
  }

  // Popup heading
  .popup-heading {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-text-color);
    flex: 1;
    text-align: center;
    padding-top: 8px; // Account for drag indicator
  }

  // Close button
  .close-button {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
    color: var(--black-color2);
    font-size: 20px;
    line-height: 1;
    min-width: 32px;
    height: 32px;

    &:hover {
      background: var(--border-color);
      color: var(--black-color);
    }

    &:focus {
      outline: 2px solid var(--primary-color);
      outline-offset: 2px;
    }

    .close-icon {
      font-size: 24px;
      font-weight: 300;
    }
  }
}

// Content body
.body-div {
  flex: 1;
  overflow: hidden; // Remove scroll from popup body - let child components handle scrolling
  // padding: 20px;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Responsive design
@media (max-width: $isMobile) {
  .dy-bootom-up-popup-div {
    border-radius: 12px 12px 0 0;
  }

  .header-menu-div {
    padding: 12px 16px;
    
    .popup-heading {
      font-size: 16px;
    }

    .close-button {
      min-width: 28px;
      height: 28px;
      padding: 6px;

      .close-icon {
        font-size: 20px;
      }
    }
  }

  // .body-div padding removed for mobile
}

@media (max-width: $breakpoint-sm) {
  .dy-bootom-up-popup-div {
    border-radius: 8px 8px 0 0;
  }

  .header-menu-div {
    padding: 10px 14px;
    
    .drag-indicator {
      width: 32px;
      height: 3px;
    }
    
    .popup-heading {
      font-size: 15px;
    }

    .close-button {
      min-width: 24px;
      height: 24px;
      padding: 4px;

      .close-icon {
        font-size: 18px;
      }
    }
  }

  // .body-div padding removed for small screens
}

// Focus and accessibility
.header-menu-div:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

// Animation keyframes
@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .dy-bootom-up-popup-div {
    border: 2px solid var(--black-color);
  }

  .header-menu-div {
    border-bottom: 2px solid var(--black-color);
  }

  .drag-indicator {
    background: var(--black-color);
  }

  .close-button {
    border: 1px solid var(--black-color);
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .dy-bootom-up-popup-div {
    transition: none;
  }

  .bottom-popup-container {
    transition: none;
  }
}

// Custom z-index support is handled via inline styles
