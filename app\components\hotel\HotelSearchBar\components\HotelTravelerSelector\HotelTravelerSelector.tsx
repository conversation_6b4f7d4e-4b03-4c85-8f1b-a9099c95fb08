"use client";
import React, { useState, useEffect } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import EditIcon from '@mui/icons-material/Edit';
import './hotel-travel-selecter.scss';
import { useTranslation } from '@/app/hooks/useTranslation';

// Define TypeScript interfaces
interface ChildAge {
  id: number;
  age: number;
}

interface Room {
  id: number;
  adults: number;
  children: number;
  childrenAges: ChildAge[];
  isOpen: boolean;
}

// // Updated interface with proper callbacks
// interface TravelerSelectorProps {
//   adults: number;
//   childCount: number;
//   // Add a proper room update handler
//   onRoomsChange?: (rooms: Omit<Room, 'isOpen'>[]) => void;
//   // Keep existing handlers for backward compatibility
//   handleAdultChange: (roomId: number, increment: number) => void;
//   handleChildChange: (roomId: number, increment: number) => void;
//   handleClose: () => void;
// }

interface TravelerSelectorProps {
  initialRooms: Omit<Room, 'isOpen'>[]; // Initial rooms passed from parent
  onRoomsChange: (rooms: Omit<Room, 'isOpen'>[]) => void;
  handleClose: () => void;
}


const HotelTravelerSelector: React.FC<TravelerSelectorProps> = ({
  initialRooms,
  onRoomsChange, // New handler for room changes
  handleClose
}) => {
  const { t } = useTranslation();
  // Create default room data when no initial rooms are provided
  const getDefaultRooms = (): Room[] => [
    {
      id: 1,
      adults: 2,
      children: 0,
      childrenAges: [],
      isOpen: true,
    }
  ];

  const [rooms, setRooms] = useState<Room[]>(
    initialRooms && initialRooms.length > 0
      ? initialRooms.map((room, index) => ({
          ...room,
          isOpen: index === 0,
        }))
      : getDefaultRooms()
  );

  // Helper function to notify parent of room changes
  const notifyParentOfChanges = (updatedRooms: Room[]) => {
    const cleanedRooms = updatedRooms.map(({...room }) => room);
    onRoomsChange(cleanedRooms);
  };

  // Notify parent of default rooms when component initializes with no initial data
  useEffect(() => {
    if (!initialRooms || initialRooms.length === 0) {
      // Component was initialized with default rooms, notify parent
      const defaultRooms = getDefaultRooms();
      notifyParentOfChanges(defaultRooms);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run on mount - we want this to run once regardless of prop changes

  // // Update room state when props change
  // useEffect(() => {
  //   setRooms(prevRooms => {
  //     if (prevRooms.length === 1) {
  //       return [
  //         {
  //           ...prevRooms[0],
  //           adults: adults || 2,
  //           children: childCount || 0,
  //           childrenAges: Array.from({ length: childCount || 0 }, (_, i) => {
  //             // Preserve existing ages if possible
  //             const existingChild = prevRooms[0].childrenAges.find(child => child.id === i + 1);
  //             return existingChild || { id: i + 1, age: 5 };
  //           })
  //         }
  //       ];
  //     }
  //     return prevRooms;
  //   });
  // }, [adults, childCount]);

  // Validation function
  const validateRoom = (room: Room): string | null => {
    if (room.adults < 1) {
      return t('search.travelers.validation.min_adults');
    }

    if (room.children > 0 && room.childrenAges.length !== room.children) {
      return t('search.travelers.validation.specify_child_age');
    }

    return null; // No validation errors
  };

  const handleRoomAdultChange = (roomId: number, increment: number): void => {
    const updatedRooms = rooms.map(room =>
      room.id === roomId
        ? { ...room, adults: Math.max(1, room.adults + increment) }
        : room
    );
    setRooms(updatedRooms);
    notifyParentOfChanges(updatedRooms);
  };


  const handleRoomChildChange = (roomId: number, increment: number): void => {
    const updatedRooms = rooms.map(room => {
      if (room.id === roomId) {
        const newChildren = Math.max(0, room.children + increment);
        const newChildrenAges = [...room.childrenAges];

        if (increment > 0) {
          // Add a new child with unique ID
          const maxId = room.childrenAges.length > 0
            ? Math.max(...room.childrenAges.map(child => child.id))
            : 0;
          newChildrenAges.push({ id: maxId + 1, age: 1 });
        } else if (increment < 0 && newChildrenAges.length > 0) {
          // Remove the last child age
          newChildrenAges.pop();
        }

        return { ...room, children: newChildren, childrenAges: newChildrenAges };
      }
      return room;
    });

    setRooms(updatedRooms);
    notifyParentOfChanges(updatedRooms);
  };


  const handleChildAgeChange = (roomId: number, childId: number, age: number): void => {
    const updatedRooms = rooms.map(room => {
      if (room.id === roomId) {
        const newChildrenAges = room.childrenAges.map(child =>
          child.id === childId ? { ...child, age } : child
        );
        return { ...room, childrenAges: newChildrenAges };
      }
      return room;
    });

    setRooms(updatedRooms);
    notifyParentOfChanges(updatedRooms);
  };

  const toggleRoomEditor = (roomId: number): void => {
    setRooms(prevRooms => {
      const currentOpenRoom = prevRooms.find(room => room.isOpen);
      if (currentOpenRoom) {
        const validationError = validateRoom(currentOpenRoom);
        if (validationError && currentOpenRoom.id !== roomId) {
          alert(validationError);
          return prevRooms; // Don't switch if current room is invalid
        }
      }

      return prevRooms.map(room => ({
        ...room,
        isOpen: room.id === roomId,
      }));
    });
  };


  const addRoom = (): void => {
    // First validate current open room
    const currentOpenRoom = rooms.find(room => room.isOpen);
    if (currentOpenRoom) {
      const validationError = validateRoom(currentOpenRoom);
      if (validationError) {
        alert(validationError);
        return;
      }
    }

    // Close all current rooms
    const updatedRooms = rooms.map(room => ({
      ...room,
      isOpen: false
    }));

    // Add a new room
    const newRoomId = Math.max(...rooms.map(room => room.id), 0) + 1;
    const newRooms = [
      ...updatedRooms,
      { id: newRoomId, adults: 1, children: 0, childrenAges: [], isOpen: true }
    ];

    setRooms(newRooms);
    notifyParentOfChanges(newRooms);
  };

  const removeRoom = (roomId: number): void => {
    if (rooms.length === 1) {
      alert(t('search.travelers.validation.min_rooms'));
      return;
    }

    const updatedRooms = rooms.filter(room => room.id !== roomId);
    // If we removed the open room, open the first one
    if (rooms.find(room => room.id === roomId)?.isOpen && updatedRooms.length > 0) {
      updatedRooms[0].isOpen = true;
    }

    setRooms(updatedRooms);
    notifyParentOfChanges(updatedRooms);
  };

  const handleDone = (): void => {
    // Validate all rooms
    for (const room of rooms) {
      const validationError = validateRoom(room);
      if (validationError) {
        toggleRoomEditor(room.id); // Show the room with error
        alert(validationError);
        return;
      }
    }

    // Close all rooms (set isOpen to false)
    const updatedRooms = rooms.map(room => ({
      ...room,
      isOpen: false
    }));

    setRooms(updatedRooms);

    // Send final state to parent
    notifyParentOfChanges(updatedRooms);

    handleClose(); // Close the modal or popup
  };


  // CSS custom properties to apply the global primary color
  const primaryButtonStyle = {
    color: 'var(--primary-color)',
  };

  const primaryBgStyle = {
    backgroundColor: 'var(--primary-color)', // Primary color from styles
    borderColor: 'var(--primary-color)',
  };

  return (
    <div className="w-full border rounded-lg bg-white shadow-sm traveler-selector-container">
      <div className="px-4 py-3 border-b">
        <h2 className="text-base font-medium text-gray-800">{t('search.travelers_header')}</h2>
      </div>

      <div className="divide-y divide-gray-100 rooms-container">
        {rooms.map((room) => (
          <div key={room.id} className={`px-4 py-3 room-container ${room.isOpen ? 'bg-gray-50' : ''}`}>
            {room.isOpen ? (
              // Expanded room editor
              <div>
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-medium text-gray-800">{t('search.travelers.room')} {room.id}</h3>
                  {rooms.length > 1 && (
                    <button
                      onClick={() => removeRoom(room.id)}
                      className="text-gray-400 hover:text-gray-600 p-1"
                      aria-label="Remove room"
                    >
                      <CloseIcon fontSize="small" />
                    </button>
                  )}
                </div>

                {/* Adults selector */}
                <div className="flex justify-between items-center mb-3 traveler-control">
                  <label className="text-sm font-medium text-gray-700 label">{t('search.travelers.adults')}</label>
                  <div className="flex items-center control-buttons">
                    <button
                      onClick={() => handleRoomAdultChange(room.id, -1)}
                      className="w-8 h-8 rounded border flex items-center justify-center text-gray-600 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={room.adults <= 1}
                      aria-label="Decrease adults"
                    >
                      -
                    </button>
                    <span className="w-8 text-center text-gray-800">{room.adults}</span>
                    <button
                      onClick={() => handleRoomAdultChange(room.id, 1)}
                      className="w-8 h-8 rounded border flex items-center justify-center text-gray-600 hover:bg-gray-100 transition-colors"
                      aria-label="Increase adults"
                    >
                      +
                    </button>
                  </div>
                </div>

                {/* Children selector */}
                <div className="flex justify-between items-center mb-3 traveler-control">
                  <div className="label">
                    <div className="text-sm font-medium text-gray-700">{t('search.travelers.children')}</div>
                    <div className="text-xs text-gray-500">{t('search.travelers.children_age_range')}</div>
                  </div>
                  <div className="flex items-center control-buttons">
                    <button
                      onClick={() => handleRoomChildChange(room.id, -1)}
                      className="w-8 h-8 rounded border flex items-center justify-center text-gray-600 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={room.children <= 0}
                      aria-label="Decrease children"
                    >
                      -
                    </button>
                    <span className="w-8 text-center text-gray-800">{room.children}</span>
                    <button
                      onClick={() => handleRoomChildChange(room.id, 1)}
                      className="w-8 h-8 rounded border flex items-center justify-center text-gray-600 hover:bg-gray-100 transition-colors"
                      aria-label="Increase children"
                    >
                      +
                    </button>
                  </div>
                </div>

                {/* Child age selectors */}
                {room.children > 0 && (
                  <div className="mt-3 mb-1">
                    <h4 className="text-xs font-medium text-gray-500 mb-2">{t('search.travelers.child_ages')}</h4>
                    <div className="space-y-2">
                      {room.childrenAges.map((child, index) => (
                        <div key={child.id} className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500 w-16">{t('search.travelers.child')} {index + 1}</span>
                          <div className="relative flex-1">
                            <select
                              value={child.age}
                              onChange={(e) => handleChildAgeChange(room.id, child.id, parseInt(e.target.value))}
                              className="w-full py-2 pl-3 pr-8 text-sm border rounded text-gray-700 appearance-none bg-white focus:outline-none focus:ring-2 focus:border-blue-500"
                              style={{ "--focus-ring-color": "var(--primary-color)" } as React.CSSProperties}
                              aria-label={`${t('search.travelers.child')} ${index + 1} age`}
                            >
                              <option value={0}>{t('search.travelers.under_one_year')}</option>
                              {[...Array(12)].map((_, i) => (
                                <option key={i + 1} value={i + 1}>
                                  {i + 1} {i !== 0 ? t('search.travelers.years') : t('search.travelers.year')}
                                </option>
                              ))}
                            </select>
                            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                              </svg>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              // Collapsed room summary
              <div className="flex justify-between items-center py-1">
                <div>
                  <h3 className="font-medium text-gray-800">{t('search.travelers.room')} {room.id}</h3>
                  <p className="text-sm text-gray-500">
                    {room.adults} {t('search.travelers.adults')}{room.children > 0 ? ` · ${room.children} ${t('search.travelers.children')}` : ''}
                  </p>
                </div>
                <button
                  onClick={() => toggleRoomEditor(room.id)}
                  className="text-gray-400 hover:text-gray-600 p-1"
                  aria-label="Edit room"
                >
                  <EditIcon fontSize="small" />
                </button>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="px-4 py-3 flex justify-between border-t">
        <button
          onClick={addRoom}
          className="flex items-center text-sm font-medium hover:opacity-80 transition-opacity"
          style={primaryButtonStyle}
        >
          + {t('search.travelers.rooms')}
        </button>

        <button
          onClick={handleDone}
          className="text-white text-sm font-medium py-2 px-4 rounded hover:opacity-90 transition-opacity"
          style={primaryBgStyle}
        >
          {t('common.apply')}
        </button>
      </div>
    </div>
  );
};

export default HotelTravelerSelector;