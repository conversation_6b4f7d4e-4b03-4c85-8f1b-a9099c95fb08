.carousel {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 10px;
    
    .image-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
    }
  
    .carousel-image {
      transition: opacity 0.5s ease-in-out;
    }
  
    .arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: white;
      color: #0770e4;
      border-radius: 50%;
      border: none;
      height: 18px;
      width: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 9px;
      cursor: pointer;
      opacity: 0;
      transition: opacity 0.3s;

      
      &.left {
        left: 10px;
      }
      
      &.right {
        right: 10px;
      }
    }
  
    &:hover .arrow {
      opacity: 1;
    }
  
    .dots {
      position: absolute;
      bottom: 10px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 8px;
  
      .dot {
        width: 6px;
        height: 6px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 50%;
        cursor: pointer;
        transition: background 0.3s;
  
        &.active {
          background: white;
        }
      }
    }
  
    .image-count {
      position: absolute;
      bottom: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.6);
      color: white;
      padding: 5px 10px;
      border-radius: 5px;
      font-size: 14px;
    }
  }
  