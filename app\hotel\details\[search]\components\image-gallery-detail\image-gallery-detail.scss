@use "/styles/variable" as *;
@use "/styles/zIndex" as *;
@use "sass:color";

.image-gallery-detail {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: z-index(modal);

  &__container {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 1400px;
    height: 90dvh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  &__header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    justify-content: space-between;
  }

  &__header-center {
    display: flex;
    align-items: center;
  }

  &__go-to-prevpage-btn, &__close-btn {
    background: none;
    border: none;
    color: #1a1a1a;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;

    .fa-arrow-left, .fa-xmark {
      font-size: 1.3rem;
    }
  }

  &__title {
    font-size: 16px;
    font-weight: 600;
    margin-right: 15px;
  }

  &__reserve-btn {
    background-color: $primary-color;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;

    &:hover {
      background-color: color.adjust($primary-color, $lightness: -10%);
    }
  }

  &__content {
    display: flex;
    overflow: hidden;
    height: 100%;
  }
  
  &__showcase-container {
      width: calc(100% - 280px);
      margin-top: 16px;
      height: calc(100% - 16px);
  }

  &__gallery {
    flex: 3;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &__tabs {
    display: flex;
    overflow-x: auto;
    padding-bottom: 16px;
    border-bottom: 1px solid #e0e0e0;
    scrollbar-width: thin;

    &::-webkit-scrollbar {
      height: 4px;
    }
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 2px;
    }
  }

  &__tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 0 0 16px;
    cursor: pointer;

    &-image {
      width: 112px;
      height: 80px;
      overflow: hidden;
      border-radius: 4px;
      border: 2px solid transparent;
      transition: border-color 0.2s ease;

      &.active {
        border-color: $primary-color;
      }
    }

    &-name {
      margin-top: 8px;
      font-size: 14px;
      text-align: center;
      transition: color 0.2s ease;
      font-weight: 500;

      &.active {
        color: $primary-color;
      }
    }
  }

  &__gallery-row {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 20px;
    overflow-y: auto;
    flex-grow: 1;

    &::-webkit-scrollbar {
      width: 8px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb:hover {
      background-color: rgba(0, 0, 0, 0.4);
    }
  }

  &__gallery-item {
    flex: 1 1 calc(25% - 10px);
    min-height: 200px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    border-radius: 4px;

    .image-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  &__sidebar {
    width: 280px;
    flex-shrink: 0;
    border-left: 1px solid #e0e0e0;
    overflow-y: auto;
    background-color: #f8f9fa;
    scrollbar-width: thin;
    
    &::-webkit-scrollbar {
      width: 8px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }
  }

  &__rating {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    gap: 8px;

    .rating-score {
      width: 32px;
      height: 32px;
      background-color: $primary-color;
      color: white;
      border-radius: 6px 6px 6px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 700;
      flex-shrink: 0;
    }

    .rating-label {
      font-weight: 500;
      color: #000;
      font-size: 16px;
    }

    .rating-reviews {
      font-size: 12px;
      color: #595959;
    }
  }

  &__categories {
    padding: 16px;
    h3 {
      margin: 0 0 15px 0;
      font-size: 14px;
      font-weight: 500;
    }

    .category-item {
      margin-bottom: 12px;
    }

    .category-name-score-wrapper {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }

    .category-name {
      display: flex;
      align-items: center;
      font-size: 14px;
      gap: 5px;

      .category-icon {
        color: #008234;
      }
    }

    .category-score {
      font-weight: 500;
      font-size: 14px;
    }

    .category-score-container {
      width: 100%;
      height: 8px;
      background-color: #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
    }

    .category-score-bar {
      height: 100%;
      background-color: $primary-color;
      border-radius: 4px;
      transition: width 0.3s ease;

      &.highscore {
        background-color: #008234;
      }
      &.lowscore {
        background-color: #d4111e;
      }
    }
    
    .score-note-container {
      margin-top: 15px;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
    
    .score-note {
      font-size: 12px;
      color: #666;
      display: flex;
      align-items: center;
      gap: 4px;

      .highscore { color: #008234; }
      .lowscore { color: #d4111e; }
    }
  }
}

// Responsive adjustments
@media (max-width: 992px) {
  .image-gallery-detail {
    &__content {
      flex-direction: column;
    }

    &__sidebar {
      width: 100%;
      border-left: none;
      border-top: 1px solid #e0e0e0;
    }

    &__gallery-item {
      flex-basis: calc(50% - 5px);
    }
  }
}

@media (max-width: 576px) {
  .image-gallery-detail {
    &__header {
      flex-wrap: wrap;
      row-gap: 10px;
      
      &-center {
        order: 2;
      }
      &__close-btn {
        order: 3;
        margin-left: auto;
      }
    }
    
    &__gallery-item {
      flex-basis: 100%;
    }
  }
}