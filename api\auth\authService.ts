import { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { axiosInstanceAuth } from '../axiosInstance';

// Types for Auth API
export interface AuthApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: any;
}

// Types for API response format
export interface InitiateOtpApiResponse {
  status: string;
  message: string;
}

export interface VerifyOtpApiResponse {
  access_token: string;
  token_type: string;
  expires_at: string;
  refresh_token: string;
  user?: User;
  isNewUser?: boolean;
}

export interface User {
  id: string;
  email?: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  // Add other user fields as needed
}

export interface InitiateOtpRequest {
  email?: string;
  phone?: string;
}

export interface VerifyOtpRequest {
  email?: string;
  phone?: string;
  otp: string;
}

export interface InitiateOtpResponse {
  otpSent: boolean;
  message?: string;
}

export interface VerifyOtpResponse {
  user: User;
  token?: string;
  isNewUser: boolean;
  verified: boolean;
}

class AuthService {
  /**
   * Initiate OTP - Send OTP to email or phone
   */
  async initiateOtp(identifier: string): Promise<AuthApiResponse<InitiateOtpResponse>> {
    try {
      // Determine if identifier is email or phone
      const isEmail = identifier.includes('@');

      const payload: InitiateOtpRequest = isEmail
        ? { email: identifier }
        : { phone: identifier };

      const response: AxiosResponse<InitiateOtpApiResponse> =
        await axiosInstanceAuth.post('auth/initiate/otp', payload);

      // Transform your API response to our expected format
      const apiResponse = response.data;

      // Check if the status indicates success (for both login and registration)
      const isSuccess = apiResponse.status === 'login_initiated' ||
                       apiResponse.status === 'registration_initiated' ||
                       apiResponse.status === 'success';

      return {
        success: isSuccess,
        message: apiResponse.message,
        data: isSuccess ? { otpSent: true } : undefined
      };
    } catch (error: unknown) {
      return this.handleError(error);
    }
  }

  /**
   * Verify OTP - Verify the OTP sent to email or phone
   */
  async verifyOtp(identifier: string, otp: string): Promise<AuthApiResponse<VerifyOtpResponse>> {
    try {
      // Determine if identifier is email or phone
      const isEmail = identifier.includes('@');

      const payload: VerifyOtpRequest = isEmail
        ? { email: identifier, otp }
        : { phone: identifier, otp };

      const response: AxiosResponse<VerifyOtpApiResponse> =
        await axiosInstanceAuth.post('auth/verify-otp', payload);

      // Transform your API response to our expected format
      const apiResponse = response.data;

      // If we get access_token, it means verification was successful
      if (apiResponse.access_token) {
        console.log('✅ OTP verification successful, received tokens');

        // Store both access and refresh tokens
        // localStorage.setItem('authToken', apiResponse.access_token);
        // localStorage.setItem('refreshToken', apiResponse.refresh_token);
        // localStorage.setItem('tokenExpiresAt', apiResponse.expires_at);

        // Extract user info from JWT token (basic extraction)
        const userInfo = this.extractUserFromToken(apiResponse.access_token);

        // For now, assume existing user since we got tokens
        // You can adjust this logic based on your business rules
        const isNewUser = false; // Adjust based on your API logic

        return {
          success: true,
          message: 'OTP verified successfully',
          data: {
            user: userInfo || {
              id: '',
              email: isEmail ? identifier : undefined,
              phone: !isEmail ? identifier : undefined,
              firstName: '',
              lastName: ''
            },
            token: apiResponse.access_token,
            isNewUser: isNewUser,
            verified: true
          }
        };
      } else {
        console.log('❌ OTP verification failed - no access token received');
        return {
          success: false,
          message: 'OTP verification failed'
        };
      }
    } catch (error: unknown) {
      return this.handleError(error);
    }
  }

  /**
   * Register new user (if needed for your flow)
   */
  async registerUser(userData: {
    email?: string;
    phone?: string;
    firstName: string;
    lastName?: string;
  }): Promise<AuthApiResponse<User>> {
    try {
      const response: AxiosResponse<AuthApiResponse<User>> = 
        await axiosInstanceAuth.post('auth/register', userData);

      // If registration successful and token is provided, store it
      if (response.data.success && response.data.data) {
        // Store user data in localStorage (following your existing pattern)
        const userDataForStorage = {
          first_name: userData.firstName,
          second_name: userData.lastName || '',
          email: userData.email || '',
          phone: userData.phone || ''
        };
        localStorage.setItem('userData', JSON.stringify(userDataForStorage));
      }

      return response.data;
    } catch (error: unknown) {
      return this.handleError(error);
    }
  }

  /**
   * Logout user
   */
  logout(): void {
    // localStorage.removeItem('authToken');
    // localStorage.removeItem('refreshToken');
    // localStorage.removeItem('tokenExpiresAt');
    // localStorage.removeItem('userData');
  }

  /**
   * Get stored auth token
   */
  getAuthToken(): string | null {
    return localStorage.getItem('authToken');
  }

  /**
   * Get stored refresh token
   */
  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(): boolean {
    const expiresAt = localStorage.getItem('tokenExpiresAt');
    if (!expiresAt) return true;

    const expirationTime = new Date(expiresAt).getTime();
    const currentTime = new Date().getTime();

    return currentTime >= expirationTime;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = this.getAuthToken();
    const userData = localStorage.getItem('userData');
    return !!(token && userData);
  }

  /**
   * Get stored user data
   */
  getUserData(): any | null {
    try {
      const userData = localStorage.getItem('userData');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  }

  /**
   * Test API connectivity
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      // Try to make a simple request to test connectivity
      const response = await axiosInstanceAuth.get('/health');
      return {
        success: true,
        message: 'API connection successful'
      };
    } catch (error) {
      console.error('API connection test failed:', error);
      return {
        success: false,
        message: 'API connection failed. Please check if the server is running.'
      };
    }
  }

  /**
   * Extract user information from JWT token
   */
  private extractUserFromToken(token: string): User | null {
    try {
      // Decode JWT token (basic decoding without verification)
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );

      const payload = JSON.parse(jsonPayload);
      console.log('🔍 Extracted token payload:', payload);

      return {
        id: payload.user_id || '',
        email: payload.email || '',
        firstName: payload.first_name || payload.name || '',
        lastName: payload.last_name || '',
        name: payload.name || payload.first_name || ''
      };
    } catch (error) {
      console.error('Error extracting user from token:', error);
      return null;
    }
  }

  /**
   * Handle API errors consistently
   */
  private handleError(error: unknown): AuthApiResponse {
    if (error instanceof AxiosError) {
      const errorResponse = error.response?.data;

      // Log error for debugging
      console.error('Auth API Error:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: errorResponse,
        url: error.config?.url
      });

      // If the API returns a structured error response, use it
      if (errorResponse && typeof errorResponse === 'object') {
        return {
          success: false,
          message: errorResponse.message || errorResponse.detail || 'An error occurred',
          error: errorResponse.error || errorResponse
        };
      }

      // Handle specific HTTP status codes
      const status = error.response?.status;
      if (status === 400) {
        return {
          success: false,
          message: 'Invalid request. Please check your input.',
          error: errorResponse
        };
      } else if (status === 429) {
        return {
          success: false,
          message: 'Too many requests. Please try again later.',
          error: errorResponse
        };
      } else if (status && status >= 500) {
        return {
          success: false,
          message: 'Server error. Please try again later.',
          error: errorResponse
        };
      }

      // Fallback for non-structured errors
      return {
        success: false,
        message: error.message || 'Network error occurred',
        error: error.response?.data
      };
    }

    // Unknown error type
    console.error('Unexpected auth error:', error);
    return {
      success: false,
      message: 'An unexpected error occurred',
      error: error
    };
  }
}

// Export singleton instance
const authService = new AuthService();
export default authService;
