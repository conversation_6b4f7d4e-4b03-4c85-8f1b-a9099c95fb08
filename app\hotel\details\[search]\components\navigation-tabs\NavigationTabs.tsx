import React from 'react';
import './navigation-tabs.scss';

export interface navigationTabs {
  id: string;
  label: string;
}

interface NavigationTabsProps {
  tabs: navigationTabs[];
  activeTabId: string;
  onTabClick: (tabId: string) => void;
  className?: string;
}

const NavigationTabs: React.FC<NavigationTabsProps> = ({
  tabs,
  activeTabId,
  onTabClick,
  className = '',
}) => {
  return (
    <div className={`navigation-tabs-container ${className}`}>
      <div className="navigation-tabs">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            className={`navigation-tab ${activeTabId === tab.id ? 'active' : ''}`}
            onClick={() => onTabClick(tab.id)}
            role="tab"
            aria-selected={activeTabId === tab.id}
            tabIndex={0}
          >
            {tab.label}
          </div>
        ))}
      </div>
    </div>
  );
};

export default NavigationTabs;