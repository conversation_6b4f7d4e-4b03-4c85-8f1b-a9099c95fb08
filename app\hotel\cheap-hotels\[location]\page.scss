@use '/styles/variable' as *;
@use "sass:color";
@use "/styles/zIndex" as *;

/* No Hotels Found Empty State */
.no-hotels-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 4rem 2rem;
  min-height: 400px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  margin: 2rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  @media (max-width: 768px) {
    padding: 3rem 1.5rem;
    min-height: 350px;
    margin: 1rem 0;
  }

  @media (max-width: 480px) {
    padding: 2rem 1rem;
    min-height: 300px;
  }
}

.no-hotels-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 59, 149, 0.2);
  animation: pulse-gentle 2s ease-in-out infinite;

  i {
    font-size: 2rem;
    color: white;
  }

  @media (max-width: 768px) {
    width: 70px;
    height: 70px;
    
    i {
      font-size: 1.75rem;
    }
  }

  @media (max-width: 480px) {
    width: 60px;
    height: 60px;
    
    i {
      font-size: 1.5rem;
    }
  }
}

.no-hotels-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.75rem;
  letter-spacing: -0.025em;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }

  @media (max-width: 480px) {
    font-size: 1.25rem;
  }
}

.no-hotels-description {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  max-width: 500px;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  @media (max-width: 480px) {
    font-size: 0.875rem;
    margin-bottom: 1.25rem;
  }
}

.no-hotels-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;

  @media (max-width: 480px) {
    flex-direction: column;
    width: 100%;
    gap: 0.75rem;
  }
}

.btn-primary,
.btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-width: 140px;
  justify-content: center;

  i {
    font-size: 0.875rem;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }

  @media (max-width: 480px) {
    width: 100%;
    padding: 0.875rem 1.5rem;
  }
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 59, 149, 0.2);

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 59, 149, 0.3);
  }
}

.btn-secondary {
  background: white;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 59, 149, 0.2);
  }
}

/* Gentle pulse animation */
@keyframes pulse-gentle {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 25px rgba(0, 59, 149, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(0, 59, 149, 0.3);
  }
}

/* Loading state for buttons */
.btn-primary:disabled,
.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Focus states for accessibility */
.btn-primary:focus,
.btn-secondary:focus {
  outline: 2px solid var(--secondary-color);
  outline-offset: 2px;
}

.hotel-search-result-container {
  padding: 20px 0;
  overflow: visible;
  /* Ensure proper scrolling behavior for sticky elements */
  height: auto;

  @media (max-width: $isMobile) {
    padding: 0;
  }

  .hotel-search-result {
    // max-width: 1110px;
    // //padding: 0 85px;
    // margin: 0 auto;
    // height: 1800px;
    //padding-top:40px ;

    .route-path {
      display: flex;
      align-items: center;
      gap: 5px;
      flex-wrap: nowrap;
      margin-bottom: 16px;
      padding: 8px 8px 0 8px;

      @media (max-width: $isMobile) {
        display: none;
      }

      font-size: 12px;
      span {
        color: $primary_color;
      }

      .fa-greater-than {
        font-size: 9px;
        margin-bottom: -2px;
      }
    }

    .searchbar-container-list {
      @media (max-width: $isMobile) {
        display: none;
      }
    }

    .filter-card-container {
      width: 100%;
      height: auto;
      display: flex;
      flex-direction: row;
      gap: 16px;
      justify-content: center;
      padding: 0 8px;
      position: relative;
      align-items: flex-start; /* This helps with sticky positioning */
      .filter-map-container {
        height: 100%;
        position: sticky;
        top: -64px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        // position: relative;
        width: 261px;

        .hotel-list-map-container {
          height: 120px;
          width: 100%;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.1);
        }
        .filter {
          width: 261px;
          height: calc(100vh - 90px);
          position: sticky;
          top: 90px;
          z-index: z-index(modal);

            .close-bttn{
              display: none;
            }
          @media (max-width: $isMobile) {
            // position: fixed;
            // left: 0;
            // right: 0;
            // top: 90px;
            // bottom: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            opacity: 0;
            visibility: hidden;
            transform: translateY(100%);
            transition: opacity 0.3s ease, visibility 0.3s ease;
            background-color: #fff;
            max-height: 100vh;
            overflow-y: auto;

            &.active {
              opacity: 1;
              visibility: visible;
              transform: translateY(0);
              z-index: z-index(modal);
            }
          }
        }
        @media screen and (max-width:950px){
          width: 0px;
          // overflow: hidden;
        }
      }
      @media screen and (max-width: 950px) {
        padding: 0px 0 30px 0;
        gap: 0px;
      }

      /* Standardized Scrollbar Styling */
      .filter::-webkit-scrollbar {
        width: 8px; /* Width of the vertical scrollbar */
      }

      .filter::-webkit-scrollbar-track {
        background: #f1f1f1; /* Track background */
        border-radius: 4px;
      }

      .filter::-webkit-scrollbar-thumb {
        background: #888; /* Scrollbar color */
        border-radius: 4px;
      }

      .filter::-webkit-scrollbar-thumb:hover {
        background: #555; /* On hover */
      }

      /* For Firefox */
      .filter {
        scrollbar-width: thin;
        scrollbar-color: #888 #f1f1f1;
      }

      .card {
        width: calc(100% - 280px);
        // padding-left: 16px;
        height: auto;
        display: flex;
        flex-direction: column;
        gap: 16px;

        @media (max-width: $isMobile) {
          width: 98%;
          padding-left: 0;
        }

        .card-head {
          display: flex;
          justify-content: space-between;
          align-items: center;
          //margin-bottom: 8px;
          

          @media (max-width: 570px) {
            flex-direction: column;
            align-items: start;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          h2 {
            font-size: 20px;
            font-weight: 700;
            //margin-bottom: 8px;

            @media (max-width: $breakpoint-sm) {
              font-size: 18px;
            }

            @media (max-width: 570px) {
              margin-bottom: 5px;
            }

            @media (max-width: $breakpoint-xs) {
              font-size: 16px;
            }
          }

          .sort-bar-container {
            width: 80%;
            
          }

          .list-grid-btn-container {
            background-color: #f5f5f5;
            font-size: 14px;
            border: 1px solid $black_color4;
            padding: 3px;
            border-radius: 15px;
            display: flex;
            gap: 4px; // Prevents unwanted shifting
            align-items: center;

            @media (max-width: $isMobile) {
              display: none;
            }

            .toggleBtn {
              padding: 3px 11px;
              border-radius: 13px;
              border: 1px solid transparent; // Prevents shifting when active
              transition: background-color 0.3s ease, border 0.3s ease;

              &:hover {
                background-color: color.adjust(#f5f5f5, $lightness: -5%);
              }

              &.active {
                border: 1px solid rgba(0, 0, 0, 0.5);
                background-color: color.adjust(#f5f5f5, $lightness: -5%);
              }
            }
          }

          .modify-search-bttn {
            display: none;
            color: #fff;
            text-decoration: none;
            padding: 0.4rem 1rem;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 13px;
            font-weight: 500;
            background-color: $secondary-color;
            transition: background-color 0.2s;
            border-radius: 6px;
            cursor: pointer;

            &:hover {
              background-color: color.adjust(
                $secondary-color,
                $lightness: - 10%
              );
            }

            @media (max-width: $isMobile) {
              display: flex;
            }

            @media (max-width: $breakpoint-xs) {
              font-size: 11px;
              padding: 8px 10px;
            }
          }
        }

        .sort-button-container {
          margin-bottom: 3px;
          position: relative;

          @media (max-width: $isMobile) {
            display: none;
          }

          .sort-button {
            width: fit-content;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 600;
            padding: 8px 12px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            background-color: #fff;

            &:hover {
              border-color: $primary_color;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            &.active {
              border-color: $primary_color;
              color: $primary_color;
              background-color: rgba(0, 59, 149, 0.05);
              box-shadow: 0 2px 8px rgba(0, 59, 149, 0.15);
            }

            .sort-icon {
              color: $primary_color;
              flex-shrink: 0;
            }

            .sort-text {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 200px;
            }
          }
        }

        .sort-top-popup {
          position: absolute;
          top: 45px;
          left: 0;
          background: white;
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
          border-radius: 12px;
          border: 1px solid rgba(0, 0, 0, 0.08);
          z-index: z-index(dropdown);
          min-width: 280px;
          max-height: 400px;
          overflow-y: auto;
          animation: fadeInUp 0.2s ease-out;

          .top-picks-filter-container {
            display: flex;
            flex-direction: column;
            padding: 8px 0;

            .sort-filter {
              width: 100%;
              padding: 12px 16px;
              font-size: 14px;
              transition: all 0.2s ease;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: space-between;
              border-radius: 0;

              &:hover {
                background-color: rgba(0, 59, 149, 0.04);
              }

              &.active {
                color: $primary_color;
                background-color: rgba(0, 59, 149, 0.08);
                font-weight: 500;
              }

              .sort-option-text {
                flex: 1;
                font-size: 14px;
                line-height: 1.4;
              }

              .check-icon {
                color: $primary_color;
                flex-shrink: 0;
                margin-left: 8px;
              }

              &:first-child {
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
              }

              &:last-child {
                border-bottom-left-radius: 12px;
                border-bottom-right-radius: 12px;
              }
            }
          }
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(-8px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .sort-top-popup-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          //background-color: rgba(0, 0, 0, 0.3);
          z-index: z-index(dropdown)-1;
          background: transparent;
          cursor: default;
        }

        .hotel-card-container {
          display: flex;
          flex-direction: column;
          width: 100%;

          .card-wrapper {
            width: 100%;
          }
        }

        // Hotel List Container
        .hotel-card-container {
          display: flex;
          flex-direction: column;
          width: 100%;
          gap: 0;
          margin-top: 8px;

          .card-wrapper {
            width: 100%;
          }
        }

        // Hotel Grid Container
        .hotel-grid-container {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
          gap: 20px;
          width: 100%;
          margin-top: 12px;

          @media (max-width: $breakpoint-md) {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
          }

          @media (max-width: $isMobile) {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .grid-wrapper {
            width: 100%;
            height: auto;
            display: flex;
            justify-content: center;
          }
        }
      }
    }
  }
}

.mobile-filter-div {
  display: none;
  position: fixed;
  left: 10px;
  bottom: 75px;
  z-index: z-index(base);
  cursor: pointer;
  background-color: $primary_color;
  color: #fff;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
  animation: rotate-shake 10s ease-in-out infinite;

  @media (max-width: $isMobile) {
    display: flex;
  }
}

@keyframes rotate-shake {
  60% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(1turn);
  }
}

.mobile-sort-btn-menu-wrapper {
  position: relative;
  width: 50px; /* Match width of the filter button */
  height: 50px; /* Match height of the filter button */
}

.overlay-search-bar {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: z-index(overlay);

  &.show {
    opacity: 1;
    pointer-events: auto;
  }
}


.mobile-search-bar {
  z-index: z-index(modal);
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  // bottom: 0;

  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  transform: translateY(-100%);

  &.show {
    transform: translateY(0);
  }

  .close-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border: #f5f5f5;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
  }
    
  
}

.search-page-popup-close-btn {
  position: fixed;
  top: 16px;
  right: 16px;
  z-index: z-index(modal);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.search-page-popup-close-btn.show {
  opacity: 1;
  pointer-events: auto;
}

.search-page-popup-close-btn i {
  font-size: 16px;
  color: #333;
}

.search-page-popup-close-btn:hover {
  background-color: #eaeaea;
}

.search-page-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: z-index(overlay);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s ease;

  &.show {
    opacity: 1;
    pointer-events: auto;
  }
}

.mobile-sort-filter-buttons-container {
  background-color: #fff;
  position: fixed;
  //bottom: 50px;
  //left: 50%;
  //transform: translate(-50%, 0);
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  display: none;
  flex-direction: row;
  //border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
  //padding: 10px 0;

  @media (max-width: $isMobile) {
    display: flex;
  }

  &__button {
    padding: 0 12px;
    height: 40px;
    font-size: 13px;
    text-align: left;
    font-weight: 500;
    cursor: pointer;
    border-right: 1px solid rgba(0, 0, 0, 0.2);
    transition: background-color 0.2s ease;
    white-space: nowrap;
    color: $primary-color;
    width: calc(100% / 3);
    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: 5px;

    &:last-child {
      border-right: none;
    }

    &:hover {
      background-color: color.adjust(#fff, $lightness: -5%);
    }
  }
}

.mobile-sort-filter-container {
  position: fixed;
  left: 0;
  right: 0;
  top: 90px;
  bottom: 0;
  width: 100%;
  height: 100dvh;
  display: flex;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transform: translateY(100%);
  transition: opacity 0.3s ease, visibility 0.3s ease;
  background-color: #fff;

  &.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    z-index: z-index(modal);
  }
}

.mobile-map-view {
  position: fixed;
  left: 0;
  right: 0;
  top: 90px;
  bottom: 0;
  width: 100%;
  height: 100dvh;
  display: flex;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  transform: translateY(100%);
  transition: opacity 0.3s ease, visibility 0.3s ease;
  background-color: #fff;
  z-index: z-index(modal);

  &.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  &__header {
    position: relative;
    height: 50px;
    width: 100%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
    }

    &__close-bttn {
      position: absolute;
      left: 10px;
      height: 30px;
      width: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      border-radius: 50%;
      border: 1px solid $primary-color;
      color: $primary-color;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: color.adjust(#fff, $lightness: -5%);
      }
    }
  }

  &__content {
    flex: 1;
    overflow: hidden;
    padding: 0;

    .hotel-search-map {
      height: 100%;
      margin-bottom: 0;
      border-radius: 0;
      box-shadow: none;

      .map-container {
        height: 100%;
      }
    }
  }
}

  .mobile-sort-filter_header {
    position: fixed;
    height: 50px;
    width: 100%;
    background-color: #fff;
    top: 0;
    z-index: z-index(base);
    display: none;
    align-items: center;
    justify-content: center;
    padding: 25px 10px;
    text-align: center;

    @media (max-width: $breakpoint-md) {
      display: flex;

    }

    &__close-bttn {
      position: absolute;
      left: 10px;
      height: 30px;
      width: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      border-radius: 50%;
      border: 1px solid $primary-color;
      color: $primary-color;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: color.adjust(#fff, $lightness: -5%);
      }
    }

    .mobile-filter-head {
      font-weight: 600;
      color: black;
    }
  }

  .mobile-sort-filter-content {
    height: 100%;
    width: 100%;
    border: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    // position: fixed;
    // top: 51px;

    .mobile-filter-head {
      padding: 8px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      h3 {
        font-size: 16px;
        font-weight: 700;
      }

      .resetBtn {
        font-size: 12px;
        color: rgb(239.4, 233, 227.6);
        font-weight: 600;
        cursor: not-allowed;
        transition: color 0.2s ease-in-out;

        &.enabled {
          color: $primary-color;
          cursor: pointer;
        }
      }
    }

    &__navbar {
      width: 100%;
      display: flex;
      flex-direction: row;
      margin-bottom: 5px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.2);

      .nav-item {
        width: 50%;
        text-align: center;
        font-size: 14px;
        font-weight: 600;
        color: #000;
        padding: 10px 0;
        border-bottom: 4px solid transparent;
        transition: color 0.3s ease-in-out, border-bottom 0.3s ease-in-out;

        &.active {
          color: $primary-color;
          border-color: $primary-color;
        }

        &:hover {
          color: color.adjust($primary-color, $lightness: -15%);
        }
      }
    }

    &__content {
      display: flex;
      flex-direction: column;
      width: 100%;

      .mobile-sort-wrapper {
        max-height: calc(100vh - 235px);

        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin;
      }

      .sort-filter {
        width: 100%;
        padding: 12px 16px;
        font-size: 14px;
        transition: all 0.2s ease;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &:hover {
          background-color: rgba(0, 59, 149, 0.04);
        }

        &.active {
          color: $primary_color;
          background-color: rgba(0, 59, 149, 0.08);
          font-weight: 500;
        }

        .sort-option-text {
          flex: 1;
          font-size: 14px;
          line-height: 1.4;
        }

        .check-icon {
          color: $primary_color;
          flex-shrink: 0;
          margin-left: 8px;
        }
      }
    }
  }

  .mobile-sort-filter_footer {
    position: absolute;
    height: 50px;
    width: 100%;
    background-color: #fff;
    bottom: 00px;
    z-index: z-index(base);
    display: none;
    align-items: center;
    justify-content: end;
    padding: 25px 10px;
    box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1);

    @media (max-width: $isMobile) {
      display: flex;
      justify-content: end;
      align-items: center;;
    }

    &__done-button {
      height: 40px;
      line-height: 40px;
      padding: 0 15px;
      font-size: 16px;
      font-weight: 600;
      text-transform: uppercase;
      background-color: $primary_color;
      color: #fff;
      border-radius: 999px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: color.adjust($primary-color, $lightness: -10%);
      }
    }
  }

