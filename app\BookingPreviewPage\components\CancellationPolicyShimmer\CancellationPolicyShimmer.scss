@use "/styles/variable" as *;

.cancellation-policy-shimmer {
  width: 100%;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 15px;

  @media (max-width: $breakpoint-md) {
    border-radius: 0%;
  }

  // Shimmer animation for all blocks
  .shimmer {
    background: linear-gradient(90deg, #f5f5f5 25%, #f5f5f5 50%, #eaeaea 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
  }

  &__header {
    height: 24px;
    width: 50%;
    margin-bottom: 15px;
    @extend .shimmer;
  }

  &__description {
    height: 16px;
    width: 80%;
    margin-bottom: 30px;
    @extend .shimmer;
  }

  &__timeline {
    position: relative;
    padding: 30px 20px;
    margin-bottom: 20px;

    @media (max-width: $breakpoint-sm) {
      padding: 20px 10px;
    }

    // Horizontal connecting line
    .timeline-line {
      position: absolute;
      top: 50%;
      left: 60px;
      right: 60px;
      height: 3px;
      transform: translateY(-50%);
      @extend .shimmer;
      z-index: 1;
    }

    // Container for all timeline stages
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;

    .timeline-stage {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      z-index: 2;
      flex: 1;

      .stage-title {
        height: 16px;
        width: 80px;
        margin-bottom: 15px;
        @extend .shimmer;

        @media (max-width: $breakpoint-sm) {
          width: 60px;
          height: 14px;
        }
      }

      .stage-dot {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-bottom: 10px;
        @extend .shimmer;

        // Ensure perfect circle
        flex-shrink: 0;

        &.start, &.end {
          width: 20px;
          height: 20px;
        }

        @media (max-width: $breakpoint-sm) {
          width: 14px;
          height: 14px;

          &.start, &.end {
            width: 16px;
            height: 16px;
          }
        }
      }

      .stage-label {
        height: 14px;
        width: 40px;
        @extend .shimmer;
      }

      .stage-info {
        display: flex;
        flex-direction: column;
        align-items: center;

        .stage-date {
          height: 14px;
          width: 50px;
          margin-bottom: 4px;
          @extend .shimmer;
        }

        .stage-time {
          height: 12px;
          width: 45px;
          @extend .shimmer;
        }
      }

      // Responsive adjustments
      @media (max-width: $breakpoint-sm) {
        .stage-info {
          .stage-date {
            width: 40px;
            height: 12px;
          }

          .stage-time {
            width: 35px;
            height: 10px;
          }
        }
      }
    }
  }

  &__link {
    height: 16px;
    width: 180px;
    @extend .shimmer;
  }
}

// Keyframes for shimmer animation
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
