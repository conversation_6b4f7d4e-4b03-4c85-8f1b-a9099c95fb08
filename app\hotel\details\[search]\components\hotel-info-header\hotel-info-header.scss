@use "@styles/variable" as *;
@use "/styles/zIndex" as *;

.hotel-info-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px 0 30px 0;

    .info-header {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      @media (max-width: $breakpoint-md) {
        flex-direction: column;
      }

      .heading {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 0 15px;

        h2 {
          font-size: 24px;
          font-weight: 700;

          @media (max-width: $breakpoint-md) {
            font-size: 28px;
          }

          @media (max-width: $breakpoint-sm) {
            font-size: 24px;
          }

          @media (max-width: $breakpoint-xs) {
            font-size: 20px;
          }
        }

        .rating {
          font-size: 10px;
          color: $rating_color;
          margin-left: 8px;

          .fa-star {
            margin-right: 2px;
          }
        }
      }
      .buttons {
        display: flex;
        flex-direction: row;
        align-items: center;

        @media (max-width: $breakpoint-md) {
          padding-bottom: 15px;
        }
      }
    }

    .review-location {
      display: flex;
      flex-direction: row;
      justify-content: start;
      gap: 60px;

      @media (max-width: $isMobile) {
        flex-direction: column;
        gap: 15px;
      }

      .review,
      .location {
        display: flex;
        flex-direction: row;
      }

      .review {
        .rating {
          padding: 10px;
          margin: 0 10px 0 0;
          background-color: #17181c;
          font-size: 16px;
          color: #fafafa;
          font-weight: 700;
          border-radius: 10px;
          display: flex;
          justify-content: center;
          align-items: center;

          @media (max-width: $breakpoint-md) {
            font-size: 18px;
          }

          @media (max-width: $breakpoint-xs) {
            font-size: 16px;
          }
        }

        .rating-detail {
          .detail1 {
            font-size: 15px;
            font-weight: 600;
            color: #17181c;

            @media (max-width: $breakpoint-sm) {
              font-size: 15px;
            }

            @media (max-width: $breakpoint-xs) {
              font-size: 14px;
            }
          }

          .detail2 {
            font-size: 14px;
            font-weight: 600;
            color: #5e616e;
            @media (max-width: $breakpoint-sm) {
              font-size: 13px;
            }
          }
        }
      }

      .location {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 10px;

        .icon {
          background-color: rgba(8, 119, 103, 0.05);
          color: $primary-color;
          font-size: 26px;
          padding: 5px 12px;
          border-radius: 5px;

          @media (max-width: $breakpoint-sm) {
            font-size: 24px;
          }

          @media (max-width: $breakpoint-xs) {
            font-size: 22px;
          }
        }
        .details {
          .detail1 {
            font-size: 14px;
            font-weight: 400;

            @media (max-width: $breakpoint-sm) {
              font-size: 15px;
            }

            @media (max-width: $breakpoint-xs) {
              font-size: 14px;
            }
          }

          .detail2 {
            color: $primary-color;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;

            @media (max-width: $breakpoint-sm) {
              font-size: 13px;
            }
          }
        }
      }
    }
  }