export interface SnapshotRoom {
  id: string;
  name: string;
  description: string;
  facilities: any[];
}

export interface SnapshotCancellationRule {
  start: string;
  end: string;
  value: number;
  valueType: string;
}

export interface SnapshotCancellationPolicy {
  rules: SnapshotCancellationRule[];
}

export interface SnapshotTax {
  amount: number;
  description: string;
}

export interface SnapshotRate {
  id: string;
  baseRate: number;
  totalRate: number;
  currency: string;
  refundability: string;
  payAtHotel: boolean;
  taxes: SnapshotTax[];
  cancellationPolicies: SnapshotCancellationPolicy[];
}

export interface BookingSnapshot {
  id: string;
  hotelName: string | null;
  rates: SnapshotRate[];
  rooms: SnapshotRoom[];
}

export interface BookingDetailsApiResponse {
  id: number;
  booking_reference: string;
  service_type: string;
  status: string;
  payment_status: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  hotel_booking: HotelBookingDetails;
}

export interface HotelBookingDetails {
  id: number;
  master_booking_reference: string;
  provider_booking_id: string;
  booking_ref_id: string;
  hotel_id: string;
  search_key: string;
  rate_ids: string[];
  rooms_allocations: RoomAllocation[];
  billing_contact: BillingContact;
  credit_card_info: any;
  special_requests: any[];
  provider_response: ProviderResponse;
  booking_snapshot: BookingSnapshot;
}

export interface RoomAllocation {
  roomid: string;
  rateid: string;
  guests: Guest[];
}

export interface Guest {
  type: string;
  title: string;
  firstname: string;
  lastname: string;
  age: number;
  email: string;
}

export interface BillingContact {
  title: string;
  firstName: string;
  lastName: string;
  age: number | string;
  contact: ContactInfo;
}

export interface ContactInfo {
  phone: string;
  address: Address;
  email: string;
}

export interface Address {
  line1: string;
  line2: string;
  city: City;
  state: State;
  country: Country;
  postalCode: string;
}

export interface City {
  name: string;
  code: string;
}

export interface State {
  name: string;
  code: string;
}

export interface Country {
  name: string;
  code: string;
}

export interface ProviderResponse {
  hotelBooking: HotelBookingProviderData;
}

export interface HotelBookingProviderData {
  billingContact: BillingContact;
  bookingId: string;
  bookingStatus: string;
  bookingType: string;
  cancellationDate: string;
  channelId: string;
  correlationId: string;
  countryOfResidence: string;
  creationDate: string;
  culture: string;
  email: string;
  guestNames: string;
  guests: string[];
  hotel: Hotel;
  hotel_id: string;
  isModified: boolean;
  nationality: string;
  providerId: string;
  providerName: string;
  rates: Rate[];
  rooms: Room[];
  roomsAllocations: ProviderRoomAllocation[];
  search_key: string;
  supplierRates: Rate[];
  totalRate: number;
  tripEndDate: string;
  tripStartDate: string;
  specialRequests: any[];
  supplierCancellationDate: string;
}

export interface Hotel {
  category: string;
  chainName: string;
  chainCode?: string;
  checkinInfo: CheckinInfo;
  checkoutInfo: CheckoutInfo;
  contact: HotelContact;
  descriptions: Description[];
  distance: string;
  facilities: Facility[];
  geoCode: GeoCode;
  heroImage: string;
  id: string;
  name: string;
  policies: Policy[];
  providerHotelId: string;
  providerId: string;
  relevanceScore: string;
  starRating: string;
  type?: string;
}

export interface CheckinInfo {
  beginTime: string;
  minAge: string;
}

export interface CheckoutInfo {
  time: string;
}

export interface HotelContact {
  address: HotelAddress;
  email?: string;
  phone: string;
}

export interface HotelAddress {
  city: City;
  country: Country;
  line1: string;
  state: any;
  postalCode?: string;
}

export interface Description {
  text: string;
  type: string;
}

export interface Facility {
  name: string;
}

export interface GeoCode {
  lat: string;
  long: string;
}

export interface Policy {
  text: string;
  type: string;
}

export interface Rate {
  IsPANMandatory: boolean;
  IsPassportMandatory: boolean;
  additionalCharges: AdditionalCharge[];
  allGuestsInfoRequired: boolean;
  availability: string;
  baseRate: number;
  boardBasis: BoardBasis;
  cancellationPolicies: CancellationPolicy[];
  cardRequired: boolean;
  conversionRate?: ConversionRate;
  currency: string;
  dailyRates: DailyRate[];
  depositRequired: boolean;
  deposits: any[];
  distributionChannel: string;
  distributionType: string;
  guaranteeRequired: boolean;
  id: string;
  isChildConvertedToAdult: boolean;
  isContractedRate: boolean;
  minSellingRate: number;
  needsPriceCheck: boolean;
  occupancies: Occupancy[];
  offers: any[];
  onlineCancellable: boolean;
  payAtHotel: boolean;
  policies: Policy[];
  providerHotelId: string;
  providerId: string;
  providerName: string;
  publishedBaseRate: number;
  publishedRate: number;
  refundability: string;
  refundable: boolean;
  specialRequestSupported: boolean;
  taxes: Tax[];
  totalRate: number;
  traderInformation: TraderInformation;
  type: string;
  usdconversionRate?: ConversionRate;
}

export interface AdditionalCharge {
  charge: Charge;
  text: string;
}

export interface Charge {
  amount: number;
  currency: string;
  description: string;
  frequency: string;
  type: string;
  unit: string;
}

export interface BoardBasis {
  description: string;
  type: string;
}

export interface CancellationPolicy {
  rules: CancellationRule[];
}

export interface CancellationRule {
  end: string;
  estimatedValue: number;
  start: string;
  value: number;
  valueType: string;
}

export interface ConversionRate {
  conversionFactor: number;
  fromCurrency: string;
  toCurrency: string;
}

export interface DailyRate {
  amount: number;
  date: string;
  discount: number;
  taxIncluded: boolean;
}

export interface Occupancy {
  childAges: any[];
  numOfAdults: string;
  numOfChildren: string;
  roomId: string;
}

export interface Tax {
  amount: number;
  description: string;
  isIncludedInBaseRate?: boolean;
}

export interface TraderInformation {
  trader: any[];
}

export interface Room {
  beds: Bed[];
  description: string;
  facilities: Facility[];
  id: string;
  maxGuestAllowed: string;
  maxAdultAllowed?: string;
  maxChildrenAllowed?: string;
  name: string;
  smokingAllowed: boolean;
  views: any[];
}

export interface Bed {
  count: string;
  type?: string;
}

export interface ProviderRoomAllocation {
  guests: ProviderGuest[];
  rateId: string;
  roomId: string;
}

export interface ProviderGuest {
  age: string;
  email: string;
  firstName: string;
  lastName: string;
  title: string;
  type: string;
}