"use client";
import React, { useState, useRef } from 'react';
import { useDynamicModalHeight } from '../../hooks/useDynamicModalHeight';
import styles from '../../HotelSearchBar.module.scss';

interface DynamicModalDemoProps {
  onClose?: () => void;
}

const DynamicModalDemo: React.FC<DynamicModalDemoProps> = ({ onClose }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'location' | 'date' | 'travelers'>('location');
  const [isMobile, setIsMobile] = useState(false);
  
  const triggerRef = useRef<HTMLDivElement>(null);
  
  const modalHeight = useDynamicModalHeight({
    triggerElementRef: triggerRef,
    modalType: isModalOpen ? modalType : null,
    isOpen: isModalOpen,
    isMobile,
    minHeight: 300,
    safetyPadding: 40,
    desiredHeight: 500
  });

  const openModal = (type: 'location' | 'date' | 'travelers') => {
    setModalType(type);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const generateDemoContent = () => {
    const items = [];
    for (let i = 1; i <= 50; i++) {
      items.push(
        <div key={i} className="p-4 border-b border-gray-200">
          <h3 className="font-semibold">Demo Item {i}</h3>
          <p className="text-gray-600">
            This is demo content item {i}. The modal should automatically adjust its height
            based on available viewport space while maintaining usability.
          </p>
        </div>
      );
    }
    return items;
  };

  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold mb-4">Dynamic Modal Height Demo</h2>
      
      <div className="space-y-4">
        <div className="flex gap-4 items-center">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={isMobile}
              onChange={(e) => setIsMobile(e.target.checked)}
            />
            Mobile Mode
          </label>
        </div>

        <div className="flex gap-4">
          <div ref={triggerRef} className="space-x-2">
            <button
              onClick={() => openModal('location')}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Open Location Modal
            </button>
            <button
              onClick={() => openModal('date')}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Open Date Modal
            </button>
            <button
              onClick={() => openModal('travelers')}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
            >
              Open Travelers Modal
            </button>
          </div>
        </div>
      </div>

      {/* Dynamic Modal */}
      {isModalOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={closeModal}
          />
          
          {/* Modal */}
          <div
            ref={modalHeight.modalRef}
            className={`${styles.dropdown} ${styles.responsiveModal} z-50 bg-white`}
            style={{
              position: 'fixed',
              top: isMobile ? 'auto' : 'var(--modal-top-position, 50%)',
              bottom: isMobile ? '0' : 'auto',
              left: isMobile ? '0' : '50%',
              right: isMobile ? '0' : 'auto',
              transform: isMobile ? 'none' : 'translateX(-50%)',
              width: isMobile ? '100%' : '400px',
              borderRadius: isMobile ? '16px 16px 0 0' : '8px'
            }}
          >
            <div className={styles.dynamicModalContent}>
              {/* Header */}
              <div className={styles.modalHeader}>
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">
                    {modalType === 'location' && 'Location Selection'}
                    {modalType === 'date' && 'Date Selection'}
                    {modalType === 'travelers' && 'Travelers Selection'}
                  </h3>
                  <button
                    onClick={closeModal}
                    className="p-2 text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>
              </div>

              {/* Body */}
              <div className={styles.modalBody}>
                <div className="space-y-2">
                  <p className="text-sm text-gray-600 mb-4">
                    Modal Type: <strong>{modalType}</strong><br/>
                    Mode: <strong>{isMobile ? 'Mobile' : 'Desktop'}</strong><br/>
                    Max Height: <strong>var(--modal-max-height)</strong><br/>
                    Available Space: <strong>var(--modal-available-space)</strong>
                  </p>
                  
                  <div className="border rounded-lg overflow-hidden">
                    {generateDemoContent()}
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className={styles.modalFooter}>
                <div className="flex justify-end gap-2">
                  <button
                    onClick={closeModal}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={closeModal}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    Apply
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default DynamicModalDemo;
