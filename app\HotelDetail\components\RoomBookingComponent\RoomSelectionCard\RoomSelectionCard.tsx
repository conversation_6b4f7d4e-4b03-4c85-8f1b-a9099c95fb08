"use client";
import React, { useCallback, useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import ImageCarousel from "../../ImageCarousal/ImageCarousal";
import SlideFromRightModal from "../../SlideFromRightModal/SlideFromRightModal";
import Link from "next/link";
import useScrollLock from "@/app/components/utilities/ScrollLock/useScrollLock";
import { useTranslation } from "@/app/hooks/useTranslation";
import "./RoomSelectionCard.scss";
import { Data, Room } from "@/app/HotelDetail/hotel-level-details.model";
import axios from "axios";
import { useCommonContext } from "@/app/contexts/commonContext";
import { RoomsAndRatesApiResponse } from "@/models/hotel/rooms-and-rates.model";

const API_URL = "/data/hotelleveldetails.json";



function RoomSelectionCard() {
  const { t } = useTranslation();
  const router = useRouter();
  const { hotelSearchFormData } = useCommonContext();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDropDownOpen, setIsDropDownOpen] = useState<boolean>(false);
  const [roomType, setRoomType] = useState<string>("");
  const [filterOptions, setFilterOptions] = useState({
    breakfast: false,
    fullBoard: false,
    halfBoard: false,
    transfer: false,
    cancellation: false,
  });
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [hotelLevelDetailsResponse, setHotelLevelDetailsResponse] =
    useState<Data>();
  const [selectedRoom, setSelectedRoom] = useState<Room>();
  const [isHeaderSticky, setIsHeaderSticky] = useState<boolean>(false);
  const [roomQuantities, setRoomQuantities] = useState<{[key: string]: number}>({});
  const [dropdownStates, setDropdownStates] = useState<{[key: string]: boolean}>({});
  const [isMobileView, setIsMobileView] = useState<boolean>(false);
  const [expandedRooms, setExpandedRooms] = useState<{[key: string]: boolean}>({});
  const [bookingSummaryHeight, setBookingSummaryHeight] = useState<number>(120);

  // Reference to the header element
  const headerRef = useRef<HTMLDivElement>(null);
  const mobileBookingSummaryRef = useRef<HTMLDivElement>(null);

  // Get total rooms from search criteria
  const maxRoomsFromSearch = hotelSearchFormData?.travelers?.rooms || 3;

  const handleRoomTypeSelection = (type: string) => {
    setRoomType(type);
    setIsDropDownOpen(false);

    // Scroll to the corresponding room card
    setTimeout(() => {
      const roomElement = document.querySelector(`[data-room-name="${type}"]`);
      if (roomElement) {
        const headerHeight = headerRef.current?.offsetHeight || 0;
        const elementPosition = roomElement.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - headerHeight - 90;

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    }, 100);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleFilterChange = (filter: keyof typeof filterOptions) => {
    setFilterOptions({
      ...filterOptions,
      [filter]: !filterOptions[filter],
    });
  };

  const { setIsLoading } = useCommonContext();

  useScrollLock(isModalOpen);

  // Handle mobile view detection
  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== 'undefined') {
        setIsMobileView(window.innerWidth <= 950);
      }
    };

    // Initial check
    handleResize();

    // Add event listener for window resize
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
    }

    // Cleanup
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, []);

  // Handle sticky header on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (!headerRef.current) return;

      const headerRect = headerRef.current.getBoundingClientRect();
      const stickyThreshold = 100; // 100px from the top

      if (headerRect.top <= stickyThreshold && !isHeaderSticky) {
        setIsHeaderSticky(true);
      } else if (headerRect.top > stickyThreshold && isHeaderSticky) {
        setIsHeaderSticky(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [isHeaderSticky]);

  const getDetails = useCallback(async () => {
    try {
      //setIsLoading(true);
      const response = await axios.get(API_URL);
      setHotelLevelDetailsResponse(response.data.data);
    } catch (err) {
      // Silent error handling
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading]);

  useEffect(() => {
    getDetails();
  }, [getDetails]);

  const formatLabel = (value?: string) => {
    if (!value) return "";
    return value
      .toLowerCase()
      .split("_")
      .map((word, index) =>
        index === 0
          ? word.charAt(0).toUpperCase() + word.slice(1)
          : word.toLowerCase()
      )
      .join("-");
  };

  const handleViewDetails = (room: Room) => {
    setSelectedRoom(room);
    setIsModalOpen(true);
  };

  const handleQuantityChange = (roomOptionId: string, quantity: number) => {
    setRoomQuantities(prev => ({
      ...prev,
      [roomOptionId]: quantity
    }));
  };

  const getRoomQuantity = (roomOptionId: string) => {
    return roomQuantities[roomOptionId] !== undefined ? roomQuantities[roomOptionId] : 0;
  };

  const toggleDropdown = (roomOptionId: string) => {
    setDropdownStates(prev => ({
      ...prev,
      [roomOptionId]: !prev[roomOptionId]
    }));
  };

  const isDropdownOpen = (roomOptionId: string) => {
    return dropdownStates[roomOptionId] || false;
  };

  const toggleRoomExpansion = (roomName: string) => {
    setExpandedRooms(prev => ({
      ...prev,
      [roomName]: !prev[roomName]
    }));
  };

  const isRoomExpanded = (roomName: string) => {
    return expandedRooms[roomName] || false;
  };

  const selectQuantity = (roomOptionId: string, quantity: number) => {
    handleQuantityChange(roomOptionId, quantity);

    // Sync quantities between main and modal views
    syncQuantities(roomOptionId, quantity);

    setDropdownStates(prev => ({
      ...prev,
      [roomOptionId]: false
    }));
  };

  // Sync quantities between main and modal room selections
  const syncQuantities = (changedRoomOptionId: string, quantity: number) => {
    // Determine if this is a modal or main room selection
    const isModalSelection = changedRoomOptionId.includes('-modal-');

    let correspondingId: string;
    if (isModalSelection) {
      // Convert modal ID to main ID
      correspondingId = changedRoomOptionId.replace('-modal-', '-');
    } else {
      // Convert main ID to modal ID
      correspondingId = changedRoomOptionId.replace(/^(.+?)-(.+?)-(\d+)$/, '$1-modal-$2-$3');
    }

    // Update the corresponding room option with the same quantity
    setRoomQuantities(prev => ({
      ...prev,
      [correspondingId]: quantity
    }));
  };

  // Calculate total rooms and total price (including both main and modal selections)
  const calculateTotals = () => {
    let totalRooms = 0;
    let totalPrice = 0;
    let totalTaxes = 0;

    hotelLevelDetailsResponse?.rooms?.forEach((room) => {
      room?.roomOptions?.forEach((roomOption, index) => {
        // Check main room selection
        const roomOptionId = `${room?.name}-${roomOption?.name}-${index}`;
        const mainQuantity = getRoomQuantity(roomOptionId);

        // Check modal room selection
        const modalRoomOptionId = `${room?.name}-modal-${roomOption?.name}-${index}`;
        const modalQuantity = getRoomQuantity(modalRoomOptionId);

        // Use the maximum of main or modal quantity (they should be synced, but this ensures we get the latest)
        const quantity = Math.max(mainQuantity, modalQuantity);

        if (quantity > 0) {
          totalRooms += quantity;
          const basePrice = parseInt(String(roomOption?.fareDetail?.baseFare)?.replace(/[^\d]/g, '') || '0');
          const taxPrice = parseInt(String(roomOption?.fareDetail?.taxesAndFees)?.replace(/[^\d]/g, '') || '0');

          totalPrice += basePrice * quantity;
          totalTaxes += taxPrice * quantity;
        }
      });
    });

    return { totalRooms, totalPrice, totalTaxes };
  };

  const { totalRooms, totalPrice, totalTaxes } = calculateTotals();

  // Update mobile rooms container margin based on booking summary height
  useEffect(() => {
    if (!isMobileView || !mobileBookingSummaryRef.current) return;

    const updateMargin = () => {
      const summaryElement = mobileBookingSummaryRef.current;
      if (summaryElement) {
        const height = summaryElement.offsetHeight;
        setBookingSummaryHeight(height);

        // Update CSS custom property for dynamic margin
        const mobileContainer = document.querySelector('.mobile-rooms-container') as HTMLElement;
        if (mobileContainer) {
          mobileContainer.style.marginBottom = `${height + 20}px`; // Add 20px extra padding
        }
      }
    };

    // Initial measurement
    updateMargin();

    // Create ResizeObserver to watch for booking summary height changes
    const resizeObserver = new ResizeObserver(() => {
      updateMargin();
    });

    if (mobileBookingSummaryRef.current) {
      resizeObserver.observe(mobileBookingSummaryRef.current);
    }

    // Cleanup
    return () => {
      resizeObserver.disconnect();
    };
  }, [isMobileView, totalRooms, totalPrice]); // Re-run when these values change

  // Calculate available room options for a specific room option
  const getAvailableRoomOptions = (currentRoomOptionId: string) => {
    const remainingRooms = maxRoomsFromSearch - totalRooms;
    const currentQuantity = getRoomQuantity(currentRoomOptionId);
    const maxAvailable = remainingRooms + currentQuantity;

    // Generate options from 0 to maxAvailable, but cap at maxRoomsFromSearch
    const options = [];
    for (let i = 0; i <= Math.min(maxAvailable, maxRoomsFromSearch); i++) {
      options.push(i);
    }
    return options;
  };

  const handleReserve = () => {
    if (totalRooms === 0) {
      alert('Please select at least one room to reserve.');
      return;
    }

    if (totalRooms > maxRoomsFromSearch) {
      alert(`You can only select up to ${maxRoomsFromSearch} room${maxRoomsFromSearch !== 1 ? 's' : ''} based on your search criteria.`);
      return;
    }

    // Reservation details validation passed

    // Navigate to booking preview page
    router.push('/BookingPreviewPage');
  };

  // Check if maximum rooms reached
  const isMaxRoomsReached = totalRooms >= maxRoomsFromSearch;



  return (
    <div className="room-selection-card-container">
      <h6>{t("hotel.detail.selectYourRoom")}</h6>

      {/* Filter options section */}
      <div className="room-filter-options">
        <div className="filter-label">Filter rooms by : </div>
        <div className="filter-checkboxes">
          <label className="filter-option">
            <input
              type="checkbox"
              checked={filterOptions.breakfast}
              onChange={() => handleFilterChange("breakfast")}
            />
            <span>Breakfast</span>
          </label>
          <label className="filter-option">
            <input
              type="checkbox"
              checked={filterOptions.fullBoard}
              onChange={() => handleFilterChange("fullBoard")}
            />
            <span>Full Board</span>
          </label>
          <label className="filter-option">
            <input
              type="checkbox"
              checked={filterOptions.halfBoard}
              onChange={() => handleFilterChange("halfBoard")}
            />
            <span>Half Board</span>
          </label>
          <label className="filter-option">
            <input
              type="checkbox"
              checked={filterOptions.transfer}
              onChange={() => handleFilterChange("transfer")}
            />
            <span>Transfer</span>
          </label>
          <label className="filter-option">
            <input
              type="checkbox"
              checked={filterOptions.cancellation}
              onChange={() => handleFilterChange("cancellation")}
            />
            <span>Cancellation Available</span>
          </label>
        </div>

        <div className="search-rooms">
          <input
            type="text"
            placeholder="Search rooms"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <span className="material-icons search-icon">search</span>
        </div>
      </div>

      {/* Header - Desktop only */}
      {!isMobileView && (
        <div
          ref={headerRef}
          className={`room-selection-card-header ${
            isHeaderSticky ? "sticky" : ""
          }`}
        >
          <div className="header1">
            <span
              className="label"
              onClick={() => setIsDropDownOpen(!isDropDownOpen)}
            >
              {hotelLevelDetailsResponse?.rooms?.length || 0} Room Type{" "}
            </span>
            <span
              className="material-icons arrowDownIcon"
              onClick={() => setIsDropDownOpen(!isDropDownOpen)}
            >
              {isDropDownOpen ? "keyboard_arrow_up" : "keyboard_arrow_down"}
            </span>

            <div
              className={`room-type-dropdown ${isDropDownOpen ? "active" : ""}`}
            >
              {hotelLevelDetailsResponse?.rooms?.map((details, index) => (
                <div
                  className="room-type-dropdown__item"
                  onClick={() => handleRoomTypeSelection(details?.name)}
                  key={index}
                >
                  {details?.name}
                </div>
              ))}
            </div>

            {isDropDownOpen && (
              <div
                className="room-type-dropdown-overlay"
                onClick={() => setIsDropDownOpen(false)}
              ></div>
            )}
          </div>
          <div className="header2">Options</div>
          <div className="header4">No. of Guests</div>
          <div className="header3">Price</div>
          <div className="header5">Booking Summary</div>
        </div>
      )}

      <div className="room-selection-main-content">
        {isMobileView ? (
          // Mobile Layout - Accordion Style
          <div className="mobile-rooms-container">
            {hotelLevelDetailsResponse?.rooms?.map((details, roomIndex) => (
              <div key={roomIndex} className="mobile-room-accordion">
                {/* Room Header with Expand/Collapse */}
                <div
                  className="mobile-room-header"
                  onClick={() => toggleRoomExpansion(details?.name || '')}
                >
                  <div className="room-header-content">
                    <div className="room-basic-info">
                      <h5>{details?.name}</h5>
                      <p className="rooms-left">{`${details?.roomsLeft} rooms left`}</p>
                      <div className="room-price-preview">
                        {details?.roomOptions && details.roomOptions.length > 0 && (
                          <span className="from-price">
                            From ₹{details.roomOptions[0]?.fareDetail?.baseFare}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="expand-controls">
                      <div className="options-preview">
                        {details?.roomOptions?.length} option{details?.roomOptions?.length !== 1 ? 's' : ''}
                      </div>
                      <i className={`fa-solid ${isRoomExpanded(details?.name || '') ? 'fa-chevron-up' : 'fa-chevron-down'} expand-icon`}></i>
                    </div>
                  </div>
                </div>

                {/* Expandable Room Details and Options */}
                {isRoomExpanded(details?.name || '') && (
                  <div className="mobile-room-expanded-content">
                    {/* Room Details */}
                    <div className="room-details-section">
                      <div className="img-carousal">
                        <ImageCarousel images={details?.imageList} />
                      </div>

                      <div className="details">
                        <ul>
                          {details?.properties?.map((property, index) => (
                            <li key={index}>
                              <span className="material-icons">{property?.code}</span>
                              <span>{property?.data}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="amenities">
                        <ul>
                          {details?.roomLevelAmenities
                            .slice(0, 7)
                            .map((amenity, index) => (
                              <li key={index}>
                                <span className="material-icons">check</span>
                                <span>{amenity}</span>
                              </li>
                            ))}
                        </ul>
                        <div
                          className="more-amenities"
                          onClick={() => handleViewDetails(details)}
                        >
                          {details?.roomLevelAmenities.length > 7 && (
                            <span>+{details?.roomLevelAmenities.length - 7} more</span>
                          )}
                        </div>
                      </div>

                      <p
                        onClick={() => handleViewDetails(details)}
                        className="viewDetails"
                      >
                        {t("hotel.viewDetails")}
                      </p>
                    </div>

                    {/* Room Options */}
                    <div className="room-options-section">
                      <h6 className="options-title">Choose your option:</h6>
                      <div className="mobile-room-options-scroll-container">
                        {details?.roomOptions.map((roomoption, roomOptionIndex) => {
                          const roomOptionId = `${details?.name}-${roomoption?.name}-${roomOptionIndex}`;
                          return (
                            <div key={roomOptionIndex} className="mobile-room-option-card">
                            <div className="mobile-option-header">
                              <h3 className="heading">{roomoption?.name}</h3>
                              <div className="mobile-guests">
                                {[...Array(roomoption?.fareDetail?.numberOfPersons)].map(
                                  (_, guestIndex) => (
                                    <i key={guestIndex} className="fa-solid fa-user"></i>
                                  )
                                )}
                              </div>
                            </div>

                            <div className="mobile-option-benefits">
                              <ul>
                                {roomoption?.cancellationBenefits?.code
                                  .toLowerCase()
                                  .includes("non_refundable") && (
                                  <li className="non-refundable">
                                    <i className="fa-solid fa-circle-xmark"></i>
                                    {formatLabel(roomoption?.cancellationBenefits?.code)}
                                  </li>
                                )}

                                <li className="free-cancellation">
                                  <i className="fa-solid fa-check"></i>
                                  {roomoption?.cancellationBenefits?.data
                                    .toLowerCase()
                                    .includes("free cancellation")
                                    ? "Free cancellation"
                                    : ""}
                                </li>
                                {roomoption?.otherBenefits?.map((otherbenefit, benefitIndex) => (
                                  <li key={benefitIndex} className="free-cancellation">
                                    <i className="fa-solid fa-check"></i>
                                    {otherbenefit}
                                  </li>
                                ))}
                              </ul>
                            </div>

                            <div className="mobile-price-section">
                              <div className="mobile-pricing">
                                <div className="rounded-indicator">
                                  <span>
                                    {roomoption?.fareDetail?.markupDiscountPercent}
                                  </span>
                                </div>
                                <div className="discountAmt-amt">
                                  <span className="discountAmt">
                                    {roomoption?.fareDetail?.markUpFare}
                                  </span>
                                  <span className="amt">
                                    {roomoption?.fareDetail?.baseFare}{" "}
                                  </span>
                                </div>
                                <p>+ {roomoption?.fareDetail?.taxesAndFees} taxes</p>
                                <p>per night for 1 room</p>
                              </div>

                              <div className="mobile-room-availability">
                                <p>{roomoption?.fomoTag}</p>
                                <div className="room-quantity-counter">
                                  <div className="quantity-controls">
                                    <button
                                      className="quantity-btn decrease"
                                      onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        const currentQty = getRoomQuantity(roomOptionId);
                                        if (currentQty > 0) {
                                          handleQuantityChange(roomOptionId, currentQty - 1);
                                        }
                                      }}
                                      disabled={getRoomQuantity(roomOptionId) === 0}
                                    >
                                      <i className="fa-solid fa-minus"></i>
                                    </button>
                                    <span className="quantity-display">
                                      {getRoomQuantity(roomOptionId)} room{getRoomQuantity(roomOptionId) !== 1 ? 's' : ''}
                                    </span>
                                    <button
                                      className="quantity-btn increase"
                                      onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        const currentQty = getRoomQuantity(roomOptionId);
                                        const maxAvailable = Math.min(
                                          maxRoomsFromSearch - totalRooms + currentQty,
                                          maxRoomsFromSearch
                                        );
                                        if (currentQty < maxAvailable) {
                                          handleQuantityChange(roomOptionId, currentQty + 1);
                                        }
                                      }}
                                      disabled={
                                        getRoomQuantity(roomOptionId) >= Math.min(
                                          maxRoomsFromSearch - totalRooms + getRoomQuantity(roomOptionId),
                                          maxRoomsFromSearch
                                        )
                                      }
                                    >
                                      <i className="fa-solid fa-plus"></i>
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          // Desktop Layout - Original
          <div className="rooms-section">
            {hotelLevelDetailsResponse?.rooms?.map((details, index) => (
              <div
                className="room-selection-card"
                key={index}
                data-room-name={details?.name}
              >
                <div className="room-selection-card-content">
                  <div className="item1">
                    <h5>{details?.name}</h5>
                    <div className="img-carousal">
                      <ImageCarousel images={details?.imageList} />
                    </div>
                    <p className="rooms-left">{`${details?.roomsLeft} rooms left`}</p>
                    <div className="details">
                      <ul>
                        {details?.properties?.map((property, index) => (
                          <li key={index}>
                            <span className="material-icons">{property?.code}</span>
                            <span>{property?.data}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="amenities">
                      <ul>
                        {details?.roomLevelAmenities
                          .slice(0, 7)
                          .map((amenity, index) => (
                            <li key={index}>
                              <span className="material-icons">check</span>
                              <span>{amenity}</span>
                            </li>
                          ))}
                      </ul>
                      <div
                        className="more-amenities"
                        onClick={() => handleViewDetails(details)}
                      >
                        {details?.roomLevelAmenities.length > 7 && (
                          <span>+{details?.roomLevelAmenities.length - 7} more</span>
                        )}
                      </div>
                    </div>

                    <p
                      onClick={() => handleViewDetails(details)}
                      className="viewDetails"
                    >
                      {t("hotel.viewDetails")}
                    </p>
                  </div>

                  <div className="item2">
                    {details?.roomOptions.map((roomoption, roomOptionIndex) => {
                      const roomOptionId = `${details?.name}-${roomoption?.name}-${roomOptionIndex}`;
                      return (
                        <div className="item2__row row1" key={roomOptionIndex}>
                          <div className="option">
                            <h3 className="heading">{roomoption?.name}</h3>

                            <ul>
                              {roomoption?.cancellationBenefits?.code
                                .toLowerCase()
                                .includes("non_refundable") && (
                                <li className="non-refundable">
                                  <i className="fa-solid fa-circle-xmark"></i>
                                  {formatLabel(roomoption?.cancellationBenefits?.code)}
                                </li>
                              )}

                              <li className="free-cancellation">
                                <i className="fa-solid fa-check"></i>
                                {roomoption?.cancellationBenefits?.data
                                  .toLowerCase()
                                  .includes("free cancellation")
                                  ? "Free cancellation"
                                  : ""}
                              </li>
                              {roomoption?.otherBenefits?.map((otherbenefit, benefitIndex) => (
                                <li key={benefitIndex} className="free-cancellation">
                                  <i className="fa-solid fa-check"></i>
                                  {otherbenefit}
                                </li>
                              ))}
                            </ul>
                          </div>
                          <div className="totalguests">
                            {[...Array(roomoption?.fareDetail?.numberOfPersons)].map(
                              (_, guestIndex) => (
                                <i key={guestIndex} className="fa-solid fa-user"></i>
                              )
                            )}
                          </div>
                          <div className="price">
                            <div className="pricing">
                              <div className="rounded-indicator">
                                <span>
                                  {roomoption?.fareDetail?.markupDiscountPercent}
                                </span>
                              </div>
                              <div className="discountAmt-amt">
                                <span className="discountAmt">
                                  {roomoption?.fareDetail?.markUpFare}
                                </span>
                                <span className="amt">
                                  {roomoption?.fareDetail?.baseFare}{" "}
                                </span>
                              </div>
                              <p>+ {roomoption?.fareDetail?.taxesAndFees} taxes</p>
                              <p>per night for 1 room</p>
                            </div>
                            <div className="roomAvailability">
                              <p>{roomoption?.fomoTag}</p>
                              <div className="room-quantity-dropdown">
                                <div
                                  className="quantity-selector-button"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    toggleDropdown(roomOptionId);
                                  }}
                                >
                                  <span className="quantity-text">
                                    {getRoomQuantity(roomOptionId)}
                                  </span>
                                  <span className="room-text"> room{getRoomQuantity(roomOptionId) > 1 ? 's' : ''}</span>
                                  <i className={`fa-solid ${isDropdownOpen(roomOptionId) ? 'fa-chevron-up' : 'fa-chevron-down'}`}></i>
                                </div>

                                {isDropdownOpen(roomOptionId) && (
                                  <>
                                    <div
                                      className="dropdown-overlay"
                                      onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        toggleDropdown(roomOptionId);
                                      }}
                                    ></div>
                                    <div className="quantity-dropdown-menu">
                                      {getAvailableRoomOptions(roomOptionId).map(num => (
                                        <div
                                          key={num}
                                          className="quantity-option"
                                          onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            selectQuantity(roomOptionId, num);
                                          }}
                                        >
                                          <span className="quantity-number">{num}</span>
                                          <span className="quantity-price">
                                            (₹ {num === 0 ? '0' : (parseInt(String(roomoption?.fareDetail?.baseFare)?.replace(/[^\d]/g, '') || '0') * num).toLocaleString()})
                                          </span>
                                        </div>
                                      ))}
                                    </div>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Booking Summary - Desktop only */}
        {!isMobileView && (
          <div className="item5 booking-summary">
            <div className="booking-summary-content">
              {totalRooms > 0 && (
                <>
                  <div className="summary-header">
                    <h4>{totalRooms} of {maxRoomsFromSearch} room{maxRoomsFromSearch !== 1 ? 's' : ''} selected</h4>
                  </div>

                  <div className="total-price">
                    <span className="currency">₹</span>
                    <span className="amount">{totalPrice.toLocaleString()}</span>
                  </div>

                  <div className="taxes-info">
                    <span className="tax-amount">+₹ {totalTaxes.toLocaleString()}</span>
                    <span className="tax-label"> taxes and fees</span>
                  </div>
                </>
              )}

              <button
                className="reserve-button"
                onClick={handleReserve}
                disabled={totalRooms === 0 || totalRooms < maxRoomsFromSearch}
              >
                Book Now
              </button>

              <div className="no-charge-info">
                <i className="fa-solid fa-circle-check"></i>
                <span>You won&apos;t be charged yet</span>
              </div>

              {isMaxRoomsReached && (
                <div className="max-rooms-info">
                  <i className="fa-solid fa-info-circle"></i>
                  <span className="max-rooms-text">Maximum rooms selected ({maxRoomsFromSearch})</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Mobile Sticky Booking Summary - Compact Design */}
      {isMobileView && totalRooms > 0 && (
        <div className="mobile-sticky-booking-summary" ref={mobileBookingSummaryRef}>
          <div className="mobile-booking-summary-content">
            <div className="mobile-summary-left">
              <div className="mobile-summary-header">
                <h4>{totalRooms} of {maxRoomsFromSearch} room{maxRoomsFromSearch !== 1 ? 's' : ''} selected</h4>
              </div>
              <div className="mobile-total-price">
                <span className="currency">₹</span>
                <span className="amount">{totalPrice.toLocaleString()}</span>
              </div>
              <div className="mobile-taxes-info">
                <span className="tax-amount">+₹{totalTaxes.toLocaleString()}</span>
                <span className="tax-label"> taxes and fees</span>
              </div>
            </div>
            <button
              className="mobile-reserve-button"
              onClick={handleReserve}
              disabled={totalRooms === 0 || totalRooms < maxRoomsFromSearch}
            >
              Book Now
            </button>
          </div>
        </div>
      )}

      <SlideFromRightModal
        isOpen={isModalOpen}
        handleClose={handleCloseModal}
        title={selectedRoom?.name || "Room Name"}
      >
        <div className="room-selection-card-container__modal">
          <div className="content content1">
            <div className="img-carousal">
              <ImageCarousel images={selectedRoom?.imageList || []} />
            </div>
            <div className="details">
              <h5>{selectedRoom?.name}</h5>
              <ul>
                {selectedRoom?.properties?.map((property, index) => (
                  <li key={index}>
                    <span className="material-icons">{property?.code}</span>
                    <span>{property?.data}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="amenities">
              <h2>{t("hotel.amenities")}</h2>
              <ul>
                {selectedRoom?.roomLevelAmenities?.map((amenity, index) => (
                  <li key={index}>
                    <span className="material-icons">check</span>{" "}
                    <span>{amenity}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="flex flex-col">
                 <div className="content content2">
            {selectedRoom?.roomOptions?.map((roomOption, modalRoomOptionIndex) => {
              const modalRoomOptionId = `${selectedRoom?.name}-modal-${roomOption?.name}-${modalRoomOptionIndex}`;
              return (
                <div className="option" key={modalRoomOptionIndex}>
                  <h3 className="heading">{roomOption?.name}</h3>
                  <ul>
                    {roomOption?.cancellationBenefits?.code
                      .toLowerCase()
                      .includes("non_refundable") && (
                      <li className="non-refundable">
                        <i className="fa-solid fa-circle-xmark"></i>
                        {formatLabel(roomOption?.cancellationBenefits?.code)}
                      </li>
                    )}

                    <li className="free-cancellation">
                      <i className="fa-solid fa-check"></i>
                      {roomOption?.cancellationBenefits?.data
                        .toLowerCase()
                        .includes("free cancellation")
                        ? "Free cancellation"
                        : ""}
                    </li>
                    {roomOption?.otherBenefits?.map((otherBenefit, subIndex) => (
                      <li className="free-cancellation" key={subIndex}>
                        <i className="fa-solid fa-check"></i>
                        {otherBenefit}
                      </li>
                    ))}
                  </ul>

                  <div className="price">
                    <div className="pricing">
                      <div className="rounded-indicator">
                        <span>
                          {roomOption?.fareDetail?.markupDiscountPercent}
                        </span>
                      </div>
                      <div className="discountAmt-amt">
                        <span className="discountAmt">
                          {roomOption?.fareDetail?.markUpFare}
                        </span>
                        <span className="amt">
                          {roomOption?.fareDetail?.baseFare}{" "}
                        </span>
                      </div>
                      <p>+ {roomOption?.fareDetail?.taxesAndFees} taxes</p>
                      <p>per night for 1 room</p>
                    </div>
                    <div className="roomAvailability">
                      <p>{roomOption?.fomoTag}</p>
                      <div className="room-quantity-dropdown">
                        <div
                          className="quantity-selector-button"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            toggleDropdown(modalRoomOptionId);
                          }}
                        >
                          <span className="quantity-text">
                            {getRoomQuantity(modalRoomOptionId)}
                          </span>
                          <span className="room-text"> room{getRoomQuantity(modalRoomOptionId) > 1 ? 's' : ''}</span>
                          <i className={`fa-solid ${isDropdownOpen(modalRoomOptionId) ? 'fa-chevron-up' : 'fa-chevron-down'}`}></i>
                        </div>

                        {isDropdownOpen(modalRoomOptionId) && (
                          <>
                            <div
                              className="dropdown-overlay"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                toggleDropdown(modalRoomOptionId);
                              }}
                            ></div>
                            <div className="quantity-dropdown-menu">
                              {getAvailableRoomOptions(modalRoomOptionId).map(num => (
                                <div
                                  key={num}
                                  className="quantity-option"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    selectQuantity(modalRoomOptionId, num);
                                  }}
                                >
                                  <span className="quantity-number">{num}</span>
                                  <span className="quantity-price">
                                    (₹ {num === 0 ? '0' : (parseInt(String(roomOption?.fareDetail?.baseFare)?.replace(/[^\d]/g, '') || '0') * num).toLocaleString()})
                                  </span>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Modal Booking Summary */}
          <div className="modal-booking-summary">
            <div className="modal-booking-summary__content">
              {totalRooms > 0 && (
                <>
                  <div className="modal-booking-summary__summary-header">
                    <h4>{totalRooms} of {maxRoomsFromSearch} room{maxRoomsFromSearch !== 1 ? 's' : ''} selected</h4>
                  </div>

                  <div className="modal-booking-summary__total-price">
                    <span className="currency">₹</span>
                    <span className="amount">{totalPrice.toLocaleString()}</span>
                  </div>

                  <div className="modal-booking-summary__taxes-info">
                    <span className="tax-amount">+₹ {totalTaxes.toLocaleString()}</span>
                    <span className="tax-label"> taxes and fees</span>
                  </div>
                </>
              )}

              <button
                className="modal-booking-summary__reserve-button"
                onClick={handleReserve}
                disabled={totalRooms === 0 || totalRooms < maxRoomsFromSearch}
              >
                Book Now
              </button>

              <div className="modal-booking-summary__no-charge-info">
                <i className="fa-solid fa-circle-check"></i>
                <span>You won&apos;t be charged yet</span>
              </div>
            </div>
          </div>
          </div>


        </div>
      </SlideFromRightModal>
    </div>
  );
}

export default RoomSelectionCard;