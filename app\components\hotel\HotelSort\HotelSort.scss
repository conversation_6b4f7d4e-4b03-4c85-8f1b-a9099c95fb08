@use '/styles/variable' as *;

.hotel-sort-desktop {
  .sort-tabs-container {
    display: flex;
    background: #fff;
    border: 1px solid #ddd;
    height: 44px;

    .sort-label {
      display: flex;
      align-items: center;
      padding: 0 16px;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      background: #f5f5f5;
      border-right: 1px solid #ddd;
      min-width: 80px;
    }

    .sort-tabs {
      display: flex;
      flex: 1;

      .sort-tab {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        padding: 0 16px;
        font-size: 14px;
        font-weight: 500;
        color: #666;
        cursor: pointer;
        border-right: 1px solid #ddd;
        background: #fff;
        transition: all 0.2s ease;

        &:last-child {
          border-right: none;
        }

        &:hover {
          background: #f8f8f8;
          color: #333;
        }

        &.active {
          background: $primary_color;
          color: #fff;
          font-weight: 600;
        }

        .sort-tab-text {
          font-size: 14px;
        }
      }
    }
  }
}

.hotel-sort-mobile {
  width: 100%;
  max-height: calc(100vh - 235px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;

  .sort-filter {
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:hover {
      background-color: rgba(0, 59, 149, 0.04);
    }

    &.active {
      color: $primary_color;
      background-color: rgba(0, 59, 149, 0.08);
      font-weight: 500;
    }

    .sort-option-text {
      flex: 1;
      font-size: 14px;
      line-height: 1.4;
    }

    .check-icon {
      color: $primary_color;
      flex-shrink: 0;
      margin-left: 8px;
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
