"use client";
import { useEffect } from "react";

export default function CashfreeScriptLoader() {
  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://sdk.cashfree.com/js/v3/cashfree.js";
    script.async = true;
    script.defer = true;

    script.onload = () => {
      console.log("Cashfree v3 SDK loaded successfully!");
    };

    script.onerror = (e) => {
      console.error("Failed to load the Cashfree SDK script.", e);
    };

    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return null;
}
