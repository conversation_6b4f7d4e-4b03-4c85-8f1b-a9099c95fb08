'use client';

import React, { useState, useEffect, useCallback } from 'react';
import MapWrapper from './MapWrapper';
import { MapMarker } from './Map';
// import { useLanguage } from '@/app/contexts/languageContext'; // Uncomment if needed for RTL support
import { useTranslation } from '@/app/hooks/useTranslation';
import { Hotel as HotelType } from '@/app/HotelSearchResult/hotel-search-result.model';
import { HotelFilterData } from '@/app/components/utilities/helpers/hotel/filterHotels';
import { addFilterInList } from '@/app/components/utilities/helpers/hotel/hotelFilterService';
import HotelFilter from '@/app/HotelSearchResult/components/HotelFilter/HotelFilter';
// Define a simple SelectedHotelCard component inline to avoid import issues
// You can replace this with the actual import when the issue is resolved
const SelectedHotelCard: React.FC<{
  hotel: HotelType;
  onClose: () => void;
}> = ({ hotel, onClose }) => {
  const { t } = useTranslation();

  return (
    <div className="selected-hotel-card" style={{
      backgroundColor: 'white',
      borderRadius: '8px',
      boxShadow: '0 5px 20px rgba(0, 0, 0, 0.15)',
      overflow: 'hidden',
      width: '100%',
      maxWidth: '350px'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '12px 15px',
        borderBottom: '1px solid #eee'
      }}>
        <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 600 }}>{hotel.name}</h3>
        <button
          onClick={onClose}
          style={{ background: 'none', border: 'none', cursor: 'pointer' }}
        >
          <i className="fa-solid fa-xmark"></i>
        </button>
      </div>

      <div style={{ padding: '15px' }}>
        <div style={{ marginBottom: '10px' }}>
          {[...Array(hotel.starRating || 0)].map((_, i) => (
            <i key={i} className="fa-solid fa-star" style={{ color: '#FFD700' }}></i>
          ))}
        </div>

        <div style={{ marginBottom: '10px' }}>
          <i className="fa-solid fa-location-dot" style={{ marginRight: '5px', color: '#0071c2' }}></i>
          <span>{hotel.address || ''}{hotel.address && hotel.city ? ', ' : ''}{hotel.city || ''}</span>
        </div>

        <div style={{ marginBottom: '10px' }}>
          <span style={{ fontWeight: 600, color: '#0071c2' }}>
            ₹ {hotel.fareDetail?.totalPrice || 0}
          </span>
        </div>
      </div>

      <div style={{
        padding: '12px 15px',
        borderTop: '1px solid #eee',
        textAlign: 'right'
      }}>
        <a
          href={hotel.hotelId ? `/HotelDetail?hotelId=${hotel.hotelId}` : '#'}
          target="_blank"
          style={{
            display: 'inline-block',
            padding: '8px 15px',
            backgroundColor: '#0071c2',
            color: 'white',
            borderRadius: '4px',
            textDecoration: 'none',
            fontWeight: 600
          }}
        >
          {t('map.viewDetails')}
        </a>
      </div>
    </div>
  );
};
import './FullScreenHotelSearchMap.scss';

interface FullScreenHotelSearchMapProps {
  isOpen: boolean;
  onClose: () => void;
  hotels: Array<HotelType>;
  destination?: {
    name: string;
    latitude: number;
    longitude: number;
  };
  filterData?: HotelFilterData; // Added back for the HotelFilter component
  onFilterChange?: (filterData: HotelFilterData | undefined) => void;
  onHotelSelect?: (hotelId: number) => void;
}

const FullScreenHotelSearchMap: React.FC<FullScreenHotelSearchMapProps> = ({
  isOpen,
  onClose,
  hotels = [],
  destination,
  filterData,
  onFilterChange,
  onHotelSelect
}) => {
  const { t } = useTranslation();
  // const { isRTL } = useLanguage(); // Uncomment if needed for RTL support
  // State for filtered hotels and filter panel
  const [filteredHotels, setFilteredHotels] = useState<HotelType[]>(hotels);
  const [selectedHotel, setSelectedHotel] = useState<HotelType | null>(null);
  const [mapCenter, setMapCenter] = useState<[number, number]>([0, 0]);
  const [zoom, setZoom] = useState(12);
  const [currentFilterData, setCurrentFilterData] = useState<HotelFilterData | undefined>(filterData);
  // Keeping searchQuery state for future use
  const [searchQuery] = useState('');



  // Update currentFilterData when filterData prop changes
  useEffect(() => {
    // Check if filterData is defined and has the expected structure
    if (filterData && typeof filterData === 'object') {
      // Check if filterData has the expected properties
      if (filterData.priceRange || filterData.starRatings || filterData.amenities) {
        setCurrentFilterData(filterData);
      }
    }
  }, [filterData]);

  // Initialize map center based on destination or first hotel
  useEffect(() => {
    if (destination) {
      setMapCenter([destination.latitude, destination.longitude]);
    } else if (hotels.length > 0 && hotels[0].geoLocationInfo) {
      setMapCenter([
        hotels[0].geoLocationInfo.lat || 0,
        hotels[0].geoLocationInfo.lon || 0
      ]);
    }
  }, [destination, hotels]);

  // Apply filters when filter data changes
  useEffect(() => {
    try {
      if (currentFilterData) {
        const filtered = addFilterInList(currentFilterData, hotels);
        setFilteredHotels(filtered);
      } else {
        setFilteredHotels(hotels);
      }
    } catch (error) {
      console.error('Error applying filters:', error);
      // Fallback to unfiltered hotels
      setFilteredHotels(hotels);
    }
  }, [currentFilterData, hotels]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilterData: HotelFilterData | undefined) => {
    setCurrentFilterData(newFilterData);
    // Notify parent component of filter changes
    if (onFilterChange) {
      onFilterChange(newFilterData);
    }
  }, [onFilterChange]);



  // Handle hotel selection from list
  const handleHotelClick = useCallback((hotel: HotelType) => {
    setSelectedHotel(hotel);
    if (hotel.geoLocationInfo) {
      setMapCenter([hotel.geoLocationInfo.lat, hotel.geoLocationInfo.lon]);
      setZoom(15); // Zoom in when a hotel is selected
    }
    if (onHotelSelect) {
      onHotelSelect(hotel.hotelId);
    }
  }, [onHotelSelect]);

  // Handle hotel selection from map
  const handleMarkerClick = useCallback((markerId: string) => {
    const hotel = hotels.find(h => h.hotelId.toString() === markerId);
    if (hotel) {
      setSelectedHotel(hotel);
      if (onHotelSelect) {
        onHotelSelect(hotel.hotelId);
      }
    }
  }, [hotels, onHotelSelect]);

  // Create map markers from filtered hotels
  const createMarkers = useCallback((): MapMarker[] => {
    try {
      return filteredHotels.map((hotel: HotelType) => {
        // Safety check for required properties
        if (!hotel.geoLocationInfo || !hotel.hotelId) {
          console.warn('Hotel missing required properties:', hotel);
          return null;
        }

        // Format price for display on marker
        const price = hotel.fareDetail?.totalPrice
          ? `₹${hotel.fareDetail.totalPrice.toLocaleString('en-IN')}`
          : '';

        return {
          id: hotel.hotelId.toString(),
          position: [
            hotel.geoLocationInfo?.lat || 0,
            hotel.geoLocationInfo?.lon || 0
          ],
          iconType: 'hotel',
          details: {
            id: hotel.hotelId.toString(),
            name: hotel.name || 'Unknown Hotel',
            rating: hotel.starRating || 0,
            price: price,
            showViewButton: true
          }
        };
      }).filter(Boolean) as MapMarker[]; // Filter out null values
    } catch (error) {
      console.error('Error creating markers:', error);
      return []; // Return empty array in case of error
    }
  }, [filteredHotels, selectedHotel]);

  // We're not using the search input in this version, but keeping the state for future use

  const filteredBySearch = searchQuery
    ? filteredHotels.filter((hotel: HotelType) =>
        hotel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        hotel.address?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        hotel.city?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : filteredHotels;

  if (!isOpen) return null;

  return (
    <div className={`fullscreen-hotel-search-map ${isOpen ? 'show' : ''}`}>
      <div className="fullscreen-map__header">
        <h2>{destination?.name || 'Goa'}</h2>
        <div className="header-buttons">
          <button className="close-btn" onClick={onClose} aria-label="Close map">
            <i className="fa-solid fa-xmark"></i>
          </button>
        </div>
      </div>

      <div className="fullscreen-map__content">
        {/* Hotel List Panel - Left Side */}
        <div className="hotel-list-panel">
          <div className="hotel-list-body">
            {filteredBySearch.map((hotel) => (
              <div
                key={hotel.hotelId}
                className={`hotel-list-item ${selectedHotel?.hotelId === hotel.hotelId ? 'selected' : ''}`}
                onClick={() => handleHotelClick(hotel)}
              >
                <div className="compact-hotel-card">
                  {/* Hotel amenities tags */}
                  <div className="amenity-tags">
                    <div className="amenity-tag">Free Wifi</div>
                    <div className="amenity-tag">Swimming pool at the property</div>
                  </div>

                  {/* Discount tag if applicable */}
                  {hotel.fareDetail && hotel.fareDetail.totalPrice < (hotel.fareDetail.displayedBaseFare ? hotel.fareDetail.displayedBaseFare : 0) && (
                    <div className="discount-tag">
                      <span>Discount of ₹{(hotel.fareDetail.displayedBaseFare || 0 - hotel.fareDetail.totalPrice).toFixed(0)} included. Coupon code NEW15 applied!</span>
                    </div>
                  )}

                  {/* Last minute deal tag */}
                  {hotel.fomoTags?.some(tag => tag.fomoType === "LAST_MINUTE_DEAL") && (
                    <div className="last-minute-tag">
                      <i className="fa-regular fa-clock"></i> Last Minute Deal
                    </div>
                  )}

                  {/* Hotel image and details */}
                  <div className="hotel-card-content">
                    <div className="hotel-image">
                      {hotel?.imageInfoList?.length > 0 ? (
                        <img
                          src={hotel.imageInfoList[0].url}
                          alt={hotel.name}
                        />
                      ) : (
                        <div className="no-image">{t("hotel.card.noImageFound")}</div>
                      )}
                    </div>
                    <div className="hotel-details">
                      <h3 className="hotel-name">{hotel.name}</h3>
                      <div className="hotel-rating">
                        {Array.from({ length: Math.max(0, hotel?.starRating || 0) }).map((_, index) => (
                          <i key={index} className="fa-solid fa-star"></i>
                        ))}
                      </div>
                      <div className="hotel-location">
                        <span>{hotel.address || ''}{hotel.address && hotel.city ? ', ' : ''}{hotel.city || ''}</span>
                      </div>
                      <div className="hotel-price">
                        {/* Show original price if we have a discount */}
                        {hotel.fareDetail && (hotel.fareDetail.displayedBaseFare ?? 0) > hotel.fareDetail.totalPrice && (
                          <span className="original-price">₹{hotel.fareDetail.displayedBaseFare}</span>
                        )}
                        <span className="current-price">₹{hotel.fareDetail?.totalPrice || 0}</span>
                        <span className="price-details">+₹{hotel.taxesAndCharges || 0} taxes & fees per night, per room</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {filteredBySearch.length === 0 && (
              <div className="no-hotels-found">
                <p>{t('map.noHotelsFound')}</p>
              </div>
            )}
          </div>
        </div>

        {/* Map Panel - Center */}
        <div className="map-panel">
          <MapWrapper
            center={mapCenter}
            zoom={zoom}
            markers={createMarkers()}
            style={{ height: '100%', width: '100%' }}
            autoCenter={false}
            fitBounds={!selectedHotel && filteredHotels.length > 1}
            onMarkerClick={handleMarkerClick}
          />

          {/* Selected Hotel Card */}
          {selectedHotel && (
            <div className="selected-hotel-card-container">
              <SelectedHotelCard
                hotel={selectedHotel}
                onClose={() => setSelectedHotel(null)}
              />
            </div>
          )}
        </div>

        {/* Filter Panel - Right Side - Always Visible */}
        <div className="filter-panel visible">
          <div className="filter-panel__header">
            <h3>{t('filters.filterBy')}</h3>
          </div>
          <div className="filter-panel__content">
            {currentFilterData && (
              <HotelFilter
                isSearchList= {false}
                initialFilterData={currentFilterData}
                handleChange={handleFilterChange}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FullScreenHotelSearchMap;
