"use client";
import React, { useCallback, useEffect, useState } from "react";
import "./ReviewsAndRatings.scss";
import Image from "next/image";
import Link from "next/link";
import SlideFromRightModal from "../../SlideFromRightModal/SlideFromRightModal";
import useScrollLock from "@/app/components/utilities/ScrollLock/useScrollLock";
import { RatingView } from "@/app/HotelDetail/hotel-detail-result.model";
import { useCommonContext } from "@/app/contexts/commonContext";
import { Data, ReviewList } from "@/app/HotelDetail/hotel-review-list-model";
import { HotelDetailApiResponse, Review as BackendReview } from "@/app/HotelDetail/hotel-detail-api-response.model";

// const categories = [
//   { label: "All Reviews", count: 262 },
//   { label: "Couple", count: 155 },
//   { label: "Family with young children", count: 49 },
//   { label: "Group", count: 29 },
//   { label: "Family with teens", count: 16 },
//   { label: "Solo traveler", count: 10 },
//   { label: "Business traveler", count: 3 },
// ];

interface ReviewsAndRatingsProps {
  ratingView?: RatingView;
  hotelData?: HotelDetailApiResponse; // Backend data
  reviews?: BackendReview[]; // Direct reviews array
}

// Static fallback data for reviews when no backend data is available
const createStaticFallbackReviews = (): ReviewList[] => [
  {
    title: "No Reviews Available",
    category: "General",
    location: "",
    reviewComment: "No reviews are currently available for this hotel. Please check back later.",
    reviewDate: "N/A",
    avgRating: "0",
    countryCode: "",
    reviewerName: "System"
  }
];

function ReviewsAndRatings({
  ratingView,
  hotelData,
  reviews
}: ReviewsAndRatingsProps) {
  const [activeCategory, setActiveCategory] = useState<string>();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isReviewFilterBtnActive, setIsReviewFilterBtnActive] =
    useState<boolean>(false);
  const [isreviewFilterDropDownVisible, setIsReviewFilterDropDownVisible] =
    useState<boolean>(false);
  const [selectedFilter, setSelectedFilter] = useState("Highest Rated");
  const [hotelReviewsData, setHotelReviewsData] = useState<Data>();
  const [sortedReviews, setSortedReviews] = useState<any[]>([]);

  const filterOptions = ["Most Recent", "Highest Rated", "Lowest Rated"];
  const { setIsLoading } = useCommonContext();

  // Determine data source (priority: backend data > direct props > legacy JSON)
  const backendReviews = hotelData?.reviews || reviews || [];
  const hasBackendData = backendReviews.length > 0;



  // Create enhanced rating view from backend data
  const getEnhancedRatingView = useCallback(() => {
    if (!hasBackendData) return ratingView;

    const backendRating = hotelData?.userRating;
    const backendCategory = hotelData?.userRatingCategoty; // Note: API has typo

    return {
      ...ratingView,
      averageRating: backendRating || ratingView?.averageRating || "0",
      ratingCount: backendReviews.length || ratingView?.ratingCount || 0,
      totalReviewCount: backendReviews.length || ratingView?.totalReviewCount || 0
    };
  }, [hasBackendData, ratingView, hotelData?.userRating, hotelData?.userRatingCategoty, backendReviews.length]);

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setIsReviewFilterDropDownVisible(false);
  }, []);

  const handleReviewFilterBtnClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation(); // Prevents event bubbling
    setIsReviewFilterBtnActive(true);
    setIsReviewFilterDropDownVisible(true);
  }, []);

  const handleFilterSelection = useCallback((option: string) => {
    setSelectedFilter(option);
    setIsReviewFilterDropDownVisible(false);
    setIsReviewFilterBtnActive(false);

    // Get reviews to sort (filtered by category or all)
    const reviewsToSort = hotelReviewsData?.reviewList || [];
    const reviewsToFilter = activeCategory
      ? reviewsToSort.filter((review) => review.category === activeCategory)
      : reviewsToSort;

    // Create a copy for sorting
    let sorted = [...reviewsToFilter];

    switch(option) {
      case "Most Recent":
        sorted = sorted.sort((a, b) => {
          const dateA = new Date(a.reviewDate);
          const dateB = new Date(b.reviewDate);
          return dateB.getTime() - dateA.getTime(); // Newest first
        });
        break;

      case "Highest Rated":
        sorted = sorted.sort((a, b) => {
          const ratingA = parseFloat(a.avgRating || "0");
          const ratingB = parseFloat(b.avgRating || "0");
          return ratingB - ratingA; // Highest first
        });
        break;

      case "Lowest Rated":
        sorted = sorted.sort((a, b) => {
          const ratingA = parseFloat(a.avgRating || "0");
          const ratingB = parseFloat(b.avgRating || "0");
          return ratingA - ratingB; // Lowest first
        });
        break;

      default:
        // Default to highest rated
        sorted = sorted.sort((a, b) => {
          const ratingA = parseFloat(a.avgRating || "0");
          const ratingB = parseFloat(b.avgRating || "0");
          return ratingB - ratingA;
        });
    }

    // Update sorted reviews state
    setSortedReviews(sorted);
  }, [hotelReviewsData?.reviewList, activeCategory]);

  useScrollLock(isModalOpen);

  const getReviewData = useCallback(async () => {
    if (hasBackendData) {
      // Use backend data - transform backend reviews to match legacy format
      const transformedReviews = backendReviews.map((review, index) => ({
        title: `Review ${index + 1}`, // Backend doesn't have titles
        category: review.type || "General", // Use type as category
        location: "Unknown", // Backend doesn't have location
        reviewComment: review.content,
        reviewDate: "Recent", // Backend doesn't have dates
        avgRating: review.rating.toString(),
        countryCode: "in", // Default country code
        reviewerName: "Anonymous" // Backend doesn't have reviewer names
      }));

      const mockData = {
        reviewList: transformedReviews,
        moreResultsAvailable: false,
        fallbackReviews: false
      };

      setHotelReviewsData(mockData);

      // Initialize sorted reviews with default sorting
      const sortedInitialReviews = [...transformedReviews].sort((a, b) => {
        const ratingA = parseFloat(a.avgRating || "0");
        const ratingB = parseFloat(b.avgRating || "0");
        return ratingB - ratingA; // Default to highest rated
      });
      setSortedReviews(sortedInitialReviews);
      setIsLoading(false);
    } else {
      // Use static fallback data when no backend data is available
      const staticFallbackReviews = createStaticFallbackReviews();
      const staticFallbackData: Data = {
        moreResultsAvailable: false,
        reviewList: staticFallbackReviews,
        fallbackReviews: true
      };

      setHotelReviewsData(staticFallbackData);
      setSortedReviews(staticFallbackReviews);
      setIsLoading(false);
    }
  }, [hotelData?.reviews, reviews]);

  useEffect(() => {
    getReviewData();
  }, [getReviewData]);

  const reviewList = hotelReviewsData?.reviewList || [];

  const categories = Array.from(
    reviewList.reduce((map, review) => {
      if (review.category && !map.has(review.category)) {
        map.set(review.category, { label: review.category, count: 1 });
      } else if (review.category) {
        map.get(review.category)!.count += 1;
      }
      return map;
    }, new Map<string, { label: string; count: number }>())
  ).map(([_, value]) => value);

  const filteredReviews = activeCategory
    ? reviewList.filter((review) => review.category === activeCategory)
    : reviewList;

  useEffect(() => {
    if (hotelReviewsData?.reviewList) {
      handleFilterSelection(selectedFilter);
    }
  }, [activeCategory, hotelReviewsData, handleFilterSelection, selectedFilter]);

  // Use enhanced rating view that includes backend data
  const enhancedRatingView = getEnhancedRatingView();

  return (
    <>
      {enhancedRatingView && (
        <div className="ratings-reviews-container">
          <div className="ratings">
            <div className="rating-summary">
              <div className="rating-summary__score">
                {enhancedRatingView?.averageRating}
              </div>
              <div className="rating-summary__details">
                <span className="rating-summary__label">
                  {(() => {
                    const rating = parseFloat(enhancedRatingView?.averageRating || "0");
                    if (rating >= 9) return "Excellent";
                    if (rating >= 8) return "Very Good";
                    if (rating >= 7) return "Good";
                    if (rating >= 6) return "Fair";
                    return "Poor";
                  })()}
                </span>

                <span className="rating-summary__count">
                  {enhancedRatingView?.ratingCount} Ratings
                </span>
              </div>
            </div>

            <div className="rating-bar-container">
              {Object.entries(enhancedRatingView?.hotelFeaturesRating || {})
                .filter(([_, value]) => parseFloat(value) > 0)
                .map(([feature, score]) => {
                  const numericScore = parseFloat(score);
                  const percentage = (numericScore / 10) * 100;

                  return (
                    <div className="rating-bar" key={feature}>
                      <div className="rating-bar__progress">
                        <div
                          className="rating-bar__fill"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      <p className="rating-bar__label-score">
                        <span>{feature}</span>
                        <span>{numericScore.toFixed(1)}</span>
                      </p>
                    </div>
                  );
                })}
            </div>

            <div className="hotel-highlights">
              <h3 className="hotel-highlights__title">
                What people love about this hotel{" "}
                <i className="fa-regular fa-thumbs-up"></i>
              </h3>

              <div className="hotel-highlights__tags">
                {enhancedRatingView?.hotelFeaturesRatingV2?.map(
                  (hotelfeature, index) => (
                    <div key={index} className="hotel-highlights__tag">
                      <span>{hotelfeature?.title}</span>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
          <div className="reviews">
            <div className="review-filter">
              <h3 className="review-filter__title">
                {`${hotelReviewsData?.reviewList.length} Reviews` || ""}
              </h3>

              <div className="review-filter__categories">
                {categories.map((category) => (
                  <div
                    key={category.label}
                    className={`review-filter__category ${
                      activeCategory == category.label
                        ? "review-filter__category active"
                        : ""
                    }`}
                    onClick={() => setActiveCategory(category.label)}
                  >
                    <span>
                      {category.label} ({category.count})
                    </span>
                  </div>
                ))}
              </div>
            </div>
            <div className="review-container">
              {filteredReviews.slice(0, 4).map((review, idx) => (
                <div
                  key={idx}
                  className="review"
                  onClick={() => setIsModalOpen(true)}
                >
                  <div className="review-title">
                    {review?.title || "No title"}{" "}
                    <span className="review-rating">
                      {review?.avgRating || "No rating"}
                    </span>
                  </div>
                  <p className="review-text">{review?.reviewComment}</p>
                  <div className="review-user-meta">
                    {review?.countryCode && (
                      <div className="flagImg">
                        <Image
                          src="https://hatscripts.github.io/circle-flags/flags/in.svg"
                          alt="flag image"
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          style={{ objectFit: "cover" }}
                        />
                      </div>
                    )}

                    <p>
                      {" "}
                      <span>{review?.category}</span>
                      {review?.location && <span>{review?.location}</span>}
                      <span>{review?.reviewDate}</span>
                    </p>
                  </div>
                </div>
              ))}
         
              <Link
                href={""}
                onClick={() => setIsModalOpen(true)}
                className="reviewLink"
              >
                View All Reviews
              </Link>
            </div>
          </div>

          <div className={`detail-page-overlay ${window.innerWidth <= 940 && isModalOpen ? 'show' : ''}`}>

          </div>


          <SlideFromRightModal
            isOpen={isModalOpen}
            handleClose={handleCloseModal}
            title="User Reviews"
          >
            <div className="rating__container__modal">
              <div className="rating__details">
                <div className="count">{enhancedRatingView?.averageRating}</div>

                <div className="details">
                  <p className="detail detail1">
                    {" "}
                    {(() => {
                      const rating = parseFloat(
                        enhancedRatingView?.averageRating || "0"
                      );
                      if (rating >= 9) return "Excellent";
                      if (rating >= 8) return "Very Good";
                      if (rating >= 7) return "Good";
                      if (rating >= 6) return "Fair";
                      return "Poor";
                    })()}
                  </p>
                  <p className="detail detail2">
                    {hotelReviewsData?.reviewList.length} Ratings
                  </p>
                </div>
              </div>
              <div className="rating-bar-container__modal">
                {Object.entries(enhancedRatingView?.hotelFeaturesRating || {})
                  .filter(([_, value]) => parseFloat(value) > 0)
                  .map(([feature, score]) => {
                    const numericScore = parseFloat(score);
                    const percentage = (numericScore / 10) * 100;

                    return (
                      <div className="rating-bar" key={feature}>
                        <div className="rating-bar__progress">
                          <div
                            className="rating-bar__fill"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <p className="rating-bar__label-score">
                          <span>{feature}</span>
                          <span>{numericScore.toFixed(1)}</span>
                        </p>
                      </div>
                    );
                  })}
              </div>

              <div className="reviews__modal">
                <div className="review-filter__modal">
                  <div className="review-filter__modal__header">
                    <h3 className="review-filter__modal__title">
                  
                      {`${hotelReviewsData?.reviewList.length} Reviews` || ""}
                    </h3>

                    {/* <div
                      className={`review-filter__modal__review-filter-button ${
                        isReviewFilterBtnActive ? "active" : ""
                      }`}
                      onClick={handleReviewFilterBtnClick}
                    >
                      <input
                        type="text"
                        readOnly
                        value={selectedFilter}
                        className="input-field"
                      />
                      <span className="material-icons arrowDown">
                        keyboard_arrow_down
                      </span>

                      <div
                        className={`review-filter__modal__review-drop-down ${
                          isreviewFilterDropDownVisible ? "active" : ""
                        }`}
                      >
                        {filterOptions?.map((option) => (
                          <div
                            className="drop-down-item"
                            key={option}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleFilterSelection(option);
                            }}
                          >
                            <div className="icon">
                              {selectedFilter === option && (
                                <i className="fa-solid fa-check"></i>
                              )}
                            </div>
                            <span className="label">{option}</span>
                          </div>
                        ))}
                      </div>
                    </div> */}

                     <div
                      className={`review-filter__modal__review-filter-button ${
                        isReviewFilterBtnActive ? "active" : ""
                      }`}
                      onClick={handleReviewFilterBtnClick}
                    >
                      <input
                        type="text"
                        readOnly
                        value={selectedFilter}
                        className="input-field"
                      />
                      <span className="material-icons arrowDown">
                        keyboard_arrow_down
                      </span>

                      <div
                        className={`review-filter__modal__review-drop-down ${
                          isreviewFilterDropDownVisible ? "active" : ""
                        }`}
                      >
                        {filterOptions?.map((option) => (
                          <div
                            className="drop-down-item"
                            key={option}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleFilterSelection(option);
                            }}
                          >
                            <div className="icon">
                              {selectedFilter === option && (
                                <i className="fa-solid fa-check"></i>
                              )}
                            </div>
                            <span className="label">{option}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {isreviewFilterDropDownVisible && (
                      <div
                        onClick={() => {
                          setIsReviewFilterBtnActive(false);
                          setIsReviewFilterDropDownVisible(false);
                        }}
                        className="review-filter__modal__review-drop-down-overlay"
                      ></div>
                    )}
                  </div>

                  <div className="review-filter__modal__categories">
                    {categories.map((category) => (
                      <div
                        key={category.label}
                        className={`review-filter__modal__category ${
                          activeCategory == category.label
                            ? "review-filter__modal__category active"
                            : ""
                        }`}
                        onClick={() => setActiveCategory(category.label)}
                      >
                        <span>
                          {category.label} ({category.count})
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="review-container__modal">
                  {sortedReviews?.map((review, idx) => (
                    <div
                      className="review__modal"
                      onClick={() => setIsModalOpen(true)}
                      key={idx}
                    >
                      <div className="review-title">
                        {review?.title || "No title"}{" "}
                        <span className="review-rating">
                          {review?.avgRating || "No rating"}
                        </span>
                      </div>
                      <p className="review-text">{review?.reviewComment}</p>
                      <div className="review-user-meta">
                        {review?.countryCode && (
                          <div className="flagImg">
                            <Image
                              src="https://hatscripts.github.io/circle-flags/flags/in.svg"
                              alt="flag image"
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                              fill
                              style={{ objectFit: "cover" }}
                            />
                          </div>
                        )}

                        <p>
                          {" "}
                          <span>{review?.category}</span>
                          {review?.location && <span>{review?.location}</span>}
                          <span>{review?.reviewDate}</span>
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </SlideFromRightModal>
        </div>
      )}
    </>
  );
}

export default ReviewsAndRatings;
