/* Shimmer Animation */
.shimmer {
  background: linear-gradient(90deg, #f6f7f8 25%, #eaeaea 50%, #f6f7f8 75%);
  background-size: 200% 100%;
  animation: shimmer-animation 1.5s infinite linear;
}

@keyframes shimmer-animation {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Hotel Card Shimmer Specific Styles */
.hotel-card-shimmer {
  transition: all 0.3s ease;

  &--grid {
    /* Grid specific styles */
    @media (max-width: 768px) {
      max-width: 100%;
      margin: 0 0 1rem 0;
    }
  }

  &--list {
    /* List specific styles */
    width: 100%;

    @media (max-width: 768px) {
      margin-bottom: 1rem;
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .flex {
      &.md\\:flex-row {
        flex-direction: column !important;
      }
    }

    /* Ensure proper spacing on mobile */
    .p-4.md\\:p-6 {
      padding: 1rem !important;
    }
  }
}
