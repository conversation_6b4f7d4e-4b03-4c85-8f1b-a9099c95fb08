import { HotelBookingRequest } from "@/app/BookingPreviewPage/hotel-booking-api.model";
import apiService from "../api-service";
import { CreateBookingResponse } from "@/models/hotel/booking.model";

/**
 * Service for creating hotel bookings
 * This API call is made when user clicks Pay Now button after filling guest details
 */
// export const createHotelBooking = async ( payload: HotelBookingRequest): Promise<HotelBookingResponse> => {
//   const response = await apiService.post2<HotelBookingResponse>(
//     "api/v1/booking/zentrumhub/hotel/book",
//     payload
//   );

//   return response;
// };


export const createBookingApi = async ( payload: HotelBookingRequest): Promise<CreateBookingResponse> => {
  const response = await apiService.post3<CreateBookingResponse>(
    "api/v1/booking/zentrumhub/hotel/book",
    payload
  );

  return response;
};
