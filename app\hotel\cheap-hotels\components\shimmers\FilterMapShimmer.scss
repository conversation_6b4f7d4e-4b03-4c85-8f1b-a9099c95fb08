/* Shimmer animation keyframes */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Custom shimmer animation class */
.animate-shimmer {
  animation: shimmer 1.5s infinite;
}

/* Ensure shimmer containers have proper overflow hidden */
.shimmer-container {
  position: relative;
  overflow: hidden;
  background-color: #e5e7eb; /* gray-200 */
}

/* Main shimmer container with white background */
.shimmer-container.bg-white {
  background-color: white !important;
}

.shimmer-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
  transform: translateX(-100%);
  animation: shimmer 1.5s infinite;
}

/* Button shimmer specific styles */
.button-shimmer {
  position: relative;
  overflow: hidden;
  background-color: #d1d5db; /* gray-300 */
}

.button-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.8),
    transparent
  );
  transform: translateX(-100%);
  animation: shimmer 1.5s infinite;
}
