import React, { useMemo, useState } from 'react'
import RecommendationListHeader, { FiltersState } from './components/recommendation-list-header/RecommendationListHeader';

function RecommendationList() {
    const [filteredList, setFilteredList] = useState<[]>([]);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [filters, setFilters] = useState<FiltersState>({
        breakfast: false,
        fullBoard: false,
        halfBoard: false,
        transfer: false,
        cancellationAvailable: false,
    });

    const handleResetFilters = () => {
        setFilters({
          breakfast: false,
          fullBoard: false,
          halfBoard: false,
          transfer: false,
          cancellationAvailable: false,
        });
        setSearchQuery('');
    };

    
  const filteredRoomsData = useMemo(() => {
    // Your actual filtering logic would be applied here
    return [];
  }, []);

  return (
    <div className='max-w-6xl mx-auto p-4'>
      <RecommendationListHeader
        filters={filters}
        setFilters={setFilters}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        handleResetFilters={handleResetFilters}
        filteredRoomsData={filteredRoomsData}
      />
      
      {/* list */}
      <div className="space-y-8">
        {filteredList && filteredList.length > 0 ? (
          <div>
            
          </div>
        ):(
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No rooms found</h3>
            <p className="text-gray-500 mb-4">
              No rooms match your current search and filter criteria.
            </p>
            <button
              onClick={handleResetFilters}
              className="bg-primary-color hover:bg-primary-color-dark text-white px-4 py-2 rounded-lg transition-colors"
            >
              Clear filters
            </button>
          </div>
        )}
      </div>

    </div>
  )
}

export default RecommendationList