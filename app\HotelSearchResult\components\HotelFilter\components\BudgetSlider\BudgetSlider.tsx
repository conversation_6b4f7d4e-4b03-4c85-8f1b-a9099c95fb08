"use client";
import React from "react";
import "./BudgetSlider.scss";
import { useTranslation } from "@/app/hooks/useTranslation";

interface PriceRange {
  min: number;
  max: number;
  values: {
    minimum: number;
    maximum: number;
  };
}

interface BudgetSliderProps {
  priceRange: PriceRange;
  onMinChange: (value: number) => void;
  onMaxChange: (value: number) => void;
  currency?: string;
  histogramData?: number[];
}

function BudgetSlider({ 
  priceRange, 
  onMinChange, 
  onMaxChange, 
  currency = "₹",
  histogramData = Array(40).fill(0).map(() => Math.floor(Math.random() * 40) + 5) // Default mock data
}: BudgetSliderProps) {
  const { t } = useTranslation();

  const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Math.min(Number(e.target.value), priceRange.values.maximum - 100);
    onMinChange(value);
  };

  const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Math.max(Number(e.target.value), priceRange.values.minimum + 100);
    onMaxChange(value);
  };

  // Normalize histogram data to fit visual space
  const maxHistogramValue = Math.max(...histogramData);
  const barWidth = 100 / histogramData.length;

  return (
    <div className="budgetSlider">
      <h3>{t("filters.budget.title") || "Your budget (per night)"}</h3>
      <p>
        {currency} {priceRange.values.minimum || 0} - {currency} {priceRange.values.maximum || "20,000+"}
      </p>
      
      <div className="sliderContainer">
        <svg 
          className="histogram" 
          viewBox={`0 0 ${histogramData.length} 40`}
          preserveAspectRatio="none"
        >
          {histogramData.map((value, index) => {
            const safeValue = isNaN(value) || value < 0 ? 0 : value; // sanitize first
            const barHeight = maxHistogramValue > 0 ? (safeValue / maxHistogramValue) * 40 : 0;
            const y = 40 - barHeight;

            return (
              <rect
                key={index}
                x={index}
                y={isNaN(y) ? 0 : y}
                width="1"
                height={isNaN(barHeight) ? 0 : barHeight}
                fill="rgba(100, 100, 100)"
                opacity={0.25 - (index / histogramData.length) * 0.1}
              />
            );
          })}
        </svg>


        <div className="sliderTrack"></div>
        <div 
          className="sliderProgress"
          style={{
            left: `${((priceRange.values.minimum - priceRange.min) / (priceRange.max - priceRange.min)) * 100}%`,
            width: `${((priceRange.values.maximum - priceRange.values.minimum) / (priceRange.max - priceRange.min)) * 100}%`
          }}
        ></div>

        <input
          type="range"
          min={priceRange.min}
          max={priceRange.max}
          value={priceRange.values.maximum}
          onChange={handleMaxChange}
          className="slider maxSlider"
        />
        <input
          type="range"
          min={priceRange.min}
          max={priceRange.max}
          value={priceRange.values.minimum}
          onChange={handleMinChange}
          className="slider minSlider"
        />
      </div>
    </div>
  );
}

export default BudgetSlider;