import { HotelSearchFormData, SearchRoom } from "@/models/hotel/search-page.model";

export function buildHotelDetailUrl(hotelId: number, data: HotelSearchFormData): string {
  const parts: string[] = [];

  // Always push hotelId
  parts.push(hotelId.toString());

  // Location info
  if (data.searchQuery) parts.push(encodeURIComponent(data.searchQuery));
  if (data.fullLocationData) parts.push(encodeURIComponent(data.fullLocationData));
  if (data.locationId) parts.push(data.locationId);

  // GeoCode
  if (data.geoCode?.lat) parts.push(data.geoCode.lat);
  if (data.geoCode?.long) parts.push(data.geoCode.long);

  // Dates
  if (data.checkInDate) parts.push(data.checkInDate);
  if (data.checkOutDate) parts.push(data.checkOutDate);

  // Travelers summary
  if (data.travelers) {
    parts.push(`adults-${data.travelers.adults}`);
    parts.push(`rooms-${data.travelers.rooms}`);
    parts.push(`children-${data.travelers.children}`);
  }

  // Detailed rooms
  if (data.roomsData?.length) {
    const roomsParam = data.roomsData
      .map(
        (room, index) =>
          `Room${index}-${room.adults}-${room.children}${
            room.childrenAges.length
              ? "-" + room.childrenAges.map((c) => c.age).join(",")
              : ""
          }`
      )
      .join("_");
    parts.push(roomsParam);
  }

  return `/hotel/details/${parts.join("/")}`;
}



// Always normalize to string
const normalizeParam = (value: string | string[] | undefined): string => {
  if (!value) return "";
  return Array.isArray(value) ? value[0] : value;
};

export const extractHotelSearchFormData = (params: Record<string, string | string[]>): HotelSearchFormData => {
  const roomsParam = normalizeParam(params.roomsParam);

  const roomsData: SearchRoom[] = roomsParam
    ? roomsParam.split("_").map((roomString: string, index: number) => {
        const parts = roomString.split("-");
        const adults = parseInt(parts[1] || "0", 10);
        const children = parseInt(parts[2] || "0", 10);

        const childrenAges =
          parts[3] && parts[3] !== ""
            ? parts[3].split(",").map((age, i) => ({ id: i, age: parseInt(age, 10) }))
            : [];

        return { id: index, adults, children, childrenAges };
      })
    : [];

  return {
    searchQuery: decodeURIComponent(normalizeParam(params.city)),
    fullLocationData: decodeURIComponent(normalizeParam(params.state)),
    locationId: normalizeParam(params.locationId),
    geoCode:
      normalizeParam(params.lat) && normalizeParam(params.long)
        ? { lat: normalizeParam(params.lat), long: normalizeParam(params.long) }
        : undefined,
    checkInDate: normalizeParam(params.checkIn) || null,
    checkOutDate: normalizeParam(params.checkOut) || null,
    travelers: {
      adults: roomsData.reduce((sum, r) => sum + r.adults, 0),
      rooms: roomsData.length,
      children: roomsData.reduce((sum, r) => sum + r.children, 0),
    },
    roomsData,
  };
};
