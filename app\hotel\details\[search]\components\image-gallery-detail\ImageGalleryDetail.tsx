import React, { useState, useMemo } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import "./image-gallery-detail.scss";
import { HotelDetailApiResponse } from "@/models/hotel/detail-page.model";
import ImageShowCase from "../image-showcase/ImageShowCase";
import { CarouselImage } from "../../../detail-page-internal.model";

interface ImageGalleryDetailProps {
  isOpen: boolean;
  onClose: () => void;
  hotelData?: HotelDetailApiResponse;
}

const ImageGalleryDetail: React.FC<ImageGalleryDetailProps> = ({
  isOpen,
  onClose,
  hotelData,
}) => {
  const router = useRouter();
  const [isImageLightBoxActive, setIsImageLightBoxActive] = useState<boolean>(false);

  // Memoize categorized images to prevent re-computation on every render
  const categorizedImages = useMemo(() => {
    if (!hotelData?.images) return {};
    const categories: { [key: string]: string[] } = {};
    hotelData.images.forEach(image => {
      const category = image.image_category_type || 'Overview';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(image.image_path);
    });
    return categories;
  }, [hotelData?.images]);

  const availableTabs = Object.keys(categorizedImages);
  const [activeTab, setActiveTab] = useState<string>(availableTabs[0] || "");

  // Memoize showcase images to prevent re-mapping on every render
  const showcaseImages: CarouselImage[] = useMemo(() => {
    if (!hotelData?.images) return [];
    return hotelData.images.map(img => ({
      pictureId: img.id.toString(),
      url: img.image_path,
      urlHigh: img.image_path,
      urlMedium: img.image_path,
      urlLow: img.image_path,
      caption: img.alt_text,
      imageCategory: img.image_category_type,
      rank: null,
      srpRank: img.sort_order,
      detailRank: img.sort_order,
      categoryRank: img.sort_order,
      withinCategoryRank: img.sort_order
    }));
  }, [hotelData?.images]);

  // Memoize ratings to prevent re-calculation on every render
  const ratings = useMemo(() => {
    if (!hotelData) return null;
    const reviewCount = hotelData.reviews?.length || 0;
    const averageReviewRating = reviewCount > 0
      ? hotelData.reviews.reduce((sum, review) => sum + review.rating, 0) / reviewCount
      : 0;

    return {
      overall: parseFloat(hotelData.userRating || "0"),
      reviews: reviewCount,
      categories: {
        "Overall Rating": parseFloat(hotelData.userRating || "0"),
        "Star Rating": (hotelData.starRating || 0) * 2,
        "Reviews": averageReviewRating,
        "Facilities": Math.min(10, (hotelData.facilities?.length || 0) * 1.5),
        "Amenities": Math.min(10, (hotelData.amenities?.length || 0) * 0.8),
        "Location": parseFloat(hotelData.userRating || "0"),
      }
    };
  }, [hotelData]);

  if (!isOpen || !hotelData) return null;

  const handleReserveBtn = () => {
    router.push('/BookingPreviewPage');
  };

  return (
    <div className="image-gallery-detail">
      <div className="image-gallery-detail__container">
        <header className="image-gallery-detail__header">
          {isImageLightBoxActive ? (
            <button onClick={() => setIsImageLightBoxActive(false)} className="image-gallery-detail__go-to-prevpage-btn">
              <i className="fa-solid fa-arrow-left"></i> Gallery
            </button>
          ) : <div />}
          <div className="image-gallery-detail__header-center">
            <div className="image-gallery-detail__title">{hotelData.name}</div>
            <button className="image-gallery-detail__reserve-btn" onClick={handleReserveBtn}>
              Reserve now
            </button>
          </div>
          <button className="image-gallery-detail__close-btn" onClick={onClose}>
            Close <i className="fa-solid fa-xmark"></i>
          </button>
        </header>

        <main className="image-gallery-detail__content">
          {isImageLightBoxActive ? (
            <div className="image-gallery-detail__showcase-container">
              <ImageShowCase images={showcaseImages} />
            </div>
          ) : (
            <div className="image-gallery-detail__gallery">
              <nav className="image-gallery-detail__tabs">
                {availableTabs.map((tab) => {
                  const thumbnailImage = categorizedImages[tab]?.[0] || "/api/placeholder/112/80";
                  const isActive = activeTab === tab;
                  return (
                    <div
                      key={tab}
                      className={`image-gallery-detail__tab ${isActive ? "active" : ""}`}
                      onClick={() => setActiveTab(tab)}
                    >
                      <div className={`image-gallery-detail__tab-image ${isActive ? "active" : ""}`}>
                        <Image src={thumbnailImage} alt={tab} width={112} height={80} objectFit="cover" />
                      </div>
                      <div className={`image-gallery-detail__tab-name ${isActive ? "active" : ""}`}>
                        {tab}
                      </div>
                    </div>
                  );
                })}
              </nav>
              <div className="image-gallery-detail__gallery-row">
                {categorizedImages[activeTab]?.map((imageUrl, index) => (
                  <div key={index} className="image-gallery-detail__gallery-item" onClick={() => setIsImageLightBoxActive(true)}>
                    <div className="image-container">
                      <Image src={imageUrl} alt={`${activeTab} image ${index + 1}`} layout="fill" objectFit="cover" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {ratings && (
            <aside className="image-gallery-detail__sidebar">
              <div className="image-gallery-detail__rating">
                <div className="rating-score">{ratings.overall.toFixed(1)}</div>
                <div className="rating-text">
                  <div className="rating-label">{hotelData.userRatingCategoty || "Good"}</div>
                  <div className="rating-reviews">{ratings.reviews} reviews</div>
                </div>
              </div>
              <div className="image-gallery-detail__categories">
                <h3>Categories:</h3>
                {Object.entries(ratings.categories).map(([category, score]) => (
                  <div key={category} className="category-item">
                    <div className="category-name-score-wrapper">
                      <div className="category-name">
                        {category}
                        {score === 10 && <i className="fa-solid fa-arrow-up category-icon"></i>}
                      </div>
                      <div className="category-score">{score.toFixed(1)}</div>
                    </div>
                    <div className="category-score-container">
                      <div
                        className={`category-score-bar ${score >= 9.5 ? "highscore" : score <= 6 ? "lowscore" : ""}`}
                        style={{ width: `${score * 10}%` }}
                      />
                    </div>
                  </div>
                ))}
                <div className="score-note-container">
                  <div className="score-note">
                    <i className="fa-solid fa-arrow-up highscore"></i> High score for {hotelData.city}
                  </div>
                  <div className="score-note">
                    <i className="fa-solid fa-arrow-down lowscore"></i> Low score for {hotelData.city}
                  </div>
                </div>
              </div>
            </aside>
          )}
        </main>
      </div>
    </div>
  );
};

export default ImageGalleryDetail;