@use "/styles/variable" as *;



// Traveler selector container
.traveler-selector-container {
  border: 1px solid $border-color;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100%;

  // Scrollable rooms container
  .rooms-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100vh - 200px); // Adjust based on header and footer height

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    // Firefox scrollbar styling
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
  }

  // Room container
  .room-container {
    padding: 16px;
    border-bottom: 1px solid $border-color;

    &:last-child {
      border-bottom: none;
    }
  }

  // Controls for incrementing/decrementing
  .traveler-control {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .control-buttons {
      display: flex;
      align-items: center;

      button {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid $border-color;
        background-color: white;
        border-radius: 4px;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .count {
        margin: 0 12px;
        min-width: 24px;
        text-align: center;
      }
    }
  }
}

// Mobile specific adjustments
@media (max-width: 950px) {
  .traveler-selector-container {
    .rooms-container {
      max-height: calc(100vh - 250px); // More space for mobile header/footer

      // Enhanced touch scrolling for mobile
      -webkit-overflow-scrolling: touch;
      scroll-behavior: smooth;
    }
  }
}

// RTL specific styles
html[dir="rtl"] {
  .traveler-selector-container {
    .traveler-control {
      .label {
        text-align: right;
      }
    }

    // Ensure consistent borders in RTL mode
    border: 1px solid $border-color;
  }
}