"use client";

import React, { useEffect, useState, useRef, useCallback } from "react";
import { createPortal } from "react-dom";
import Image from "next/image";
import "./header.scss";
import { useRouter, usePathname } from "next/navigation";
import { useCommonContext } from "@/app/contexts/commonContext";
import { useLanguage } from "@/app/contexts/languageContext";
import { useTranslation } from "@/app/hooks/useTranslation";
import { languageMap, languageFlags } from "@/i18n";
import MobileMenuModal from "./components/MobileMenu/MobileMenu";
import MobileMenuContent, { MobileMenuModals } from "./components/MobileMenuContent/MobileMenuContent";
import useScrollLock from "../utilities/ScrollLock/useScrollLock";
import MobileSubMenuModal from "./components/MobileSubMenuModal/MobileSubMenuModal";
import LanguageSelector from "./components/LanguageSelector/LanguageSelector";
import CurrencySelector from "./components/CurrencySelector/CurrencySelector";
import { Country } from "@/common-models/common.model";
import LoginModal from "../login/LoginModal";
import { CurrencyItem } from "@/models/common.model";
import { getUserDetailsApi } from "@/api/auth/auth-service";

// Icon Component for both PNG and SVG
interface IconProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
}

const IconComponent: React.FC<IconProps> = ({ src, alt, className = "", width = 20, height = 20 }) => {
  const isSvg = src.endsWith('.svg');
  const isKindaliServiceIcon = src.includes('Kindali Service icons');
  const isBluePngIcon = src.includes('BLUE') && src.includes('PNG');

  // Debug logging removed - icons should now be working

  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={`${className} object-contain`}
      style={{
        // No filter for Kindali Service icons or blue PNG icons (they should keep their original colors)
        filter: (isKindaliServiceIcon || isBluePngIcon) ? 'none' : (isSvg ? 'none' : 'brightness(0) invert(1)'),
        minWidth: `${width}px`,
        minHeight: `${height}px`
      }}
      onError={(e) => {
        console.error('Image failed to load:', src, e);
      }}
      // Add these props for better Next.js Image optimization
      priority={false}
      placeholder="empty"
      unoptimized={false}
    />
  );
};

// Simple SVG Icon Component for missing icons
interface SvgIconProps {
  className?: string;
  children: React.ReactNode;
}

const SvgIcon: React.FC<SvgIconProps> = ({ className = "", children }) => (
  <svg
    className={className}
    fill="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    {children}
  </svg>
);

// Icon mapping for Kindali Service icons
const iconPaths = {
  hotel: "/assets/img/Kindali Service icons/BLUE HOTEL PNG.png",
  flight: "/assets/img/Kindali Service icons/BLUE FLIGHT PNG.png",
  visa: "/assets/img/Kindali Service icons/BLUE VISA PNG.png",
  cruise: "/assets/img/Kindali Service icons/BLUE SHIP PNG.png",
  attractions: "/assets/img/Kindali icons/icons8-chicago-100.png", // Using chicago icon for attractions
};

// Icon components for missing icons
const Globe = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
  </SvgIcon>
);

const ChevronDown = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
  </SvgIcon>
);

const Headphones = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 1c-4.97 0-9 4.03-9 9v7c0 1.66 1.34 3 3 3h3v-8H5v-2c0-3.87 3.13-7 7-7s7 3.13 7 7v2h-4v8h3c1.66 0 3-1.34 3-3v-7c0-4.97-4.03-9-9-9z"/>
  </SvgIcon>
);

const Bell = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
  </SvgIcon>
);

const User = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
  </SvgIcon>
);

const Menu = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
  </SvgIcon>
);

// Additional icons for profile menu
const Calendar = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
  </SvgIcon>
);

const Heart = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
  </SvgIcon>
);

const Star = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
  </SvgIcon>
);

const LogOut = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
  </SvgIcon>
);

// Icon renderer function
const renderIcon = (iconName: string, className: string = "w-5 h-5") => {
  switch (iconName) {
    case "calendar":
      return <Calendar className={className} />;
    case "user":
      return <User className={className} />;
    case "heart":
      return <Heart className={className} />;
    case "star":
      return <Star className={className} />;
    case "logout":
      return <LogOut className={className} />;
    case "phone":
      return <Headphones className={className} />;
    default:
      return <User className={className} />;
  }
};

// Removed renderNavIcon function - using PNG icons with IconComponent instead

// Define types for better type safety
type NavItem = {
  label: string;
  icon: string;
};

type PriceView = {
  label: string;
  icon: string;
  description: string;
};

type Notification = {
  id: string;
  message: string;
  timestamp: string;
  isRead: boolean;
};

const Settings: NavItem[] = [
  // { label: "Language", icon: "globe" },
  // { label: "Price Display", icon: "tag" },
  { label: "Helpline", icon: "phone" },
];

const PROFILE_NAV_ITEMS: NavItem[] = [
  {label: "Bookings",icon: "calendar"},
  {label: "Profile",icon: "user"},
  {label: "Wishlist",icon: "heart"},
  {label: "Reviews",icon: "star"},
  {label: "Logout",icon: "logout"},
];

const PROFILE_NAV_ITEMS_MOBILE: NavItem[] = [
  {label: "Bookings",icon: "calendar"},
  {label: "Profile",icon: "user"},
  {label: "Wishlist",icon: "heart"},
  {label: "Reviews",icon: "star"},
   {label: "Helpline",icon: "phone"},
];

const Price_View: PriceView[] = [
  {
    label: "Average per night",
    icon: "tags",
    description: "Includes taxes & fees",
  },
  {
    label: "Base per night",
    icon: "tag",
    description: "Room rate only",
  },
];

export const currencyList: CurrencyItem[] = [
  { id: 1, currency_name: "Afghani", currency_symbol: "؋", currency_symbol_on_right: false, to_currency_code: "AFN", rate: 210.0, precision: 0, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 2, currency_name: "Arab Emirates Dirham", currency_symbol: "د.إ", currency_symbol_on_right: true, to_currency_code: "AED", rate: 0.27, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 3, currency_name: "Australian Dollar", currency_symbol: "A$", currency_symbol_on_right: false, to_currency_code: "AUD", rate: 1.36, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 4, currency_name: "Bahraini Dinar", currency_symbol: "ب.د", currency_symbol_on_right: true, to_currency_code: "BHD", rate: 1, precision: 3, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 5, currency_name: "Brazilian Real", currency_symbol: "R$", currency_symbol_on_right: false, to_currency_code: "BRL", rate: 5.25, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 6, currency_name: "British Pound", currency_symbol: "£", currency_symbol_on_right: false, to_currency_code: "GBP", rate: 0.81, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 7, currency_name: "Chinese Yuan", currency_symbol: "¥", currency_symbol_on_right: false, to_currency_code: "CNY", rate: 6.9, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 8, currency_name: "Euro", currency_symbol: "€", currency_symbol_on_right: false, to_currency_code: "EUR", rate: 0.92, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 9, currency_name: "Indian Rupee", currency_symbol: "₹", currency_symbol_on_right: false, to_currency_code: "INR", rate: 83.12, precision: 0, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 10, currency_name: "Japanese Yen", currency_symbol: "¥", currency_symbol_on_right: false, to_currency_code: "JPY", rate: 144.0, precision: 0, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 11, currency_name: "Kuwaiti Dinar", currency_symbol: "د.ك", currency_symbol_on_right: true, to_currency_code: "KWD", rate: 0.31, precision: 3, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 12, currency_name: "Omani Rial", currency_symbol: "﷼", currency_symbol_on_right: true, to_currency_code: "OMR", rate: 0.10, precision: 3, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 13, currency_name: "Pakistani Rupee", currency_symbol: "₨", currency_symbol_on_right: false, to_currency_code: "PKR", rate: 281.0, precision: 0, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 14, currency_name: "Philippine Peso", currency_symbol: "₱", currency_symbol_on_right: false, to_currency_code: "PHP", rate: 55.0, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 15, currency_name: "Qatari Riyal", currency_symbol: "﷼", currency_symbol_on_right: true, to_currency_code: "QAR", rate: 0.97, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 16, currency_name: "Russian Ruble", currency_symbol: "₽", currency_symbol_on_right: false, to_currency_code: "RUB", rate: 76.0, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 17, currency_name: "Saudi Riyal", currency_symbol: "﷼", currency_symbol_on_right: true, to_currency_code: "SAR", rate: 0.97, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 18, currency_name: "Singapore Dollar", currency_symbol: "S$", currency_symbol_on_right: false, to_currency_code: "SGD", rate: 1.36, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
  { id: 19, currency_name: "US Dollar", currency_symbol: "$", currency_symbol_on_right: false, to_currency_code: "USD", rate: 2.65, precision: 2, is_disabled_currency: false, is_disabled_conversion: false },
];

const Header: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>("hotels");
  const [isProfileDdVisible, setIsProfileDdVisible] = useState<boolean>(false);
  const [isMobileMenuVisible, setIsMobileMenuVisible] =
    useState<boolean>(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [showNotifications, setShowNotifications] = useState<boolean>(false);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [windowWidth, setWindowWidth] = useState<number | undefined>(undefined);
  const [isMounted, setIsMounted] = useState(false);
  const notificationRef = useRef<HTMLDivElement>(null);
  const {
    isLoggedIn,
    setIsLoggedIn,
    setUserData,
    userData,
    setHotelSearchFormData,
    isMobileModalOpen,
    setCurrencyList,
    setSelectedCurrency,
    selectedCurrency,
    isLoginModalOpen,
    setIsLoginModalOpen,
  } = useCommonContext();
  const { currentLanguage, changeLanguage, isRTL } = useLanguage();
  const { t } = useTranslation("common");
  const router = useRouter();
  const pathname = usePathname();
  const [activeProfileItem, setActiveProfileItem] = useState<string>("");
  const [isLanguageModalVisible, setIsLanguageModalVisible] =useState<boolean>(false);
  const [isPriceDisplayModalVisible, setIsPriceDisplayModalVisible] =useState<boolean>(false);
  const [countries, setCountries] = useState<Country[]>([]);
  const [currentSelectedLanguage, setCurrentSelectedLanguage] =useState<string>("English");
  const [currentSelectedPriceView, setCurrentSelectedPriceView] =useState<string>("Average per night");
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState<boolean>(false);
  const [isCurrencyDropdownOpen, setIsCurrencyDropdownOpen] = useState<boolean>(false);

  // Mobile menu modal states
  const [isMobileLanguageModalOpen, setIsMobileLanguageModalOpen] = useState<boolean>(false);
  const [isMobilePriceModalOpen, setIsMobilePriceModalOpen] = useState<boolean>(false);

  // Refs for positioning dropdowns
  const languageButtonRef = useRef<HTMLButtonElement>(null);
  const currencyButtonRef = useRef<HTMLButtonElement>(null);
  const notificationButtonRef = useRef<HTMLButtonElement>(null);
  const profileButtonRef = useRef<HTMLButtonElement>(null);

  const getUserDetails = useCallback(async () => {
  try {
    const response = await getUserDetailsApi();

    if (response.id) {
      setUserData(response);
      localStorage.setItem('userData', JSON.stringify(response));
      setIsLoggedIn(true);
    } else {
      setIsLoggedIn(false);
      localStorage.removeItem('userData');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('token');
    }
  } catch (error) {
    console.error("Error fetching user details:", error);
  }
}, [setUserData, setIsLoggedIn]);

  useEffect(() => {
    if(isLoggedIn){
      getUserDetails();
    }
  }, [getUserDetails,isLoggedIn]);

  
  // Handle window resize for dynamic layout changes
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    // Set initial window width and mounted state
    if (typeof window !== 'undefined') {
      setWindowWidth(window.innerWidth);
      setIsMounted(true);
      window.addEventListener('resize', handleResize);
    }

    // Cleanup event listener
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, []);


  useEffect(() => {
    // Only access localStorage on the client side
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem("userData");
      if (userData) {
        setUserData(JSON.parse(userData));
        setIsLoggedIn(true);
      }
      const searchData = localStorage.getItem("hotelSearchFormData");
      if (searchData) {
        setHotelSearchFormData(JSON.parse(searchData));
      }
    }
  }, [setIsLoggedIn, setHotelSearchFormData,setUserData]);

  useEffect(() => {
    const mockNotifications: Notification[] = [
      {
        id: "1",
        message: "Your hotel booking in Dubai is confirmed!",
        timestamp: "2 hours ago",
        isRead: false,
      },
      {
        id: "2",
        message: "New holiday package for Bali is available now.",
        timestamp: "1 day ago",
        isRead: false,
      },
      {
        id: "3",
        message: "Your flight to Paris departs tomorrow.",
        timestamp: "2 days ago",
        isRead: true,
      },
    ];

    setNotifications(mockNotifications);
    setUnreadCount(mockNotifications.filter((n) => !n.isRead).length);
  }, []);

  // Close notifications dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target as Node)
      ) {
        setShowNotifications(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [notificationRef]);

  // Close profile dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (isProfileDdVisible) {
        const target = event.target as HTMLElement;
        // Check for both the dropdown container and the dropdown menu itself
        if (!target.closest(".profile-dropdown-container") &&
            !target.closest(".profile-dropdown") &&
            !target.closest(".dropdown-menu.profile-dropdown")) {
          setIsProfileDdVisible(false);
        }
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isProfileDdVisible]);

  useEffect(() => {
    fetch("/assets/json/countries.json")
      .then((response) => response.json())
      .then((data) => setCountries(data))
      .catch((error) => console.error("Error fetching JSON:", error));
  }, []);

  useEffect(() => {
    getUserDetails();
  }, [getUserDetails]);

  const handleProfileMenuClick = (item: string) => {
    setIsProfileDdVisible(false);
    setIsMobileMenuVisible(false);
    setActiveProfileItem(item);

    if (
      item === "Bookings" ||
      item === "Profile" ||
      item === "Reviews" ||
      item === "Wishlist"
    ) {
      router.push(`/profile?tab=${item}`);
    } else if (item === "Logout") {
      // Only need to remove userData from localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem("userData");
      }
      setIsLoggedIn(false);
      router.push("/");
    }
  };

  const handleProfileMenuAtMobileClick = (item: string) => {
    // Close profile dropdown
    setIsProfileDdVisible(false);
    setActiveProfileItem(item);

    // Close mobile menu for all items except Language
    if (item !== "Language") {
      setIsMobileMenuVisible(false);
    }

    // Handle different menu items
    switch (item) {
      case "Bookings":
      case "Profile":
      case "Reviews":
      case "Wishlist":
        router.push(`/profile?tab=${item}`);
        setIsProfileDdVisible(false);
        break;

      case "Language":
        setIsLanguageModalVisible(true);
        break;

      case "Price Display":
        setIsPriceDisplayModalVisible(true);
        setIsMobileMenuVisible(true);
        break;

      case "Logout":
        // Only need to remove userData from localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem("userData");
        }
        setIsLoggedIn(false);
        router.push("/");
        break;
    }
  };

  const handleNotificationClick = () => {
    setShowNotifications(!showNotifications);
  };

  const markAllAsRead = () => {
    const updatedNotifications = notifications.map((notification) => ({
      ...notification,
      isRead: true,
    }));
    setNotifications(updatedNotifications);
    setUnreadCount(0);
  };

  const viewAllNotifications = () => {
    setShowNotifications(false);
    // Navigate to notifications page
    router.push("/Notifications");
  };

  const handleCurrentSelectedMobileLanguage = (language: string) => {
    setCurrentSelectedLanguage(language);
    setIsLanguageModalVisible(false);
  };

  useScrollLock(isMobileMenuVisible);
  useScrollLock(isLanguageModalVisible);
  useScrollLock(isPriceDisplayModalVisible);

  // Navigation handler for nav items
  const handleNavItemClick = (itemId: string) => {
    setActiveTab(itemId);

    // Navigate to appropriate pages
    switch (itemId) {
      case "hotels":
        router.push("/hotel");
        break;
      case "flight":
        router.push("/flights");
        break;
      case "visa":
        router.push("/visa");
        break;
      case "cruise":
        router.push("/cruise");
        break;
      default:
        break;
    }
  };

  useEffect(()=>{
    const currencyListLocal = localStorage.getItem("currencyList");
    if (currencyListLocal) {
      setCurrencyList(JSON.parse(currencyListLocal));
    }else{
      setCurrencyList(currencyList);
    }
  },[setCurrencyList])

  return (
    <>
  {/* Header with new white background and primary-color text styling */}
<header
  className={`header-main ${isMobileModalOpen ? '' : ''} bg-white text-primary-color shadow-lg border-b border-gray-200 ${isRTL ? 'rtl' : ''}`}
>
  <div className="container mx-auto px-4">
    <div className="flex justify-between items-center h-16">
      {/*
        LOGO SECTION - Kindali official logo
        - Clickable logo that navigates to home page
        - Consistent sizing across all devices
      */}
      <div className="cursor-pointer" onClick={()=>router.push('/')}>
        <Image
          src="/assets/img/kindali logo/kindali-logo-blue.png"
          alt="Kindali Logo"
          width={100}
          height={100}
          style={{minWidth:'140px'}}
          className="w-full h-auto"
          priority
        />
      </div>
        {
          isMounted && windowWidth !== undefined && windowWidth >= 1280 && (  <div className={`container mx-auto px-4`}>
    <div className={`flex gap-4 overflow-x-auto hide-scrollbar justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
      {[
        { id: "hotels", icon: iconPaths.hotel, label: t("header.nav.hotels", "Stays") },
      ].map(item => {
        return (
          <button
            key={item.id}
            className={`px-4 py-1 font-medium text-sm transition-all duration-200 flex flex-row items-center justify-center cursor-pointer whitespace-nowrap gap-2 rounded-lg ${
              activeTab === item.id
                ? "bg-gradient-to-br from-blue-50 to-blue-100 text-primary-color shadow-sm"
                : "text-primary-color hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 hover:shadow-sm"
            }`}
            onClick={() => handleNavItemClick(item.id)}
          >
            <div
              className="w-8 h-8 transition-all duration-200"
              style={{
                filter: activeTab === item.id ? 'grayscale(0)' : 'grayscale(1)',
                transition: 'filter 0.2s ease-in-out'
              }}
            >
              <IconComponent
                src={item.icon}
                alt={item.label}
                className="w-8 h-8"
                width={32}
                height={32}
              />
            </div>
            <span className="text-sm font-semibold">{item.label}</span>
          </button>
        );
      })}
      {[
        { id: "flight", icon: iconPaths.flight, label: t("header.nav.flights", "Flights") },
        { id: "visa", icon: iconPaths.visa, label: t("header.nav.visa", "Visa") },
        { id: "cruise", icon: iconPaths.cruise, label: t("header.nav.cruise", "Cruise") }
      ].map(item => {
        return (
          <button
            key={item.id}
            className={`px-4 py-1 font-medium text-sm transition-all duration-200 flex flex-row items-center justify-center cursor-pointer whitespace-nowrap gap-2 rounded-lg ${
              activeTab === item.id
                ? "bg-gradient-to-br from-blue-50 to-blue-100 text-primary-color shadow-sm"
                : "text-primary-color hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 hover:shadow-sm"
            }`}
            onClick={() => handleNavItemClick(item.id)}
          >
            <div
              className="w-8 h-8 transition-all duration-200"
              style={{
                filter: activeTab === item.id ? 'grayscale(0)' : 'grayscale(1)',
                transition: 'filter 0.2s ease-in-out'
              }}
            >
              <IconComponent
                src={item.icon}
                alt={item.label}
                className="w-8 h-8"
                width={32}
                height={32}
              />
            </div>
            <span className="text-sm font-semibold">{item.label}</span>
          </button>
        );
      })}
    </div>
  </div>)
        }
      <div className={`hidden md:flex items-center ${isRTL ? 'space-x-reverse space-x-8' : 'space-x-8'}`}>
        {/* Language Dropdown */}
        <div className="dropdown-container">
          <button
            ref={languageButtonRef}
            onClick={() => {
              setIsLanguageDropdownOpen(!isLanguageDropdownOpen);
              setIsCurrencyDropdownOpen(false); // Close currency dropdown
            }}
            className={`text-primary-color hover:text-primary-color-dark text-sm font-semibold flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} transition-all duration-200 cursor-pointer !rounded-button whitespace-nowrap hover:opacity-80`}
          >
            <Globe className="w-5 h-5 text-primary-color" />
            <span>{languageMap[currentLanguage] || "English"}</span>
            <ChevronDown className={`w-4 h-4 text-primary-color transition-transform duration-200 ${isLanguageDropdownOpen ? 'rotate-180' : ''}`} />
          </button>

        </div>

        {/* Currency Dropdown */}
        <div className="dropdown-container">
          <button
            ref={currencyButtonRef}
            onClick={() => {
              setIsCurrencyDropdownOpen(!isCurrencyDropdownOpen);
              setIsLanguageDropdownOpen(false); // Close language dropdown
            }}
            className={`text-primary-color hover:text-primary-color-dark text-sm font-semibold flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} transition-all duration-200 cursor-pointer !rounded-button whitespace-nowrap hover:opacity-80`}
          >
            <span>{selectedCurrency.currency_symbol}</span>
            <span>{selectedCurrency.to_currency_code}</span>
            <ChevronDown className={`w-4 h-4 text-primary-color transition-transform duration-200 ${isCurrencyDropdownOpen ? 'rotate-180' : ''}`} />
          </button>

        </div>

        {/* Customer Service */}
        <div className="dropdown-container">
          <button className={`text-primary-color hover:text-primary-color-dark text-sm font-semibold flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} transition-all duration-200 cursor-pointer !rounded-button whitespace-nowrap hover:opacity-80`}>
            <Headphones className="w-5 h-5 text-primary-color" />
            <span>{t("header.helpline")}</span>
          </button>
        </div>

        {/* Notification Bell (if logged in) */}
        {isLoggedIn && (
          <div className="dropdown-container" ref={notificationRef}>
            <button
              ref={notificationButtonRef}
              onClick={handleNotificationClick}
              className={`text-primary-color hover:text-primary-color-dark text-sm font-medium flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} transition-all duration-200 cursor-pointer !rounded-button whitespace-nowrap relative`}
            >
              <Bell className="w-5 h-5 text-primary-color" />
              {unreadCount > 0 && (
                <span className={`absolute -top-1 ${isRTL ? '-left-1' : '-right-1'} bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center`}>
                  {unreadCount}
                </span>
              )}
            </button>


          </div>
        )}

        <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
          {/* Login/Register Button */}
          {isLoggedIn ? (
            <div className="dropdown-container profile-dropdown-container">
              <button
                ref={profileButtonRef}
                onClick={(e) => {
                  e.stopPropagation();
                  setIsProfileDdVisible(!isProfileDdVisible);
                }}
                className={`bg-primary-color text-white px-6 py-2 rounded-md hover:bg-primary-color-dark transition-all duration-200 text-sm font-medium cursor-pointer !rounded-button whitespace-nowrap flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} shadow-sm hover:shadow`}
              >
                <User className="w-5 h-5 text-white" />
                <span>{userData?.name || t("common.account", "Account")}</span>
              </button>


            </div>
          ) : (
            <button
              onClick={() => setIsLoginModalOpen(true)}
              className={`bg-primary-color text-white px-6 py-2 rounded-md hover:bg-primary-color-dark transition-all duration-200 text-sm font-medium cursor-pointer !rounded-button whitespace-nowrap flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} shadow-sm hover:shadow`}
            >
              <User className="w-5 h-5 text-white" />
              <span>{t("common.signInRegister", "Sign in / Register")}</span>
            </button>
          )}
        </div>
      </div>

      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileMenuVisible(true)}
        className="md:hidden text-primary-color p-2 hover:bg-gray-100 rounded-md transition-colors duration-200"
      >
        <Menu className="w-6 h-6 text-primary-color" />
      </button>
    </div>

      {
          isMounted && windowWidth !== undefined && windowWidth < 1280 && pathname === '/hotel' && (
          <div className={`container hide-scrollbar`}>
            <div className={`flex justify-start sm:justify-center overflow-x-auto  gap-1 ${isRTL ? 'flex-row-reverse' : ''} px-4`} style={{scrollbarWidth: 'none', msOverflowStyle: 'none'}}>
  {[
    { id: "hotels", icon: iconPaths.hotel, label: t("header.nav.hotels", "Stays") },
    { id: "flight", icon: iconPaths.flight, label: t("header.nav.flights", "Flights") },
    { id: "visa", icon: iconPaths.visa, label: t("header.nav.visa", "Visa") },
    { id: "cruise", icon: iconPaths.cruise, label: t("header.nav.cruise", "Cruise") }
  ].map(item => {
    const isActive = activeTab === item.id;

    return (
      <button
        key={item.id}
        className={`px-3 py-1 font-medium text-sm transition-all duration-200 flex flex-col items-center justify-center cursor-pointer whitespace-nowrap ${
          activeTab === item.id
            ? "bg-gradient-to-br from-blue-50 to-blue-100 text-primary-color shadow-sm rounded-t-lg"
            : "text-primary-color hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 hover:shadow-sm rounded-lg"
        }`}
        onClick={() => handleNavItemClick(item.id)}
        role="tab"
        aria-selected={isActive}
        aria-controls={`${item.id}-panel`}
      >
        <div
          className="w-8 h-8 mb-1 transition-all duration-200"
          style={{
            filter: activeTab === item.id ? 'grayscale(0)' : 'grayscale(1)',
            transition: 'filter 0.2s ease-in-out'
          }}
        >
          <IconComponent
            src={item.icon}
            alt={item.label}
            className="w-8 h-8"
            width={36}
            height={36}
          />
        </div>
        <span className="text-xs font-semibold">{item.label}</span>
      </button>
    );
  })}
</div>
          </div>
      )}
  </div>
</header>

{/* Portal Dropdowns - Outside header stacking context */}
{isMounted && typeof window !== 'undefined' && createPortal(
  <>
    {/* Light Overlay for all dropdowns */}
    {(isLanguageDropdownOpen || isCurrencyDropdownOpen || showNotifications || isProfileDdVisible) && (
      <div
        className="dropdown-overlay"
        onClick={() => {
          setIsLanguageDropdownOpen(false);
          setIsCurrencyDropdownOpen(false);
          setShowNotifications(false);
          setIsProfileDdVisible(false);
        }}
      />
    )}

    {/* Language Dropdown */}
    {isLanguageDropdownOpen && languageButtonRef.current && (
      <div
        className="dropdown-menu language-dropdown"
        style={{
          top: languageButtonRef.current.getBoundingClientRect().bottom + window.scrollY + 4,
          left: isRTL ? languageButtonRef.current.getBoundingClientRect().left + window.scrollX :
                       languageButtonRef.current.getBoundingClientRect().right + window.scrollX - 224,
        }}
      >
        {Object.entries(languageMap)
          .filter(([code]) => ['en', 'ar', 'es', 'fr', 'hi'].includes(code))
          .map(([code, name]) => (
            <a
              key={code}
              href="#"
              onClick={(e) => {
                e.preventDefault();
                changeLanguage(code);
                setIsLanguageDropdownOpen(false);
              }}
              className={`flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 ${currentLanguage === code ? 'bg-gray-100' : ''}`}
            >
              <div className="w-6 h-4 mr-3 relative overflow-hidden rounded-sm border border-gray-200">
                <Image
                  src={languageFlags[code]}
                  alt={`${name} flag`}
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <span>{name}</span>
            </a>
          ))}
      </div>
    )}

    {/* Currency Dropdown */}
    {isCurrencyDropdownOpen && currencyButtonRef.current && (
      <div
        className="dropdown-menu currency-dropdown"
        style={{
          top: currencyButtonRef.current.getBoundingClientRect().bottom + window.scrollY + 8,
          left: isRTL ? currencyButtonRef.current.getBoundingClientRect().left + window.scrollX :
                       currencyButtonRef.current.getBoundingClientRect().right + window.scrollX - 300,
        }}
      >
        <div className="currency-selector-container">
          {currencyList.map((currency, index) => (
            <button
              key={index}
              onClick={() => {
                setSelectedCurrency(currency);
                setIsCurrencyDropdownOpen(false);
              }}
              className={`currency-selector ${selectedCurrency.to_currency_code === currency.to_currency_code ? "active" : ""}`}
            >
              <span className="currency-name">{currency.to_currency_code}</span>
              <span className="currency-symbol">{currency.currency_symbol}</span>
            </button>
          ))}
        </div>
      </div>
    )}

    {/* Notifications Dropdown */}
    {showNotifications && notificationButtonRef.current && (
      <div
        className={`dropdown-menu notification-dropdown ${isRTL ? 'text-right' : 'text-left'}`}
        style={{
          top: notificationButtonRef.current.getBoundingClientRect().bottom + window.scrollY + 4,
          left: isRTL ? notificationButtonRef.current.getBoundingClientRect().left + window.scrollX :
                       notificationButtonRef.current.getBoundingClientRect().right + window.scrollX - 256,
        }}
      >
        <div className="px-4 py-2 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-gray-800 font-medium">{t("common.notifications", "Notifications")}</h3>
          {unreadCount > 0 && (
            <button
              className="text-blue-600 text-xs hover:text-blue-800"
              onClick={markAllAsRead}
            >
              {t("common.markAllAsRead", "Mark all as read")}
            </button>
          )}
        </div>
        <div className="max-h-64 overflow-y-auto">
          {notifications.length > 0 ? (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className={`px-4 py-3 border-b border-gray-100 hover:bg-gray-50 ${
                  !notification.isRead ? "bg-blue-50" : ""
                }`}
              >
                <p className="text-sm text-gray-800">{notification.message}</p>
                <span className="text-xs text-gray-500 mt-1 block">
                  {notification.timestamp}
                </span>
              </div>
            ))
          ) : (
            <div className="px-4 py-3 text-center text-gray-500 text-sm">
              {t("common.noNotifications", "No notifications yet")}
            </div>
          )}
        </div>
        <div className="px-4 py-2 text-center border-t border-gray-200">
          <button
            onClick={viewAllNotifications}
            className="text-blue-600 text-sm hover:text-blue-800"
          >
            {t("common.viewAllNotifications", "View all notifications")}
          </button>
        </div>
      </div>
    )}

    {/* Profile Dropdown */}
    {isProfileDdVisible && profileButtonRef.current && (
      <div
        className={`dropdown-menu profile-dropdown ${isRTL ? 'text-right' : 'text-left'}`}
        style={{
          top: profileButtonRef.current.getBoundingClientRect().bottom + window.scrollY + 4,
          left: isRTL ? profileButtonRef.current.getBoundingClientRect().left + window.scrollX :
                       profileButtonRef.current.getBoundingClientRect().right + window.scrollX - 192,
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="px-4 py-2 border-b border-gray-200">
          <h3 className="text-gray-800 font-medium text-sm">{t("common.myAccount", "MY ACCOUNT")}</h3>
        </div>
        {PROFILE_NAV_ITEMS.map((item, index) => {
          return (
            <button
              key={index}
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleProfileMenuClick(item.label);
              }}
              className={`w-full text-left px-4 py-2 text-sm flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} ${
                activeProfileItem === item.label
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-gray-100"
              } cursor-pointer transition-colors duration-200`}
            >
              {renderIcon(item.icon, "w-5 h-5")}
              <span>{t(`profile.${item.label.toLowerCase()}`, item.label)}</span>
            </button>
          );
        })}
      </div>
    )}
  </>,
  document.body
)}

{/* Spacer for fixed header */}
<div className="header-spacer"></div>

{/* Navigation Tabs */}

{/* Mobile Menu Modal */}
<MobileMenuModal
  isOpen={isMobileMenuVisible}
  handleClose={() => setIsMobileMenuVisible(false)}
  isRTL={isRTL}
>
  <MobileMenuContent
    userData={userData}
    activeProfileItem={activeProfileItem}
    isRTL={isRTL}
    handleProfileMenuAtMobileClick={handleProfileMenuAtMobileClick}
    profileItems={PROFILE_NAV_ITEMS_MOBILE}
    settingsItems={Settings}
    isLanguageModalOpen={isMobileLanguageModalOpen}
    isPriceModalOpen={isMobilePriceModalOpen}
    setIsLanguageModalOpen={setIsMobileLanguageModalOpen}
    setIsPriceModalOpen={setIsMobilePriceModalOpen}
    currentPathname={pathname}
  />
</MobileMenuModal>

{/* Mobile Menu Modals - Rendered outside mobile menu for full screen coverage */}
<MobileMenuModals
  isLanguageModalOpen={isMobileLanguageModalOpen}
  isPriceModalOpen={isMobilePriceModalOpen}
  setIsLanguageModalOpen={setIsMobileLanguageModalOpen}
  setIsPriceModalOpen={setIsMobilePriceModalOpen}
  selectedCurrency={selectedCurrency}
  setSelectedCurrency={setSelectedCurrency}
  currentLanguage={currentLanguage}
  changeLanguage={changeLanguage}
  currencyList={currencyList}
/>

{/* Language Modal */}
<MobileSubMenuModal
  isOpen={isLanguageModalVisible}
  handleClose={() => setIsLanguageModalVisible(false)}
  title={t("settings.language", "Language")}
  isRTL={isRTL}
  onBack={() => {
    console.log("Language modal back button clicked");
    setIsLanguageModalVisible(false);
    setIsMobileMenuVisible(true);
  }}
>
  <LanguageSelector
    handleLanguageSelection={handleCurrentSelectedMobileLanguage}
    isRTL={isRTL}
  />
</MobileSubMenuModal>

{/* Price Display Modal */}
<MobileSubMenuModal
  isOpen={isPriceDisplayModalVisible}
  handleClose={() => setIsPriceDisplayModalVisible(false)}
  title={t("settings.priceDisplay", "Price Display")}
  isRTL={isRTL}
  onBack={() => {
    console.log("Currency modal back button clicked");
    setIsPriceDisplayModalVisible(false);
    setIsMobileMenuVisible(true);
  }}
>
  <CurrencySelector
    currencies={currencyList}
    currentSelectedCurrency={selectedCurrency.to_currency_code}
    handleCurrencySelection={(currency) => {
      setSelectedCurrency(currency);
      setIsPriceDisplayModalVisible(false);
      setIsMobileMenuVisible(true);
    }}
    priceViewOptions={Price_View}
    currentSelectedPriceView={currentSelectedPriceView}
    handlePriceViewSelection={setCurrentSelectedPriceView}
  />
</MobileSubMenuModal>

{/* Login/Signup Popup */}
<LoginModal
  registerRoute="/profile?tab=Profile"
  isOpen={isLoginModalOpen}
  onClose={() => setIsLoginModalOpen(false)}
/>
    </>
  );
};

export default Header;