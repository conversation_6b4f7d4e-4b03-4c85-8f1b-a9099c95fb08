"use client";
import React from "react";
import './FareSummary.scss'
import { HotelRate } from "../../booking-init.model";
import { getConvertedCurrency } from "@/helpers/hotel/currency-helper";
import { useCommonContext } from "@/app/contexts/commonContext";

type FareSummaryProps = {
  bookingRate?: HotelRate;
  roomCount?: number;
  nightCount?: number;
  currency?: string;
}

function FareSummary({ bookingRate, roomCount = 1, nightCount = 1, currency = "INR" } : FareSummaryProps) {
  // Calculate total taxes from booking init API
  const { selectedCurrency } = useCommonContext();
  const totalTaxes = bookingRate?.taxes?.reduce((sum, tax) => sum + tax.amount, 0) || 0;

  // Calculate total additional charges
  const totalAdditionalCharges = bookingRate?.additionalCharges?.reduce((sum, charge) => sum + charge.charge.amount, 0) || 0;

  return (
    <div className="fare-summary-container">
      <h6 className="fare-summary-container__header">Fare Summary</h6>
      <div className="fare-summary-container__label-price">
        <div className="label">{roomCount} Room{roomCount > 1 ? 's' : ''}, {nightCount} Night{nightCount > 1 ? 's' : ''}</div>
        <div className="price">
          {getConvertedCurrency(bookingRate?.baseRate || 0, selectedCurrency)}
        </div>
      </div>
      <div className="fare-summary-container__label-price">
        <div className="label">
          Taxes & Charges{" "}
          <span>
            <i className="fa-solid fa-circle-exclamation"></i>
          </span>
        </div>

        <div className="price">
          {getConvertedCurrency(totalTaxes, selectedCurrency)}
        </div>
      </div>

      {/* Additional Charges (City Tax, Cleaning Fee, etc.) */}
      {bookingRate?.additionalCharges && bookingRate.additionalCharges.length > 0 && (
        <>
          {bookingRate.additionalCharges.map((charge, index) => (
            <div key={index} className="fare-summary-container__label-price">
              <div className="label">
                {(charge.text || charge.charge.description)?.replace(/_/g, " ")}{" "}
                <span>
                  <i className="fa-solid fa-circle-exclamation"></i>
                </span>
              </div>

              <div className="price">
                {getConvertedCurrency(charge.charge.amount, selectedCurrency)}
                {/* {charge.charge.currency === "AED" ? "AED" : currencySymbol}{Number(charge.charge.amount).toFixed(2).split('.')[0]} */}
                {/* <span className="sub">.{Number(charge.charge.amount).toFixed(2).split('.')[1]}</span> */}
              </div>
            </div>
          ))}
        </>
      )}
      <hr style={{marginBottom: '10px'}}/>

      <div className="fare-summary-container__label-price ">
        <div className="label netAmt">
        Net Amount Payable
        </div>

        <div className="price netAmt">
          {getConvertedCurrency(bookingRate?.totalRate || 0, selectedCurrency)}
          {/* {currencySymbol}{Number(bookingRate?.totalRate || 0).toFixed(2).split('.')[0]}
          <span className="sub">.{Number(bookingRate?.totalRate || 0).toFixed(2).split('.')[1]}</span> */}
        </div>
      </div>
    </div>
  );
}

export default FareSummary;
