@use '/styles/variable' as *;

.hotel-filter-container{
    width: 100%;
    height: 100%;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #fff;

    .filter-head{
        height: fit-content;
        padding: 8px;
        height: 50px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        h3{
            font-size: 16px;
            font-weight: 700;
        }

        .resetBtn{
            font-size: 12px;
            color: rgb(239.4, 233, 227.6);
            font-weight: 600;
            cursor: not-allowed;
            transition: color 0.2s ease-in-out;

            &.enabled{
                color: $primary-color;
                cursor: pointer;
            }
        }
    }

    .filter-body{
        height: calc(100% - 50px);
        width: 100%;
        overflow-x: hidden;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin;
        padding-right: 5px;
        padding-bottom: 20px;
    }
}