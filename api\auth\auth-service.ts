import { InitiateOtpRequest, InitiateOtpResponse, OtpVerificationResponse, User, VerifyOtpRequest } from "@/models/common.model";
import apiService from "../api-service";

export const initiateOtp = async (identifier: string) : Promise<InitiateOtpResponse> => {
  try {

    const isEmail = identifier.includes('@');
    const payload: InitiateOtpRequest = isEmail? { email: identifier }: { phone: identifier };

    const response = await apiService.postAuthentication<InitiateOtpResponse>('auth/initiate/otp', payload);
    return response;
  } catch (error) {
    console.error('Error initiating OTP:', error);
    return {
      status: 'failed',
      message: 'Failed to send OTP. Please try again.'
    };
  }
};

export const verifyOtp = async (identifier: string, otp: string) : Promise<OtpVerificationResponse> => {
  try {
    const isEmail = identifier.includes('@');
    const payload: VerifyOtpRequest = isEmail? { email: identifier, otp }: { phone: identifier, otp };

    const response = await apiService.postAuthentication<OtpVerificationResponse>('auth/verify-otp', payload);
    return response;
  } catch (error) {
    console.error('Error verifying OTP:', error);
    return {
      access_token: '',
      token_type: '',
      expires_at: '',
      refresh_token: ''
    };
  }
};

export const getUserDetailsApi = async () : Promise<User> => {
  try {
    const response = await apiService.getAuthentication<User>('auth/me');
    return response;
  } catch (error) {
    console.error('Error fetching user details:', error);
    return {
      id: '',
      email: '',
      name: '',
      phone: '',
      created_at: '',
      updated_at: ''
    };
  }
};