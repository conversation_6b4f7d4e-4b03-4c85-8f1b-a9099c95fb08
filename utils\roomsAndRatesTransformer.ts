import { RoomsAndRatesApiResponse, TransformedRoom, TransformedRoomsData } from '@/models/hotel/rooms-and-rates.model';



export const transformRoomsAndRates = (data: RoomsAndRatesApiResponse): TransformedRoomsData => {
  const roomGroups: { [stdRoomId: string]: TransformedRoom } = {};

  data.data.forEach((recommendation) => {
    recommendation.groupedRates.forEach((groupedRate) => {
      const roomName = groupedRate.room.name;
      
      // Handle multiple occupancies - add to each stdRoomId group
      groupedRate.rate.occupancies.forEach((occupancy) => {
        const stdRoomId = occupancy.stdRoomId;
        
        // Create group if doesn't exist
        if (!roomGroups[stdRoomId]) {
          roomGroups[stdRoomId] = {
            stdRoomId,
            roomName,
            roomdata: []
          };
        }
        
        // Add recommendation to this group (avoid duplicates)
        const exists = roomGroups[stdRoomId].roomdata.some(
          existing => existing.recommendationId === recommendation.recommendationId
        );
        
        if (!exists) {
          roomGroups[stdRoomId].roomdata.push(recommendation);
        }
      });
    });
  });

  // Convert object to array
  return Object.values(roomGroups);
};
