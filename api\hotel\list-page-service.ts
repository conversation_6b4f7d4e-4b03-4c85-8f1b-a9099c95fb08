import { HotelSearchData, SearchApiRequest } from "@/models/hotel/search-page.model"
import {  } from "@/app/HotelSearchResult/hotel-search-result.model"
import apiService from "../api-service"
import { SearchInitApiResponse } from "@/models/hotel/list-page.model";

export const searchInitApi = async (body:HotelSearchData, skip: number = 0, limit: number = 1) : Promise<SearchInitApiResponse> => {
    const queryParams = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString()
    });

    return apiService.post(`search/init?${queryParams.toString()}`, body)
}

// Updated search API with skip parameter and polling until isCompleted
export const searchApi = async (body: SearchApiRequest, skip: number = 0): Promise<SearchInitApiResponse> => {
    const queryParams = new URLSearchParams({
        skip: skip.toString()
    });

    return apiService.post(`search?${queryParams.toString()}`, body)
}

