"use client";
import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import './BookingConfirmation.scss';
import { getBookingDetails, extractBookingDisplayData } from '@/api/hotel/booking-details-service';
import { BookingDetailsApiResponse } from './booking-details-api.model';
import { HotelBookingResponse } from '../BookingPreviewPage/hotel-booking-api.model';
import BookingConfirmationShimmer from './BookingConfirmationShimmer';

interface UpiDetails {
  channel: string;
  upi_id: string;
}

interface PaymentMethod {
  upi: UpiDetails;
}

interface OfferMeta {
  offer_title: string;
  offer_description: string;
  offer_code: string;
  offer_start_time: string;
  offer_end_time: string;
}

interface OfferRedemption {
  redemption_status: string;
  discount_amount: number;
  cashback_amount: number;
}

interface PaymentOffer {
  offer_id: string;
  offer_type: string;
  offer_meta: OfferMeta;
  offer_redemption: OfferRedemption;
}

interface OrderDetails {
  order_id: string;
  order_amount: number;
  order_currency: string;
  order_tags: any;
}

interface PaymentDetails {
  cf_payment_id: string;
  payment_status: string;
  payment_amount: number;
  payment_currency: string;
  payment_message: string;
  payment_time: string;
  bank_reference: string;
  auth_id: any;
  payment_method: PaymentMethod;
  payment_group: string;
}

interface CustomerDetails {
  customer_name: string | null;
  customer_id: string;
  customer_email: string;
  customer_phone: string;
}

interface GatewayDetails {
  gateway_name: string;
  gateway_order_id: string;
  gateway_payment_id: string;
  gateway_order_reference_id: string;
  gateway_settlement: string;
  gateway_status_code: any;
}

interface WebhookData {
  order: OrderDetails;
  payment: PaymentDetails;
  customer_details: CustomerDetails;
  payment_gateway_details: GatewayDetails;
  payment_offers: PaymentOffer[];
}

export interface BookingStatusUpdateWebhookPayload {
  data: WebhookData;
  event_time: string;
  type: string;
}

interface BookingDisplayData {
  confirmationNumber: string;
  bookingId?: string;
  status: string;
  paymentStatus: string;
  hotel: {
    name: string;
    address: string;
    city: string;
    country: string;
    phone: string;
    email: string;
    starRating: string;
    heroImage: string;
    checkInTime: string;
    checkOutTime: string;
  };
  booking: {
    checkIn: string;
    checkOut: string;
    nights: number;
    guests: number;
    roomType: string;
    bookingDate: string;
    bookingStatus: string;
  };
  pricing: {
    baseRate: number;
    taxes: number;
    totalAmount: number;
    currency: string;
  };
  guest: {
    name: string;
    email: string;
    phone: string;
  };
  rooms: Array<{
    id: string;
    name: string;
    description: string;
    facilities: string[];
  }>;
  cancellation: {
    refundable: boolean;
    cancellationPolicies: any[];
  };
}

export default function BookingConfirmation() {
  const params = useParams();
  const router = useRouter();

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bookingDetails, setBookingDetails] = useState<BookingDisplayData | null>(null);
  const [bookingStatus, setBookingStatus] = useState<'success' | 'failed'>('success');
  const [currentBookingReference, setCurrentBookingReference] = useState<string | null>(null);
  const [isNoDataAvailable, setIsNoDataAvailable] = useState<boolean>(true);
  // Countdown state, initialized to 2 hours (7200 seconds)
  const [countdown, setCountdown] = useState(7200);


  const fetchBookingDetails = useCallback(async (bookingReference: string) => {
    try {
      setIsLoading(true);
      setError(null);

      if (!bookingReference && params?.bookingReference) {
        bookingReference = params.bookingReference as string;
      }

      if (!bookingReference) {
        const storedBookingResponse = localStorage.getItem('bookingResponse');
        if (storedBookingResponse) {
          try {
            const bookingResponse: HotelBookingResponse = JSON.parse(storedBookingResponse);
            bookingReference = bookingResponse.booking_reference;
          } catch (parseError) {
            console.log('Error parsing stored booking response:', parseError);
          }
        }
      }
      
      setCurrentBookingReference(bookingReference);

      const apiResponse = await getBookingDetails(bookingReference || "");
      if(!(apiResponse && apiResponse.hotel_booking.provider_response && apiResponse.hotel_booking.provider_response.hotelBooking)){
        setIsNoDataAvailable(true);
        setTimeout(() => {
          fetchBookingDetails(bookingReference)
        }, 1000);
        return;
      }else{
        setIsLoading(false);
        setIsNoDataAvailable(false);
      }
      const displayData = extractBookingDisplayData(apiResponse);

      const isBookingSuccessful = apiResponse.status === 'CONFIRMED' ||
        apiResponse.status === 'COMPLETED' ||
        (apiResponse.status !== 'FAILED' && apiResponse.payment_status !== 'FAILED');

      setBookingStatus(isBookingSuccessful ? 'success' : 'failed');
      setBookingDetails(displayData);

    } catch (error) {
      let errorMessage = 'Unable to load booking details. Please try again later.';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        const apiError = error as any;
        if (apiError.response?.data?.message) {
          errorMessage = apiError.response.data.message;
        } else if (apiError.message) {
          errorMessage = apiError.message;
        }
      }

      setError(errorMessage);
      setBookingStatus('failed');
    } finally {
    }
  }, [params,setBookingDetails,setBookingStatus,setCurrentBookingReference,setError,setIsLoading]);

  useEffect(() => {
    const sendFeedbackAndFetchDetails = async () => {
      let bookingReference: string | null = null;

      // First, get the booking reference from the URL
      if (typeof window !== "undefined") {
        const urlParams = new URLSearchParams(window.location.search);
        const reference = urlParams.get("bookingReference");
        bookingReference = reference || null;
      }

      // If there's no reference, we probably don't need to do anything.
      if (!bookingReference) {
        // You could optionally call fetchBookingDetails("") here if that's desired behavior.
        return;
      }

      // 1. Prepare the webhook payload using the bookingReference as the order_id
      const webhookPayload: BookingStatusUpdateWebhookPayload = {
        type: 'PAYMENT_SUCCESS_WEBHOOK',
        event_time: new Date().toISOString(),
        data: {
          order: {
            order_id: bookingReference, // Dynamically set from the URL
            order_amount: 2,
            order_currency: "INR",
            order_tags: null
          },
          payment: {
            cf_payment_id: "**********",
            payment_status: "SUCCESS",
            payment_amount: 1,
            payment_currency: "INR",
            payment_message: "00::Transaction success",
            payment_time: new Date().toISOString(),
            bank_reference: "************",
            auth_id: null,
            payment_method: {
              upi: {
                channel: "collect",
                upi_id: "suhasg6@ybl"
              }
            },
            payment_group: "upi"
          },
          customer_details: {
            customer_name: null,
            customer_id: "7112AAA812234",
            customer_email: "<EMAIL>",
            customer_phone: "**********"
          },
          payment_gateway_details: {
            gateway_name: "CASHFREE",
            gateway_order_id: "**********",
            gateway_payment_id: "**********",
            gateway_order_reference_id: "abc_124",
            gateway_settlement: "CASHFREE",
            gateway_status_code: null
          },
          payment_offers: [{
            offer_id: "0f05e1d0-fbf8-4c9c-a1f0-814c7b2abdba",
            offer_type: "DISCOUNT",
            offer_meta: {
              offer_title: "50% off on UPI",
              offer_description: "50% off for testing",
              offer_code: "UPI50",
              offer_start_time: "2022-11-09T06:23:25.972Z",
              offer_end_time: "2023-02-27T18:30:00Z"
            },
            offer_redemption: {
              redemption_status: "SUCCESS",
              discount_amount: 1,
              cashback_amount: 0
            }
          }]
        }
      };

      try {
        // 2. Call the webhook feedback API with a POST request
        const feedbackUrl = 'http://************:30003/api/v1/webhooks/cashfree/feedback';
        const response = await fetch(feedbackUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(webhookPayload),
        });

        if (!response.ok) {
          // If the API call fails, log the error but continue to fetch booking details
          console.error('Webhook feedback API failed:', response.statusText);
        } else {
          console.log('Webhook feedback sent successfully.');
        }

      } catch (error) {
        console.error('Error sending webhook feedback:', error);
      } finally {
        // 3. Finally, call fetchBookingDetails AFTER the API call is complete (success or fail)
        setTimeout(() => {
          fetchBookingDetails(bookingReference);          
        }, 5000);
      }
    };

    if(isNoDataAvailable){
      sendFeedbackAndFetchDetails();
    }

  }, [fetchBookingDetails,isNoDataAvailable]);

  // useEffect for the countdown timer
  useEffect(() => {
    // Start timer only if booking details are loaded, payment was successful,
    // but the final booking status is not yet 'CONFIRMED'.
    if (bookingDetails && bookingStatus === 'success' && bookingDetails.booking.bookingStatus !== 'CONFIRMED') {
      const timer = setInterval(() => {
        setCountdown(prevCountdown => (prevCountdown > 0 ? prevCountdown - 1 : 0));
      }, 1000);

      // Cleanup interval on component unmount or when dependencies change
      return () => clearInterval(timer);
    }
  }, [bookingDetails, bookingStatus]);

  const handlePrint = () => {
    if (typeof window !== 'undefined') {
      window.print();
    }
  };

  const handleRetry = () => {
    // Redirect back to booking page
    router.push('/BookingPreviewPage');
  };

  const formatCurrency = (amount: number, currency: string = 'INR') => {
    if (currency === 'INR') {
      return `₹${amount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
    return `${currency} ${amount.toFixed(2)}`;
  };

  const formatDate = (dateString: string): string => {
    try {
      if (!dateString) return 'Date not available';
      
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.warn('Invalid date string provided to formatDate:', dateString);
        return 'Invalid date';
      }
      
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', { dateString, error });
      return 'Date not available';
    }
  };

  // Formats seconds into a HH:MM:SS string
  const formatCountdown = (totalSeconds: number): string => {
    if (totalSeconds <= 0) return "00:00:00";
    const hours = Math.floor(totalSeconds / 3600).toString().padStart(2, '0');
    const minutes = Math.floor((totalSeconds % 3600) / 60).toString().padStart(2, '0');
    const seconds = (totalSeconds % 60).toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  };

  // Render loading state
  const renderLoadingContent = () => (
    <BookingConfirmationShimmer />
  );

  // Render error state
  const renderErrorContent = () => (
    <div className="error-container">
      <i className="fa-solid fa-triangle-exclamation error-icon"></i>
      <h2 className="error-title">Unable to Load Booking Details</h2>
      <p className="error-message">{error}</p>
      <div className="error-actions">
        <button onClick={() => window.location.reload()} className="btn primary">
          <i className="fa-solid fa-rotate-right"></i> Try Again
        </button>
        <Link href="/profile" className="btn secondary">
          <i className="fa-solid fa-user"></i> My Bookings
        </Link>
        <Link href="/contact" className="btn secondary">
          <i className="fa-solid fa-headset"></i> Contact Support
        </Link>
      </div>
    </div>
  );

  // Render the success confirmation UI
  const renderSuccessContent = () => {
    if (!bookingDetails) return null;

    return (
      <>
        <div className="confirmation-header success">
          <i className="fa-solid fa-circle-check"></i>
          <h1>Payment Successful</h1>
          <p>Your payment has been processed. Awaiting final booking confirmation.</p>
        </div>

        <div className="booking-status-container">
          <div className="status-header">
            <h2>Current Booking Status</h2>
            <span className={`status-badge ${bookingDetails.booking.bookingStatus.toLowerCase()}`}>
              {bookingDetails.booking.bookingStatus}
            </span>
          </div>
          {/* {bookingDetails.booking.bookingStatus !== 'CONFIRMED' && (
            <div className="status-pending-info">
              <i className="fa-solid fa-hourglass-half"></i>
              <div className="pending-text">
                <p>Your booking is being processed. This can take up to 2 hours.</p>
                {countdown > 0 ? (
                  <div className="countdown-timer">
                    <span>Confirmation expected within: </span>
                    <strong>{formatCountdown(countdown)}</strong>
                  </div>
                ) : (
                  <p>Please check your email or the "My Bookings" page for the final status.</p>
                )}
              </div>
            </div>
          )} */}
          {bookingDetails.booking.bookingStatus !== 'CONFIRMED' && (
            <div className="status-pending-info">
              <i className="fa-solid fa-hourglass-half"></i>
              <div className="pending-text">
                <p>Your booking will be confirmed soon.</p>
                <div className="booking-ref-line">
                  <span>Booking Reference: </span>
                  <strong>{bookingDetails.confirmationNumber}</strong>
                </div>
              </div>
            </div>
          )}
        </div>
{/* 
        <div className="confirmation-code">
          <p>Confirmation Code: <strong>{bookingDetails.confirmationNumber}</strong></p>
        </div> */}

        <div className="confirmation-details">
          <div className="section">
            <h2>Reservation Details</h2>
            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-hotel"></i>
                  <div className="detail-text">
                    <label>Hotel</label>
                    <p>{bookingDetails.hotel.name}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-bed"></i>
                  <div className="detail-text">
                    <label>Room Type</label>
                    <p>{bookingDetails.booking.roomType}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-in</label>
                    <p>{formatDate(bookingDetails.booking.checkIn)}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-out</label>
                    <p>{formatDate(bookingDetails.booking.checkOut)}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-user"></i>
                  <div className="detail-text">
                    <label>Guests</label>
                    <p>{bookingDetails.booking.guests} Adults</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-moon"></i>
                  <div className="detail-text">
                    <label>Length of Stay</label>
                    <p>{bookingDetails.booking.nights} Nights</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="section">
            <h2>Payment Summary</h2>
            <div className="payment-summary">
              <div className="payment-row">
                <span>Room Rate ({bookingDetails.booking.nights} nights)</span>
                <span>{formatCurrency(bookingDetails.pricing.baseRate, bookingDetails.pricing.currency)}</span>
              </div>
              <div className="payment-row">
                <span>Taxes & Fees</span>
                <span>{formatCurrency(bookingDetails.pricing.taxes, bookingDetails.pricing.currency)}</span>
              </div>
              <div className="payment-row total">
                <span>Total Amount</span>
                <span>{formatCurrency(bookingDetails.pricing.totalAmount, bookingDetails.pricing.currency)}</span>
              </div>
              {/* <div className="payment-method">
                <span>Payment Status:</span>
                <span>{bookingDetails.paymentStatus}</span>
              </div> */}
            </div>
          </div>

          <div className="section info-section">
            <div className="info-item">
              <i className="fa-solid fa-circle-info"></i>
              <p>Check-in time starts at {bookingDetails.hotel.checkInTime}, and check-out time is until {bookingDetails.hotel.checkOutTime}.</p>
            </div>
            <div className="info-item">
              <i className="fa-solid fa-envelope"></i>
              <p>A confirmation email has been sent to {bookingDetails.guest.email}.</p>
            </div>
            {bookingDetails.hotel.phone && (
              <div className="info-item">
                <i className="fa-solid fa-phone"></i>
                <p>Hotel Contact: {bookingDetails.hotel.phone}</p>
              </div>
            )}
          </div>

          <div className="actions">
            <Link
              href={currentBookingReference ? `/Itinerary?bookingReference=${currentBookingReference}` : "/Itinerary"}
              className="btn secondary"
            >
              View Itinerary
            </Link>
            <Link href="/profile" className="btn secondary">
              My Bookings
            </Link>
            <button onClick={handlePrint} className="btn primary">
              <i className="fa-solid fa-print"></i> Print
            </button>
          </div>
        </div>
      </>
    );
  };

  // Render the booking failed UI
  const renderFailedContent = () => {
    if (!bookingDetails) return null;

    return (
      <>
        <div className="confirmation-header failed">
          <i className="fa-solid fa-circle-xmark"></i>
          <h1>Booking Failed</h1>
          <p>We encountered a problem with your reservation.</p>
        </div>

        <div className="error-message">
          <p>{error || 'Your booking could not be completed at this time.'}</p>
        </div>

        <div className="confirmation-details">
          <div className="section">
            <h2>Booking Details</h2>
            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-hotel"></i>
                  <div className="detail-text">
                    <label>Hotel</label>
                    <p>{bookingDetails.hotel.name}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-bed"></i>
                  <div className="detail-text">
                    <label>Room Type</label>
                    <p>{bookingDetails.booking.roomType}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-in</label>
                    <p>{formatDate(bookingDetails.booking.checkIn)}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-out</label>
                    <p>{formatDate(bookingDetails.booking.checkOut)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="section payment-failed-section">
            <div className="info-item">
              <i className="fa-solid fa-triangle-exclamation"></i>
              <p>Your booking was not completed. Please try again or use a different payment method.</p>
            </div>
            <div className="info-item">
              <i className="fa-solid fa-credit-card"></i>
              <p>No charges have been made to your payment method.</p>
            </div>
            {/* {bookingDetails.paymentStatus && (
              <div className="info-item">
                <i className="fa-solid fa-info-circle"></i>
                <p>Payment Status: {bookingDetails.paymentStatus}</p>
              </div>
            )} */}
          </div>

          <div className="actions">
            <button onClick={handleRetry} className="btn primary">
              <i className="fa-solid fa-rotate-right"></i> Try Again
            </button>
            <Link href="/contact" className="btn secondary">
              <i className="fa-solid fa-headset"></i> Contact Support
            </Link>
          </div>
        </div>
      </>
    );
  };

  // Main render logic
  const renderContent = () => {
    if (isLoading) {
      return renderLoadingContent();
    }

    if (error && !bookingDetails) {
      return renderErrorContent();
    }

    if (bookingStatus === 'success') {
      return renderSuccessContent();
    } else {
      return renderFailedContent();
    }
  };

  return (
    <div className={`booking-confirmation ${bookingStatus}`}>
      {renderContent()}

      {/* Only show footer if not in loading or error state */}
      {!isLoading && (error ? bookingDetails : true) && (
        <div className="footer">
          <p>Thank you for choosing KindAli</p>
          <div className="contact">
            <span><i className="fa-solid fa-phone"></i> **************</span>
            <span><i className="fa-solid fa-envelope"></i> <EMAIL></span>
          </div>
        </div>
      )}
    </div>
  );
}