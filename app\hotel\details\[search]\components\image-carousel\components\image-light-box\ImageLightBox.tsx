"use client";
import Image from "next/image";
import React, { useEffect, useState, useRef } from "react";
import "./ImageLightBox.scss";
import { ImageList } from "@/app/hotel/details/detail-page-internal.model";

interface ImageLightBoxProps {
  onClose: () => void;
  images: ImageList[];
}

function ImageLightBox({ onClose, images}: ImageLightBoxProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [slideDirection, setSlideDirection] = useState<'next' | 'prev'>('next');
  const [disableTransition, setDisableTransition] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Check if screen is mobile-sized
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 950);
    };
    
    // Initial check
    checkMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const startAutoSlide = () => {

    if (isMobile) return;
    
    stopAutoSlide();
    if (images && images.length > 0) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % images.length);
      }, 3000);
    }
  };

  const stopAutoSlide = () => {
    if (intervalRef.current) clearInterval(intervalRef.current);
  };

  // Update current index when scrolling stops
  useEffect(() => {
    if (isMobile && scrollContainerRef.current) {
      const handleScrollEnd = () => {
        if (scrollContainerRef.current) {
          const scrollPosition = scrollContainerRef.current.scrollLeft;
          const itemWidth = scrollContainerRef.current.offsetWidth;
          const newIndex = Math.round(scrollPosition / itemWidth);
          if (newIndex !== currentIndex && newIndex >= 0 && newIndex < images.length) {
            setCurrentIndex(newIndex);
          }
        }
      };

      const scrollContainer = scrollContainerRef.current;
      let scrollTimeout: NodeJS.Timeout;

      const handleScroll = () => {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(handleScrollEnd, 50);
      };

      scrollContainer.addEventListener('scroll', handleScroll);
      return () => {
        scrollContainer.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeout);
      };
    }
  }, [isMobile, currentIndex, images]);

  // Ensure scroll position matches current index
  useEffect(() => {
    if (isMobile && scrollContainerRef.current) {
      const itemWidth = scrollContainerRef.current.offsetWidth;
      scrollContainerRef.current.scrollTo({
        left: currentIndex * itemWidth,
        behavior: 'smooth'
      });
    }
  }, [currentIndex, isMobile]);

  useEffect(() => {
    if (!isMobile) {
      startAutoSlide();
    }
    return () => stopAutoSlide(); // Cleanup on unmount
  }, [isMobile]);

  const nextSlide = () => {
    stopAutoSlide();
    setSlideDirection('next'); // Set direction to next
    setDisableTransition(false);
    if (images && images.length > 0) {
      setCurrentIndex((prev) => (prev + 1) % images.length);
    }
  };
  
  const prevSlide = () => {
    stopAutoSlide();
    setSlideDirection('prev'); // Set direction to prev
    setDisableTransition(false);
    if (images && images.length > 0) {
      setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
    }
  };

  const handleThumbnailClick = (index: number) => {
    stopAutoSlide();
    setDisableTransition(true); // Disable transition
    setCurrentIndex(index);
  };

  // Safety check to prevent errors if images array is empty
  if (!images || images.length === 0) {
    return (
      <div className="image-light-box-container">
        <div className="close-btn" onClick={onClose}>
          <i className="fa-solid fa-xmark"></i>
        </div>
        <div className="image-light-box">
          <div className="no-images">No images available</div>
        </div>
      </div>
    );
  }

  return (
    <div className="image-light-box-container">
      {/* Close Button */}
      <div className="close-btn" onClick={onClose}>
        <i className="fa-solid fa-xmark"></i>
      </div>

      <div 
        className={`image-light-box ${isMobile ? 'mobile' : ''}`} 
        onMouseEnter={!isMobile ? startAutoSlide : undefined} 
        onMouseLeave={!isMobile ? startAutoSlide : undefined}
      >
        {/* Image Counter */}
        <div className="image-counter">
          {currentIndex + 1}/{images.length}
        </div>

        {!isMobile && currentIndex > 0 && (
          <button className="arrow left" onClick={prevSlide}>
            <i className="fa-solid fa-chevron-left"></i>
          </button>
        )}

        {isMobile ? (
          // Mobile horizontal scroll container
          <div 
            className="mobile-scroll-container" 
            ref={scrollContainerRef}
          >
            {images.map((image, index) => (
              <div className="mobile-image-item" key={index}>
                <Image 
                  src={image?.url} 
                  alt={image?.caption || "hotel image"} 
                  fill 
                  style={{ objectFit: "cover" }}
                />
              </div>
            ))}
          </div>
        ) : (
          // Desktop single image view
          <div className={`image ${slideDirection} ${disableTransition ? "no-transition" : ""}`} key={currentIndex}>
            <Image 
              src={images[currentIndex]?.url} 
              alt={images[currentIndex]?.caption || "hotel image"} 
              fill 
              style={{ objectFit: "cover" }}  
            />
          </div>
        )}

        {!isMobile && currentIndex < images.length - 1 && (
          <button className="arrow right" onClick={nextSlide}>
            <i className="fa-solid fa-chevron-right"></i>
          </button>
        )}

        {/* Thumbnail Container - only show on desktop */}
        {!isMobile && (
          <div className="thumbnail-container">
            {images.map((image, index) => (
              <div
                key={index}
                className={`thumbnail ${index === currentIndex ? "active" : ""}`}
                onClick={() => handleThumbnailClick(index)}
                onMouseEnter={stopAutoSlide}
                onMouseLeave={startAutoSlide}
              >
                <Image 
                  src={image?.url} 
                  alt={image?.caption || "hotel image"} 
                  fill 
                  style={{ objectFit: "cover" }} 
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default ImageLightBox;