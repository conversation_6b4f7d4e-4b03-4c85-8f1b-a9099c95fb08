//import { HotelDetailResponse } from "@/app/HotelDetail/hotel-detail-result.model"
import { RoomsAndRatesApiResponse, RoomsAndRatesRequest } from "@/models/hotel/rooms-and-rates.model";
import apiService from "../api-service"
import { HotelDetailApiResponse } from "@/app/HotelDetail/hotel-detail-api-response.model";

export const getHotelDetails = async (hotelId: string): Promise<{ data: HotelDetailApiResponse }> => {
    const response = await apiService.get2<HotelDetailApiResponse>(`hotels/${hotelId}/details`);
    return { data: response };
}

export const getRoomsAndRates = async (payload:RoomsAndRatesRequest ): Promise<RoomsAndRatesApiResponse> => {
    const response = await apiService.post2<RoomsAndRatesApiResponse>("search/rooms-and-rates",payload);
    return response;
}