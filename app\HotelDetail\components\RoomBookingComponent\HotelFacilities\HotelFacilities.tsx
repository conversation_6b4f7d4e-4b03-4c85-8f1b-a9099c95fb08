"use client";
import React, { forwardRef } from 'react';
import './HotelFacilities.scss';
import { useTranslation } from '@/app/hooks/useTranslation';
import { AmenityDetails } from '@/app/HotelDetail/hotel-detail-result.model';
import { Facility, HotelDetailApiResponse } from '@/app/HotelDetail/hotel-detail-api-response.model';


type Amenity = {
  name: string;
  amenityUrl: string;
  amenityRank: number;
  categoryName: string;
  categoryUrl: string;
  categoryRank: number;
};

interface FacilityItem {
  icon: string;
  title: string;
  items: string[];
}

interface HotelFacilitiesProps {
  hotelData?: HotelDetailApiResponse; // Primary backend data
  backendFacilities?: Facility[]; // Direct facilities array
  amenities?: string[]; // Simple amenities array
  // Legacy props (kept for backward compatibility)
  facilities?: FacilityItem[][];
  amenityDetails?: AmenityDetails;
}

const HotelFacilities = forwardRef<HTMLDivElement, HotelFacilitiesProps>(({
  hotelData,
  backendFacilities,
  amenities,
  // Legacy props
  facilities,
  amenityDetails
}, ref) => {
  const { t } = useTranslation();

  // Helper function to get icon for facility type
  const getIconForFacility = (facilityName: string) => {
    const iconMap: { [key: string]: string } = {
      "Swimming Pool": "fa-swimming-pool",
      "Fitness Center": "fa-dumbbell",
      "Spa & Wellness": "fa-spa",
      "Spa": "fa-spa",
      "Dining": "fa-utensils",
      "Restaurant": "fa-utensils",
      "Business Services": "fa-briefcase",
      "Business Center": "fa-building",
      "Free WiFi": "fa-wifi",
      "WiFi": "fa-wifi",
      "Internet": "fa-wifi",
      "Parking": "fa-car",
      "Transportation": "fa-car",
      "Room Service": "fa-bell-concierge",
      "Concierge": "fa-bell-concierge",
      "Security": "fa-shield-halved",
      "Safety": "fa-shield-halved",
      "Accessibility": "fa-wheelchair",
      "Laundry": "fa-shirt",
      "Cleaning": "fa-broom",
      "Entertainment": "fa-tv",
      "Kitchen": "fa-kitchen-set",
      "Air Conditioning": "fa-snowflake",
      "Heating": "fa-fire",
      "Pet Services": "fa-paw"
    };

    // Find matching icon by checking if facility name contains any key
    for (const [key, icon] of Object.entries(iconMap)) {
      if (facilityName.toLowerCase().includes(key.toLowerCase())) {
        return icon;
      }
    }

    return "fa-star"; // Default icon
  };

  // Convert backend facilities to FacilityItem format
  const convertBackendFacilitiesToCategories = (facilities: Facility[]): FacilityItem[][] => {
    const facilityItems: FacilityItem[] = facilities.map(facility => ({
      icon: getIconForFacility(facility.name),
      title: facility.name,
      items: facility.details.map(detail => detail.content)
    }));

    // Split into 3 columns for better layout
    const chunkSize = Math.ceil(facilityItems.length / 3);
    const facilityCategories: FacilityItem[][] = [];

    for (let i = 0; i < facilityItems.length; i += chunkSize) {
      facilityCategories.push(facilityItems.slice(i, i + chunkSize));
    }

    return facilityCategories;
  };

  // Convert simple amenities array to FacilityItem format
  const convertAmenitiesToCategories = (amenitiesArray: string[]): FacilityItem[][] => {
    const facilityItems: FacilityItem[] = amenitiesArray.map(amenity => ({
      icon: getIconForFacility(amenity),
      title: amenity,
      items: ["Available"] // Simple amenities don't have details
    }));

    // Split into 3 columns
    const chunkSize = Math.ceil(facilityItems.length / 3);
    const facilityCategories: FacilityItem[][] = [];

    for (let i = 0; i < facilityItems.length; i += chunkSize) {
      facilityCategories.push(facilityItems.slice(i, i + chunkSize));
    }

    return facilityCategories;
  };

  // Use provided facilities or fallback to default data
  // const facilityCategories = props.facilities || [
  //   [
  //     { icon: "fa-bed", title: "Room amenities", items: ["Air-conditioned rooms", "Daily housekeeping", "Fan", "Free drinking water", "Hair dryer", "Ironing facilities", "Study table", "Tea/Coffee maker", "Safety Deposit Box"] },
  //     { icon: "fa-bath", title: "Bathroom", items: ["Shower"] },
  //     { icon: "fa-car", title: "Transfers and transport", items: ["Onsite car parking", "Airport transfer", "Car rental service", "Shuttle service", "Taxi/Cab service"] },
  //     { icon: "fa-key", title: "Access", items: ["24*7 Front Desk", "Non-smoking rooms", "Private Check-in/out"] },
  //     { icon: "fa-shield-halved", title: "Safety and security", items: ["24*7 Security", "CCTV in common areas", "Fire extinguisher"] }
  //   ],
  //   [
  //     { icon: "fa-wifi", title: "Internet Access", items: ["Internet services", "Paid Wi-Fi"] },
  //     { icon: "fa-utensils", title: "Kitchen", items: ["Tea/Coffee maker"] },
  //     { icon: "fa-wine-glass-empty", title: "Food and drinks", items: ["Continental Breakfast", "24*7 Room Service", "Bar", "Coffee Shop", "In-Room Breakfast"] },
  //     { icon: "fa-futbol", title: "Activities and sports", items: ["Indoor Swimming Pool", "Fitness Centre", "Spa/sauna", "Bicycle Rental", "Ticket Services", "Tours"] },
  //     { icon: "fa-wheelchair", title: "Accessibility", items: ["Elevator access"] }
  //   ],
  //   [
  //     { icon: "fa-bell-concierge", title: "Services and conveniences", items: ["Facilities for Differently-Abled Guests", "Luggage Storage", "Laundry Service", "Chapel", "Doorman", "Dry-Cleaning", "Library", "Postal Service"] },
  //     { icon: "fa-shield-virus", title: "Safety and cleanliness", items: ["Cashless Payment Service", "Common Area Disinfection (Daily)", "Doctor/Nurse on Call", "Free Face Masks", "No Shared Stationery"] },
  //     { icon: "fa-language", title: "Languages spoken", items: ["English", "Hindi"] }
  //   ]
  // ];

  function convertAmenitiesToFacilityCategories(amenities: Amenity[]): FacilityItem[][] {
  const categoryMap = new Map<string, { icon: string; title: string; items: string[]; rank: number }>();

  amenities.forEach((amenity) => {
    if (!categoryMap.has(amenity.categoryName)) {
      categoryMap.set(amenity.categoryName, {
        icon: amenity.categoryUrl,
        title: amenity.categoryName,
        items: [],
        rank: amenity.categoryRank
      });
    }
    categoryMap.get(amenity.categoryName)!.items.push(amenity.name);
  });

  const sortedCategories = Array.from(categoryMap.values()).sort((a, b) => a.rank - b.rank);
  const chunkSize = Math.ceil(sortedCategories.length / 3);
  const facilityCategories: FacilityItem[][] = [];

  for (let i = 0; i < sortedCategories.length; i += chunkSize) {
    const chunk = sortedCategories.slice(i, i + chunkSize).map(({ icon, title, items }) => ({
      icon,
      title,
      items
    }));
    facilityCategories.push(chunk);
  }

  return facilityCategories;
}

  // Determine which data source to use (priority order)
  let facilityCategories: FacilityItem[][] = [];

  if (hotelData?.facilities && hotelData.facilities.length > 0) {
    // Use backend facilities (highest priority)
    facilityCategories = convertBackendFacilitiesToCategories(hotelData.facilities);
  } else if (backendFacilities && backendFacilities.length > 0) {
    // Use direct facilities prop
    facilityCategories = convertBackendFacilitiesToCategories(backendFacilities);
  } else if (hotelData?.amenities && hotelData.amenities.length > 0) {
    // Use backend amenities array
    facilityCategories = convertAmenitiesToCategories(hotelData.amenities);
  } else if (amenities && amenities.length > 0) {
    // Use direct amenities prop
    facilityCategories = convertAmenitiesToCategories(amenities);
  } else if (facilities && facilities.length > 0) {
    // Use legacy facilities prop
    facilityCategories = facilities;
  } else if (amenityDetails?.amenities) {
    // Fallback to legacy amenityDetails
    facilityCategories = convertAmenitiesToFacilityCategories(amenityDetails.amenities);
  } else {
    // No data available
    facilityCategories = [];
  }


  

  return (
    <div ref={ref} className="hotel-facilities-section">
      <h2 className="section-title">{t('hotel.detail.hotelFacilities')}</h2>

      {facilityCategories.length > 0 ? (
        <div className="facilities-container">
          {facilityCategories.map((column, columnIndex) => (
            <div key={columnIndex} className="facilities-column">
              {column.map((facility, facilityIndex) => (
                <div key={facilityIndex} className="facility-box">
                  <div className="facility-header">
                    <i className={`fa-solid ${facility.icon}`}></i>
                    <h3>{facility.title}</h3>
                  </div>
                  <div className="facility-content">
                    {facility.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="facility-item">{item}</div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      ) : (
        <div className="no-facilities-message" style={{
          textAlign: 'center',
          padding: '40px 20px',
          color: '#666',
          fontSize: '16px'
        }}>
          <i className="fa-solid fa-info-circle" style={{ fontSize: '24px', marginBottom: '10px', display: 'block' }}></i>
          {t('hotel.detail.noFacilitiesAvailable') || 'Facility information will be available soon.'}
        </div>
      )}
    </div>
  );
});

HotelFacilities.displayName = "HotelFacilities";

export default HotelFacilities;