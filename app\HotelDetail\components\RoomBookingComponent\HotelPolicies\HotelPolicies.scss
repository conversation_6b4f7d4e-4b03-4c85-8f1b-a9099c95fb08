@use "/styles/variable" as *;

.hotel-policies-section {
  padding: 40px 20px; // Added horizontal padding for smaller screens

  h6 {
    font-size: 22px;
    color: #17181c;
    font-weight: 700;
    margin-bottom: 30px;
    border-left: 4px solid $primary-color;
    padding-left: 15px;
  }

  .hotel-policies-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 30px;

    // Common styles for section headings
    .section-heading {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      i {
        color: $primary-color;
        margin-right: 10px;
        font-size: 16px;
      }

      span {
        font-size: 18px;
        color: #17181c;
        font-weight: 600;
      }
    }

    // Common styles for policy blocks
    .policy-block {
      background-color: rgba($primary-color, 0.05);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 25px;
      border-left: 3px solid $primary-color;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      width: 100%; // Make sure all blocks take full width by default

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
    }

    // Backend policies styling
    .backend-policies {
      width: 100%;

      .backend-policies-list {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 0;
      }

      .policy-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);

        &:last-child {
          border-bottom: none;
        }

        .policy-header {
          display: flex;
          align-items: center;
          gap: 10px;

          i {
            color: $primary-color;
            font-size: 16px;
            width: 20px;
            text-align: center;
            flex-shrink: 0; // Prevent icon from shrinking
          }

          .policy-name {
            font-weight: 600;
            color: #17181c;
            font-size: 16px;
            text-transform: capitalize;
          }
        }

        .policy-description {
          margin-left: 30px;
          color: #666;
          font-size: 14px;
          line-height: 1.5;
        }
      }

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    // Check-in/Check-out section
    .hotel-checkInOut-details {
      @extend .policy-block;
      display: flex;
      flex-direction: column;
      align-items: start;
      white-space: nowrap;

      .label {
        @extend .section-heading;
        margin-bottom: 0;
      }

      .details {
        display: list-item;
       list-style-type: disc;
        margin-left: 20px;
        font-size: 16px;
        color: #17181c;
        font-weight: 500;
      }
    }

    // Children and extra beds section
    .children-extra-beds {
      @extend .policy-block;
      display: flex;
      flex-direction: column;

      &__heading {
        @extend .section-heading;
      }

      &__intro-list {
        width: 100%;
      }

      &__intro {
        list-style-type: none;
        margin-bottom: 15px;
        padding-left: 0; // Remove default padding

        li {
          margin-bottom: 10px;
          position: relative;
          padding-left: 20px;

          &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 8px;
            width: 8px;
            height: 8px;
            background-color: $primary-color;
            border-radius: 50%;
          }
        }
      }

      &__list {
        display: flex;
        flex-direction: column;
        padding-left: 0; // Remove default padding
      }

      &__item {
        display: flex;
        flex-direction: column;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px dashed rgba($primary-color, 0.3);
        min-width: 280px;

        &:last-child {
          border-bottom: none;
          padding-bottom: 0;
        }

        span {
          font-size: 16px;
          color: #17181c;
          font-weight: 600;
          margin-bottom: 5px;
          color: $primary-color;
        }

        p {
          font-size: 15px;
          color: #17181c;
          font-weight: 400;
          line-height: 1.5;
          margin-top: 0; // Reset margin
        }
      }
    }

    // Property information section
    .propertyInformation {
      @extend .policy-block;
      display: flex;
      flex-direction: column;

      &__heading {
        @extend .section-heading;

        span {
          @media (max-width: 576px) {
            font-size: 14px;
          }
        }
      }

      &__list {
        width: 100%;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        column-gap: 30px;
        padding-left: 0; // Remove default padding
      }

      &__item {
        width: calc(50% - 15px);
        font-size: 15px;
        font-weight: 500;
        color: #17181c;
        margin-bottom: 12px;
        position: relative;
        padding-left: 18px;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 8px;
          width: 8px;
          height: 8px;
          background-color: $primary-color;
          border-radius: 50%;
        }

        @media(max-width: 576px) {
          width: 100%;
        }
      }
    }
  }

  // Media queries for responsiveness
  @media (max-width: 1200px) {
    h6 {
      font-size: 22px;
    }

    .hotel-policies-container {
      .section-heading {
        span {
          font-size: 17px;
        }
      }
    }
  }

  @media (max-width: 992px) {
    padding: 30px 15px;

    h6 {
      font-size: 20px;
      margin-bottom: 25px;
    }

    .hotel-policies-container {
      gap: 20px;

      .policy-block {
        padding: 15px;
      }

      .section-heading {
        i {
          font-size: 15px;
        }

        span {
          font-size: 16px;
        }
      }

      .children-extra-beds,
      .propertyInformation {
        &__item {
          span {
            font-size: 15px;
          }

          p {
            font-size: 14px;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 25px 0;

    h6 {
      font-size: 18px;
      margin-bottom: 20px;
    }

    .hotel-policies-container {
      flex-direction: column;
      gap: 15px;

      // Make the flex container for check-in/out responsive
      .flex {
        flex-direction: column;
        gap: 15px !important;
      }

      .hotel-checkInOut-details {
        .label {
          i {
            font-size: 14px;
          }

          span {
            font-size: 15px;
          }
        }

        .details {
          font-size: 15px;
        }
      }

      .propertyInformation {
        &__list {
          flex-direction: column;
        }

        &__item {
          font-size: 14px;
        }
      }
    }
  }

  @media (max-width: 640px) {
    h6 {
      font-size: 16px;
      margin-bottom: 15px;
      padding-left: 10px;
    }
  }

  @media (max-width: 576px) {
    padding: 20px 10px;

    h6 {
      font-size: 15px;
      margin-bottom: 15px;
      padding-left: 10px;
    }

    .hotel-policies-container {
      gap: 10px;

      .policy-block {
        padding: 12px;
        margin-bottom: 15px;
      }

      .section-heading {
        i {
          font-size: 14px;
        }

        span {
          font-size: 15px;
        }
      }

      .hotel-checkInOut-details {
        padding: 10px;
        .label {
          i {
            margin-right: 5px;
          }

          span {
            font-size: 14px;
          }
        }

        .details {
          font-size: 14px;
        }
      }

      .children-extra-beds {
        &__heading,
        .propertyInformation__heading {
          margin-bottom: 10px;

          i {
            font-size: 13px;
          }

          span {
            font-size: 15px;
          }
        }

        &__intro {
          li {
            font-size: 13px;
            padding-left: 15px;

            &:before {
              top: 6px;
              width: 6px;
              height: 6px;
            }
          }
        }

        &__item {
          margin-bottom: 12px;
          padding-bottom: 12px;

          span {
            font-size: 14px;
          }

          p {
            font-size: 13px;
          }
        }
      }

      .propertyInformation {
        &__item {
          font-size: 13px;
          padding-left: 15px;
          margin-bottom: 8px;

          &:before {
            top: 6px;
            width: 6px;
            height: 6px;
          }
        }
      }
    }
  }
}
