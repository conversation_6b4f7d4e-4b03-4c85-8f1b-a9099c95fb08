import { transformToBookingRequest, validateBookingDataAvailability } from '../bookingApiUtils';

// Mock the storage functions
jest.mock('@/utils/selectedRoomStorage', () => ({
  getSelectedRoom: jest.fn()
}));

jest.mock('@/utils/hotelDetailsStorage', () => ({
  getStoredHotelDetails: jest.fn()
}));

jest.mock('../bookingInitUtils', () => ({
  getStoredBookingInitData: jest.fn()
}));

describe('bookingApiUtils', () => {
  const mockFormData = {
    contactTitle: "Mr.",
    email: "<EMAIL>",
    mobile: "1234567890",
    nationality: "India",
    passportNumber: "A12345678",
    isBookingForSomeoneElse: false,
    guestRooms: [
      {
        title: "Mr.",
        firstName: "John",
        lastName: "Doe"
      }
    ]
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('transformToBookingRequest', () => {
    it('should transform form data to booking request format', () => {
      const result = transformToBookingRequest(mockFormData);
      
      expect(result).toHaveProperty('search_key');
      expect(result).toHaveProperty('hotel_id');
      expect(result).toHaveProperty('rateIds');
      expect(result).toHaveProperty('user_id');
      expect(result).toHaveProperty('roomsAllocations');
      expect(result).toHaveProperty('billingContact');
      
      expect(result.roomsAllocations).toHaveLength(1);
      expect(result.roomsAllocations[0].guests).toHaveLength(1);
      expect(result.billingContact.contact.email).toBe('<EMAIL>');
    });
  });

  describe('validateBookingDataAvailability', () => {
    it('should return validation result', () => {
      const result = validateBookingDataAvailability();
      
      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('missingData');
      expect(Array.isArray(result.missingData)).toBe(true);
    });
  });
});
