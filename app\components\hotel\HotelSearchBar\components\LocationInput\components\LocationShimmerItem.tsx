const ShimmerItem = () => (
  <div className="flex items-center px-2 py-1.5 animate-pulse">
    <div className="w-5 h-5 bg-gray-200 rounded-full mr-1.5"></div>
    <div className="flex-1 min-w-0">
      <div className="flex items-center mb-1">
        <div className="h-3 bg-gray-200 rounded w-24 mr-2"></div>
        <div className="h-2 bg-gray-200 rounded w-12"></div>
      </div>
      <div className="h-2 bg-gray-200 rounded w-32"></div>
    </div>
  </div>
);

// Shimmer loading list
export const LocationShimmerItem = () => (
  <div className="py-1">
    {[...Array(5)].map((_, idx) => (
      <ShimmerItem key={idx} />
    ))}
  </div>
);
