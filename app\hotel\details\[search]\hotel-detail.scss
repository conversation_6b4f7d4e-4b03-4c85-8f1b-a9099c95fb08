@use "/styles/variable" as *;
@use "/styles/zIndex" as *;
@use "sass:color";

.hotel-detail-container{
    width: 100%;
    height: auto;
    padding: 10px 0;
    position: relative;

    .mobile-header-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;
        min-width: 0; // Allow flex item to shrink

        .hotel-name-with-rating {
          display: flex;
          align-items: center;
          gap: 8px;
          min-width: 0; // Allow flex item to shrink

          .hotel-name {
            font-size: 16px;
            font-weight: 600;
            color: #17181c;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
            //flex: 1;
            min-width: 0; // Allow flex item to shrink
          }

          .rating-stars {
            display: flex;
            align-items: center;
            gap: 1px;
            flex-shrink: 0; // Prevent stars from shrinking

            i.fa-star {
              font-size: 10px;
              color: $rating_color;
            }
          }
        }

        .mobile-date-range {
          // Compact date range styling is handled in the DateRangeDisplay component
          margin: 0;
        }
    }


    .overview-section{
        display: flex;
        flex-direction: column;

        .route-path {
          display: flex;
          align-items: center;
          gap: 5px;
          flex-wrap: nowrap;
          margin-bottom: 16px;
          padding: 16px 8px 0 8px;
          font-size: 12px;
          span {
            color: $primary_color;
          }

          .fa-greater-than {
            font-size: 9px;
            margin-bottom: -2px;
          }

          @media (max-width: $isMobile) {
            display: none;
          }
        }
    }
}