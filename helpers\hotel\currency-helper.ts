import { CurrencyItem } from "@/models/common.model";

export function getConvertedCurrency(amount: number, currency: CurrencyItem): string {
  if (!currency) return amount.toString();

  const converted = currency.to_currency_code === "BHD" ? amount : amount * currency.rate;
  const formatted = converted.toFixed(currency.precision);

  return `${currency.currency_symbol_on_right ? currency.to_currency_code : currency.currency_symbol} ${formatted}`;
}
