"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { X } from "lucide-react";

// Local Imports
import "./hotel-detail-overview.scss";
import { MapboxMap } from "@/app/components/map-box-map/MapBoxMap";
import useScrollLock from "@/app/components/utilities/ScrollLock/useScrollLock";
import { HotelDetailApiResponse, Images } from "@/models/hotel/detail-page.model";
import GallerySlider from "@/app/components/gallery-slider/GallerySlider";
import { useMediaQuery } from "@/hooks/common-hooks";
import ImageGalleryDetail from "../image-gallery-detail/ImageGalleryDetail";

interface HotelOverviewComponentProps {
  images?: Images[];
  hotelData?: HotelDetailApiResponse;
}

// A simple placeholder component for when images are not available
const ImagePlaceholder = ({ className }: { className?: string }) => (
  <div className={`image-placeholder ${className || ''}`}>
    <span>No Image</span>
  </div>
);

function HotelOverviewComponent({ images = [], hotelData }: HotelOverviewComponentProps) {
  const [isPhotoGalleryModalActive, setIsPhotoGalleryModalActive] = useState(false);
  const [isMapModalActive, setIsMapModalActive] = useState(false);
  
  useScrollLock(isPhotoGalleryModalActive || isMapModalActive);
  const isMobile = useMediaQuery('(max-width: 768px)');

  // Handle Escape key to close map modal
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsMapModalActive(false);
      }
    };

    if (isMapModalActive) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => document.removeEventListener('keydown', handleEscapeKey);
    }
  }, [isMapModalActive]);

  // Image processing for the desktop grid
  const heroImage = images.find(img => img.is_hero_image) || images[0];
  const secondaryImages = images.filter(img => img.id !== heroImage?.id);
  const sideImages = secondaryImages.slice(0, 2);
  const bottomRowImages = secondaryImages.slice(2, 7);
  const remainingPhotos = Math.max(0, images.length - 8);

  const topAmenity = hotelData?.amenities?.[0];

  return (
    <div className="hotel-overview-container">
      {isMobile ? (
        // Mobile View: Render the Gallery Slider
        <GallerySlider images={images} />
      ) : (
        // Desktop View: Render the Image Grid
        <div className="hotel-overview-container__left-section">
          <div className="left-section-row1">
            <div className="left-section-row1__flex-col col1">
              {heroImage ? (
                <Image
                  src={heroImage.image_path}
                  alt={heroImage.alt_text || "Hotel main view"}
                  fill
                  style={{ objectFit: "cover" }}
                  sizes="(max-width: 1200px) 50vw, 33vw"
                  priority
                />
              ) : <ImagePlaceholder className="col1-placeholder" />}
            </div>
            <div className="left-section-row1__flex-col col2">
              {Array.from({ length: 2 }).map((_, index) => (
                <div className="sub-col" key={index}>
                  {sideImages[index] ? (
                    <Image
                      src={sideImages[index].image_path}
                      alt={sideImages[index].alt_text || `Hotel side view ${index + 1}`}
                      fill
                      style={{ objectFit: "cover" }}
                      sizes="(max-width: 1200px) 25vw, 17vw"
                    />
                  ) : <ImagePlaceholder />}
                </div>
              ))}
            </div>
          </div>

          <div className="left-section-row2">
            {Array.from({ length: 5 }).map((_, index) => {
              const image = bottomRowImages[index];
              const isLastImageSlot = index === 4;
              return (
                <div className="left-section-row2__col" key={index}>
                  {image ? (
                    <>
                      <Image
                        src={image.image_path}
                        alt={image.alt_text || `Hotel room ${index + 1}`}
                        fill
                        style={{ objectFit: "cover" }}
                        sizes="(max-width: 1200px) 10vw, 7vw"
                      />
                      {isLastImageSlot && remainingPhotos > 0 && (
                        <div className="image-overlay" onClick={() => setIsPhotoGalleryModalActive(true)}></div>
                      )}
                    </>
                  ) : <ImagePlaceholder />}
                </div>
              );
            })}
            {remainingPhotos > 0 && (
                <div className="morePhotos" onClick={() => setIsPhotoGalleryModalActive(true)}>
                    <i className="fa-solid fa-images"></i>
                    <span className="label">+{remainingPhotos} Photos</span>
                </div>
            )}
          </div>
        </div>
      )}

      {/* --- Right Section (Review & Map) --- */}
      <div className="hotel-overview-container__right-section">
        {topAmenity && (
          <div className="reviewFloater">
            <div className="best-review-score-card">
              <div className="best-review-score-card__label">{topAmenity}</div>
              <div className="best-review-score-card__count">{hotelData?.userRating || "N/A"}</div>
            </div>
          </div>
        )}
        <div className="hotel-search-map-container" style={{ height: '86%', borderRadius: '8px' }}>
          <MapboxMap
            center={hotelData?.geoLocationInfo ? [hotelData.geoLocationInfo.lon, hotelData.geoLocationInfo.lat] : [76.2673, 9.9312]}
            zoom={12}
            buttonText="Show on Map"
            onButtonClick={() => setIsMapModalActive(true)}
          />
        </div>
      </div>

      {/* --- Modals --- */}
      <ImageGalleryDetail
        isOpen={isPhotoGalleryModalActive}
        onClose={() => setIsPhotoGalleryModalActive(false)}
        hotelData={hotelData}
      />

      {isMapModalActive && (
        <div className="map-modal-overlay" onClick={() => setIsMapModalActive(false)}>
          <div className="map-modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="map-modal-header">
              <h2>Explore Area</h2>
              <button
                className="map-modal-close-btn"
                onClick={() => setIsMapModalActive(false)}
                aria-label="Close map"
              >
                <X size={24} />
              </button>
            </div>
            <div className="map-modal-body">
              {/* <ExploreMap hotelData={hotelData} isModal={true} /> */}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default HotelOverviewComponent;