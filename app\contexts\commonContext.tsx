"use client";
import React, { createContext, useContext, useState, ReactNode } from "react";
import { HotelSearchData, HotelSearchFormData } from "@/models/hotel/search-page.model";
import { CurrencyItem, User } from "@/models/common.model";
import { HotelDetailApiResponse } from "../HotelDetail/hotel-detail-api-response.model";
//import { HotelDetailResponse } from "../HotelDetail/hotel-detail-result.model";





interface CommonContextProps {
  isLoggedIn: boolean;
  setIsLoggedIn: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentPage: string;
  setCurrentPage: React.Dispatch<React.SetStateAction<string>>;
  userData: User | undefined;
  setUserData: React.Dispatch<React.SetStateAction<User | undefined>>;
  screenSize: number;
  setScreenSize: React.Dispatch<React.SetStateAction<number>>;
  hotelSearchFormData : HotelSearchFormData | undefined;
  setHotelSearchFormData: React.Dispatch<React.SetStateAction<HotelSearchFormData | undefined>>;
  hotelSearchData: HotelSearchData | undefined;
  setHotelSearchData: React.Dispatch<React.SetStateAction<HotelSearchData | undefined>>;
  hotelDetailsResponse : HotelDetailApiResponse | undefined;
  setHotelDetailsResponse : React.Dispatch<React.SetStateAction<HotelDetailApiResponse | undefined>>;
  selectedHotelId: string | undefined;
  setSelectedHotelId: React.Dispatch<React.SetStateAction<string | undefined>>;
  searchKey: string | undefined;
  setSearchKey: React.Dispatch<React.SetStateAction<string | undefined>>;
  isShareGroupVisible: boolean;
  setIsShareGroupVisible:  React.Dispatch<React.SetStateAction<boolean>>;
  isMobileModalOpen: boolean;
  setIsMobileModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  selectedCurrency: CurrencyItem;
  setSelectedCurrency: React.Dispatch<React.SetStateAction<CurrencyItem>>;
  currencyList: CurrencyItem[];
  setCurrencyList: React.Dispatch<React.SetStateAction<CurrencyItem[]>>;
  isLoginModalOpen: boolean;
  setIsLoginModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

// Create the context
const CommonContext = createContext<CommonContextProps | undefined>(undefined);

// Provider Component
export const CommonProvider = ({ children }: { children: ReactNode }) => {
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage]  = useState('');
  const [userData,setUserData] = useState<User | undefined>()
  const [screenSize, setScreenSize] = useState<number>(() => {
    if (typeof window !== "undefined") {
      return window.innerWidth;
    }
    return 1600; // Default fallback for SSR
  });
  const [hotelSearchFormData, setHotelSearchFormData] = useState<HotelSearchFormData>();
  const [isLoginModalOpen, setIsLoginModalOpen] = useState<boolean>(false);
  const [hotelSearchData, setHotelSearchData] = useState<HotelSearchData>();
  const [hotelDetailsResponse, setHotelDetailsResponse] = useState<HotelDetailApiResponse>();
  const [selectedHotelId, setSelectedHotelId] = useState<string>();
  const [searchKey, setSearchKey] = useState<string>();
  const [isShareGroupVisible, setIsShareGroupVisible] = useState<boolean>(false);
  const [isMobileModalOpen, setIsMobileModalOpen] = useState<boolean>(false);
  const [selectedCurrency, setSelectedCurrency] = useState<CurrencyItem>(  { id: 4, currency_name: "Bahraini Dinar", currency_symbol: "ب.د", currency_symbol_on_right: true, to_currency_code: "BHD", rate: 1, precision: 3, is_disabled_currency: false, is_disabled_conversion: false });
  const [currencyList, setCurrencyList] = useState<CurrencyItem[]>([]);


  return (
    <CommonContext.Provider value={{ isLoggedIn, setIsLoggedIn, isLoading, setIsLoading, currentPage, setCurrentPage , userData , setUserData , screenSize , setScreenSize , hotelSearchFormData, setHotelSearchFormData, hotelSearchData, setHotelSearchData, hotelDetailsResponse, setHotelDetailsResponse, selectedHotelId, setSelectedHotelId, searchKey, setSearchKey, isShareGroupVisible, setIsShareGroupVisible, isMobileModalOpen, setIsMobileModalOpen , selectedCurrency, setSelectedCurrency, currencyList, setCurrencyList , isLoginModalOpen, setIsLoginModalOpen }}>
      {children}
    </CommonContext.Provider>
  );
};

// Hook for easy access
export const useCommonContext = () => {
  const context = useContext(CommonContext);
  if (!context) {
    throw Error("useCommonContext must be used within a CommonProvider");
  }
  return context;
};
