@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

// Special Offers complete SCSS styles
.special-offers-main {
  width: 100%;
  margin-top: 30px;

  @media (min-width: $breakpoint-md) {
    margin-top: 2.5rem; // 40px
  }
}

.special-offers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem; // 24px

  @media (min-width: $breakpoint-md) {
    margin-bottom: 2rem; // 32px
  }

  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem; // 8px
  }
}

.special-offers-title {
  font-size: 1.125rem; // 18px
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;

  @media (min-width: $breakpoint-md) {
    font-size: 1.5rem; // 24px
  }
}

.special-offers-container {
  position: relative;
}

.special-offers-scroll {
  display: flex;
  gap: 1.25rem; // 20px
  overflow-x: auto;
  padding-bottom: 1rem; // 16px
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  @media (min-width: $breakpoint-md) {
    gap: 1rem; // 16px
  }
}

.offer-card {
  flex: 0 0 auto;
  width: 280px;
  height: 100px;
  background: white;
  border-radius: 1rem; // 16px
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  @media (min-width: $breakpoint-md) {
    width: 380px;
    height: 130px;
  }

  @media (min-width: $breakpoint-lg) {
    width: 420px;
    height: 140px;
  }
}

.offer-image-container {
  flex: 0 0 90px;
  position: relative;

  @media (min-width: $breakpoint-md) {
    flex: 0 0 130px;
  }

  @media (min-width: $breakpoint-lg) {
    flex: 0 0 140px;
  }
}

.offer-image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 1rem 0 0 1rem; // rounded-l-2xl
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: -1.25rem; // -20px equivalent to -right-5
    width: 2.5rem; // 40px equivalent to w-10
    height: 100%;
    background: white;
    border-radius: 50%;
    // z-index: z-index(base) + 2; // equivalent to z-[2]
  }

  img {
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.offer-content {
  flex: 1;
  padding: 10px 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 0;

  @media (min-width: $breakpoint-md) {
    padding: 16px 20px;
  }

  @media (min-width: $breakpoint-lg) {
    padding: 18px 22px;
  }
}

.offer-text-content {
  margin-bottom: 14px;
  overflow: hidden;
}

.offer-title {
  font-size: 13px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 5px 0;
  line-height: 1.25;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (min-width: $breakpoint-md) {
    font-size: 17px;
  }

  @media (min-width: $breakpoint-lg) {
    font-size: 19px;
    white-space: normal;
  }
}

.offer-subtitle {
  font-size: 10px;
  color: #555555;
  margin: 0 0 7px 0;
  font-weight: 500;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (min-width: $breakpoint-md) {
    font-size: 12px;
  }

  @media (min-width: $breakpoint-lg) {
    font-size: 13px;
    white-space: normal;
  }
}

.offer-description {
  font-size: 8px;
  color: #777777;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;

  @media (min-width: $breakpoint-md) {
    font-size: 10px;
    -webkit-line-clamp: 2;
  }

  @media (min-width: $breakpoint-lg) {
    -webkit-line-clamp: 2;
  }
}

.offer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.promo-code-button {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
  border: none;
  color: white;
  font-size: 9px;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &:hover {
    background: linear-gradient(135deg, #ff5252 0%, #ff4444 100%);
    transform: translateY(-1px);
  }

  @media (min-width: $breakpoint-md) {
    font-size: 12px;
    padding: 8px 16px;
  }

  @media (min-width: $breakpoint-lg) {
    padding: 8px 16px;
  }
}

.view-details-button {
  background: transparent;
  border: none;
  color: #007bff;
  font-size: 10px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;

  &:hover {
    color: #0056b3;

    .chevron-icon {
      transform: translateX(2px);
    }
  }

  @media (min-width: $breakpoint-md) {
    font-size: 13px;
  }
}

.chevron-icon {
  font-size: 10px;
  transition: transform 0.2s ease;
}
