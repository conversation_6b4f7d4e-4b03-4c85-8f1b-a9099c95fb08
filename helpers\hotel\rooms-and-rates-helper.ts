import {
  Tax,
  RoomRecommendation,
  EnrichedRecommendation,
  TransformedRoomsData,
  TransformedRoom
} from '@/models/hotel/rooms-and-rates.model';

const calculateTaxesFromAPI = (taxes: Tax[]): number => {
  if (!taxes) return 0;
  return taxes
    .filter(tax => !tax.isIncludedInBaseRate)
    .reduce((total, tax) => total + tax.amount, 0);
};


export const transformRoomsAndRates = (recommendations: RoomRecommendation[]): TransformedRoomsData => {
  if (!recommendations) {
    return [];
  }

  // Step 1: Enrich each recommendation with calculated pricing totals
  const enrichedRecommendations: EnrichedRecommendation[] = recommendations.map((rec) => {
    const { groupedRates } = rec;

    if (groupedRates.length === 0) {
      return {
        ...rec,
        totalBaseRate: 0, totalTaxesAndFees: 0, grandTotal: 0,
        totalOriginalPrice: 0, totalSavings: 0, hasDiscount: false, bestRate: null,
      };
    }

    const totalBaseRate = groupedRates.reduce((sum, current) => sum + current.rate.baseRate, 0);
    const totalTaxesAndFees = groupedRates.reduce((sum, current) => sum + calculateTaxesFromAPI(current.rate.taxes), 0);
    const grandTotal = totalBaseRate + totalTaxesAndFees;
    const bestRate = groupedRates.reduce((best, current) => current.rate.totalRate < best.rate.totalRate ? current : best);
    const totalOriginalPrice = groupedRates.reduce((sum, current) => {
      const originalRatePrice = current.rate.dailyRates.reduce((dailySum, dailyRate) =>
        dailySum + (dailyRate.amount + (dailyRate.discount || 0)), 0
      );
      return sum + originalRatePrice;
    }, 0);
    const hasDiscount = totalOriginalPrice > grandTotal;
    const totalSavings = hasDiscount ? totalOriginalPrice - grandTotal : 0;

    return {
      ...rec,
      totalBaseRate,
      totalTaxesAndFees,
      grandTotal,
      totalOriginalPrice,
      totalSavings,
      hasDiscount,
      bestRate,
    };
  });

  // Step 2: Group the enriched recommendations by room type
  const groupedByRoom = enrichedRecommendations.reduce((acc: { [key: string]: TransformedRoom }, rec) => {
    if (rec.groupedRates.length === 0) return acc;
    
    // Use the ID and name from the first room in the group for consistency
    const primaryRoomId = rec.groupedRates[0].room.id;
    const primaryRoomName = rec.groupedRates[0].room.name;
    
    if (!acc[primaryRoomId]) {
      acc[primaryRoomId] = {
        stdRoomId: primaryRoomId,
        roomName: primaryRoomName,
        roomdata: [],
      };
    }
    
    acc[primaryRoomId].roomdata.push(rec);
    return acc;
  }, {});

  return Object.values(groupedByRoom);
};