import React from 'react';

type RoomData = {
  roomdata: {
    groupedRates: unknown[];
  }[];
}[];

export type FiltersState = {
  breakfast: boolean;
  fullBoard: boolean;
  halfBoard: boolean;
  transfer: boolean;
  cancellationAvailable: boolean;
};

interface RecommendationListHeaderProps {
  filters: FiltersState;
  setFilters: React.Dispatch<React.SetStateAction<FiltersState>>;
  searchQuery: string;
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>;
  handleResetFilters: () => void;
  filteredRoomsData: RoomData;
}

const RecommendationListHeader: React.FC<RecommendationListHeaderProps> = ({
  filters,
  setFilters,
  searchQuery,
  setSearchQuery,
  handleResetFilters,
  filteredRoomsData,
}) => {
  const totalRooms = filteredRoomsData.reduce((count, roomGroup) =>
    count + roomGroup.roomdata.reduce((roomCount, recommendation) =>
      roomCount + recommendation.groupedRates.length, 0), 0
  );

  const isFilterActive = Object.values(filters).some(filter => filter) || searchQuery.trim();

  return (
    <div className="mb-6">
      <h2 className="text-2xl font-bold text-primary-color mb-4">Rooms & Rates</h2>

      <div className="flex flex-wrap items-center gap-4 mb-4">
        <span className="text-sm font-medium text-gray-700">Filter rooms by:</span>
        <div className="flex flex-wrap gap-3">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-primary-color focus:ring-primary-color"
              checked={filters.breakfast}
              onChange={(e) => setFilters(prev => ({ ...prev, breakfast: e.target.checked }))}
            />
            <span>Breakfast</span>
          </label>
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-primary-color focus:ring-primary-color"
              checked={filters.fullBoard}
              onChange={(e) => setFilters(prev => ({ ...prev, fullBoard: e.target.checked }))}
            />
            <span>Full Board</span>
          </label>
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-primary-color focus:ring-primary-color"
              checked={filters.halfBoard}
              onChange={(e) => setFilters(prev => ({ ...prev, halfBoard: e.target.checked }))}
            />
            <span>Half Board</span>
          </label>
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-primary-color focus:ring-primary-color"
              checked={filters.transfer}
              onChange={(e) => setFilters(prev => ({ ...prev, transfer: e.target.checked }))}
            />
            <span>Transfer</span>
          </label>
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-primary-color focus:ring-primary-color"
              checked={filters.cancellationAvailable}
              onChange={(e) => setFilters(prev => ({ ...prev, cancellationAvailable: e.target.checked }))}
            />
            <span>Cancellation Available</span>
          </label>
        </div>

        {isFilterActive && (
          <button
            onClick={handleResetFilters}
            className="text-sm text-primary-color hover:text-primary-color-dark font-medium underline"
          >
            Clear all filters
          </button>
        )}
      </div>

      <div className="relative">
        <input
          type="text"
          placeholder="Search by room name, amenities, or meal plans..."
          className="w-full max-w-md px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-color focus:border-primary-color"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <div className="absolute right-3 top-2.5">
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      <div className="text-sm text-gray-600 mt-4">
        Showing {totalRooms} room option{totalRooms !== 1 ? 's' : ''}
      </div>
    </div>
  );
};

export default RecommendationListHeader;