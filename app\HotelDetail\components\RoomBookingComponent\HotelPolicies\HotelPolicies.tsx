"use client";
import React from "react";
import "./HotelPolicies.scss";
import { HouseRulesAndPolicies } from "@/app/HotelDetail/hotel-detail-result.model";
import { HotelDetailApiResponse, Policy } from "@/app/HotelDetail/hotel-detail-api-response.model";
import DOMPurify from 'dompurify'

interface HotelPoliciesProps {
  hotelData?: HotelDetailApiResponse; // Primary backend data
  policies?: Policy[]; // Direct policies array
  // Legacy props (kept for backward compatibility)
  houseRulesAndPolicies?: HouseRulesAndPolicies;
}

function HotelPolicies({
  hotelData,
  policies,
  // Legacy props
  houseRulesAndPolicies
}: HotelPoliciesProps) {

  // Helper function to extract check-in/check-out times from backend policies
  const extractCheckInOutTimes = (policiesArray: Policy[]) => {
    const checkInPolicy = policiesArray.find(p => p.name === "check_in");
    const checkOutPolicy = policiesArray.find(p => p.name === "check_out");

    return {
      checkIn: checkInPolicy?.description || "Check-in time not specified",
      checkOut: checkOutPolicy?.description || "Check-out time not specified"
    };
  };

  // Helper function to get other policies (excluding check-in/out)
  const getOtherPolicies = (policiesArray: Policy[]) => {
    return policiesArray.filter(p =>
      p.name !== "check_in" && p.name !== "check_out"
    );
  };

  // Helper function to get icon for policy type
  const getIconForPolicy = (policyName: string) => {
    const iconMap: { [key: string]: string } = {
      "cancellation": "fa-calendar-xmark",
      "pets": "fa-paw",
      "smoking": "fa-smoking-ban",
      "parking": "fa-car",
      "wifi": "fa-wifi",
      "breakfast": "fa-utensils",
      "pool": "fa-swimming-pool",
      "gym": "fa-dumbbell",
      "spa": "fa-spa",
      "business": "fa-briefcase",
      "accessibility": "fa-wheelchair",
      "age": "fa-child",
      "payment": "fa-credit-card",
      "luggage": "fa-suitcase",
      "laundry": "fa-shirt"
    };

    // Find matching icon by checking if policy name contains any key
    for (const [key, icon] of Object.entries(iconMap)) {
      if (policyName.toLowerCase().includes(key)) {
        return icon;
      }
    }

    return "fa-info-circle"; // Default icon
  };

  // Determine data source (priority: backend data > direct props > legacy)
  const backendPolicies = hotelData?.policies || policies || [];
  const hasBackendData = backendPolicies.length > 0;

  let checkInTime: string;
  let checkOutTime: string;
  let otherPolicies: Policy[];

  if (hasBackendData) {
    const times = extractCheckInOutTimes(backendPolicies);
    checkInTime = times.checkIn;
    checkOutTime = times.checkOut;
    otherPolicies = getOtherPolicies(backendPolicies);
  } else {
    // Fallback to legacy data
    checkInTime = houseRulesAndPolicies?.checkinTime || "Check-in time not specified";
    checkOutTime = houseRulesAndPolicies?.checkoutTime || "Check-out time not specified";
    otherPolicies = [];
  }

  return (
    <div className="hotel-policies-section">
      <h6>Hotel Policies</h6>

      <div className="hotel-policies-container">
        <div className="flex gap-x-8">
          <div className="hotel-checkInOut-details">
            <div className="label">
              <i className="fa-solid fa-person-walking-arrow-right"></i>
              <span>Check-in</span>
            </div>
            <div className="details">
              From {checkInTime}
            </div>
          </div>

          <div className="hotel-checkInOut-details">
            <div className="label">
              <i className="fa-solid fa-person-walking-arrow-loop-left"></i>
              <span>Check-Out </span>
            </div>
            <div className="details">
              Till {checkOutTime}
            </div>
          </div>
        </div>

        {/* Backend Policies Section */}
        {hasBackendData && otherPolicies.length > 0 && (
          <div className="backend-policies">
            <div className="propertyInformation">
              <div className="propertyInformation__heading">
                <i className="fa-regular fa-file"></i>
                <span>Hotel Policies</span>
              </div>

              <div className="backend-policies-list">
                {otherPolicies.map((policy, index) => (
                  <div key={index} className="policy-item">
                    <div className="policy-header">
                      <i className={`fa-solid ${getIconForPolicy(policy.name)}`}></i>
                      <span className="policy-name">
                        {policy.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    </div>
                    <div className="policy-description">
                      {policy.description}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Legacy Rules Section - Fallback */}
        {!hasBackendData && houseRulesAndPolicies?.rules && (
          <div>
            {houseRulesAndPolicies.rules.map((item,index)=>(
              <div key={index}>
                {index === 0 && item.policyType == "CHILD_AND_EXTRA_BED_POLICY" && (
                  <div className="children-extra-beds">
                    <div className="children-extra-beds__heading">
                      <i className="fa-solid fa-children"></i>
                      <span>{item.heading}</span>
                    </div>

                    <div className="children-extra-beds__intro-list">
                      <ul className="children-extra-beds__intro">
                        <li>All children are welcome.</li>
                      </ul>

                      <ul className="children-extra-beds__list">
                        {item.subRules.map(
                          (subrules, index) => (
                            <li key={index} className="children-extra-beds__item">
                              <span>{subrules?.subHeading}</span>
                              <p>{subrules?.details}</p>
                            </li>
                          )
                        )}
                      </ul>
                    </div>
                  </div>
                )}

                {index === 1 && item.policyType == "OTHER_INFO" && (
                  <div className="propertyInformation">
                    <div className="propertyInformation__heading">
                      <i className="fa-regular fa-file"></i>
                      <span>{item?.heading}</span>
                    </div>

                    <ul className="propertyInformation__list">
                      {item?.subRules?.[0].details.map(
                        (details, index) => (
                          <li key={index} className="propertyInformation__item">
                            {details}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}

                {index === 2 && item.policyType == "ANNOUNCEMENT" && (
                  <div className="children-extra-beds">
                    <div className="children-extra-beds__heading">
                      <i className="fa-solid fa-building"></i>
                      <span>{item?.heading}</span>
                    </div>

                    <div className="children-extra-beds__intro-list">
                      <ul className="children-extra-beds__intro">
                        <li
                          dangerouslySetInnerHTML={{
                            __html: DOMPurify.sanitize(item?.subRules?.[0].details?.[0] || ""),
                          }}
                        ></li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default HotelPolicies;
