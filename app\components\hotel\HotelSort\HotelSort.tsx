"use client";
import React from 'react';
import { Check, ArrowUp, ArrowDown } from 'lucide-react';

interface HotelSortProps {
  selectedSort: string;
  onSortChange: (sortOption: string) => void;
  isMobile?: boolean;
}

function HotelSort({
  selectedSort,
  onSortChange,
  isMobile = false
}: HotelSortProps) {

  const sortButtons = [
    {
      key: 'featured',
      label: 'Featured',
      options: ['Top picks for long stays']
    },
    {
      key: 'rating',
      label: 'Rating',
      options: ['Property rating (low to high)', 'Property rating (high to low)']
    },
    {
      key: 'price',
      label: 'Price',
      options: ['Price (lowest first)', 'Price (highest first)']
    }
  ];

  const handleButtonClick = (button: typeof sortButtons[0]): void => {
    if (button.options.length === 1) {
      onSortChange(button.options[0]);
    } else {
      const currentIndex = button.options.findIndex(option => option === selectedSort);
      const nextIndex = currentIndex === -1 ? 0 : (currentIndex + 1) % button.options.length;
      onSortChange(button.options[nextIndex]);
    }
  };

  const getButtonState = (button: typeof sortButtons[0]) => {
    const currentIndex = button.options.findIndex(option => option === selectedSort);
    if (currentIndex === -1) return { isActive: false, Icon: null };

    if (button.options.length === 1) {
      return { isActive: true, Icon: null };
    }

    if (button.key === 'rating' || button.key === 'price') {
      return {
        isActive: true,
        Icon: currentIndex === 0 ? ArrowUp : ArrowDown
      };
    }

    return { isActive: true, Icon: null };
  };

  if (isMobile) {
    return (
      <div className="w-full space-y-3 p-4 bg-gray-50 rounded-xl">
        {sortButtons.map((button, index) => {
          const buttonState = getButtonState(button);
          return (
            <div
              key={index}
              className={`
                flex items-center justify-between
                w-full px-6 py-4
                rounded-xl
                cursor-pointer
                transition-all duration-300 ease-in-out
                transform hover:scale-[1.02]
                ${buttonState.isActive 
                  ? "bg-blue-100 text-blue-800 font-semibold shadow-lg ring-2 ring-blue-500" 
                  : "bg-white text-gray-800 shadow-md hover:bg-gray-100"
                }
              `}
              onClick={() => handleButtonClick(button)}
            >
              <span className="text-base flex items-center gap-4">
                {button.label}
                {buttonState.Icon && <buttonState.Icon className="w-5 h-5" />}
              </span>
              {button.options.length === 1 && buttonState.isActive && (
                <Check className="w-5 h-5 text-blue-600" />
              )}
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <div className="hidden md:block bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center h-12">
        <div className="px-5 h-full flex items-center bg-gray-100 border-r border-gray-200 text-sm font-bold text-gray-600 uppercase tracking-wider flex-shrink-0">
          Sort by
        </div>
        <div className="flex flex-1 justify-around items-center h-full">
          {sortButtons.map((button, index) => {
            const buttonState = getButtonState(button);
            return (
              <div
                key={index}
                className={`flex items-center justify-center px-4 h-full cursor-pointer transition-colors duration-200 ease-in-out ${
                  buttonState.isActive
                    ? "text-blue-600 font-semibold"
                    : "text-gray-500 hover:text-gray-800"
                }`}
                onClick={() => handleButtonClick(button)}
              >
                <span className="text-sm flex items-center gap-1.5">
                  {button.label}
                  {buttonState.Icon && <buttonState.Icon className="w-4 h-4" />}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

export default HotelSort;
