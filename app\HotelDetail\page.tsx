"use client";

import React, { useCallback, useEffect, useRef, useState, useMemo } from "react";
import "./HotelDetail.scss";
import RoomBookingComponent from "./components/RoomBookingComponent/RoomBookingComponent";
import ShimmerImageGallery from "./components/ShimmerImageGallery/ShimmerImageGallery";
import ShimmerInfoHeader from "./components/ShimmerInfoHeader/ShimmerInfoHeader";
import { useCommonContext } from "../contexts/commonContext";
import useScrollLock from "../components/utilities/ScrollLock/useScrollLock";
import HotelFacilities from "./components/RoomBookingComponent/HotelFacilities/HotelFacilities";
import { useRouter } from "next/navigation";
import { useTranslation } from "@/app/hooks/useTranslation";
import { showToast } from "../components/utilities/SonnerToasterCustom";
import { getHotelDetails } from "@/api/hotel/hotel-detail-service";
import { getRoomsAndRates } from "@/api/hotel/rooms-and-rates-service";
import { RoomsAndRatesApiResponse } from "@/models/hotel/rooms-and-rates.model";
import { transformRoomsAndRates } from "@/utils/roomsAndRatesTransformer";

import { HotelDetailApiResponse } from "./hotel-detail-api-response.model";
import { storeHotelDetails } from "@/utils/hotelDetailsStorage";

// Static fallback data structure matching the API response
const createStaticFallbackData = (): HotelDetailApiResponse => ({
  id: 0,
  hotel_id: "fallback-hotel",
  hotelId: "fallback-hotel",
  name: "Hotel Not Available",
  city: "Unknown City",
  country: "Unknown Country",
  userRating: null,
  userRatingCategoty: null,
  address: "Address not available",
  stateName: null,
  roomDetails: {
    type: null,
    bedroom: null,
    livingRoom: null,
    bathroom: null,
    size: null,
    bed: null
  },
  comfortRating: null,
  geoLocationInfo: {
    lat: 0,
    lon: 0
  },
  about: null,
  HotelType: "Hotel",
  starRating: null,
  category: null,
  amenities: [],
  attributes: [],
  roomCountLeft: null,
  isVisible: true,
  chain_code: "",
  chain_name: null,
  data_source_provider: "fallback",
  data_source_updated_at: null,
  provider_id: "fallback",
  provider_name: "Fallback Provider",
  provider_hotel_id: "fallback",
  hero_image: null,
  image_count: null,
  language: null,
  relevance_score: null,
  phones_json: [],
  faxes_json: null,
  attributes_json: [],
  available_suppliers: null,
  descriptions: [],
  facilities: [],
  reviews: [],
  policies: [],
  images: [],
  rooms: [],
  additional_information: [],
  recent_pricing: []
});

function Page() {
  const { t } = useTranslation();
  const { hotelSearchFormData } = useCommonContext();
  const [isShimmerLoading, setisShimmerLoading] = useState<boolean>(false);
  const [hotelId, setHotelId] = useState<string | null>(null);
  const [urlHotelId, setUrlHotelId] = useState<string | null>(null);
  const roomBookingRef = useRef<{ scrollToRoomCard: () => void } | null>(null);
  const [isMobileFacilitiesActive, setIsMobileFacilitiesActive] = useState<boolean>(false);
  const [isRoomsAndRatesLoading, setIsRoomsAndRatesLoading] = useState<boolean>(false);

  // Client-side only states to prevent hydration mismatch
  const [mounted, setMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [canShare, setCanShare] = useState(false);

  const parentOverviewRef = useRef<HTMLDivElement>(null!);

  const router = useRouter();
  const { hotelDetailsResponse, setHotelDetailsResponse, setIsLoading, selectedHotelId, setSelectedHotelId, searchKey } = useCommonContext();

  // Local state for rooms and rates
  const [roomsAndRatesResponse, setRoomsAndRatesResponse] = useState<RoomsAndRatesApiResponse>();




  // Initialize client-side only features after component mounts
  useEffect(() => {
    setMounted(true);

    // Check if mobile
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 950);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    // Check if sharing is supported
    setCanShare(typeof navigator !== 'undefined' && !!navigator.share);

    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  }, []);


  useEffect(() => {
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);
      const hotelId = params.get("hotelId");
      setUrlHotelId(hotelId);
    }
  }, []);

  // useEffect(() => {
  //   setIsLoading(false);
  //   setTimeout(() => {
  //     setisShimmerLoading(false);
  //   }, 2000);
  // }, [setIsLoading]);

  const handleReserveClick = () => {
    roomBookingRef.current?.scrollToRoomCard();
  };

  useScrollLock(isMobileFacilitiesActive);

  const scrollToOverview = () => {
    parentOverviewRef.current?.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  };

  const handlaSave = useCallback(() => {
    showToast("Successfully added to wishlist!", "default", "top-right");
  }, []);

  const shareHotel = useCallback((hotel: { name: string; url: string }) => {
    // Only execute on client side
    if (!mounted || typeof navigator === 'undefined') return;

    if (canShare) {
      navigator
        .share({
          title: `Check out this hotel: ${hotel.name}`,
          text: `Found this great hotel: ${hotel.name}`,
          url: hotel.url,
        })
        .then(() => { })
        .catch((error) => { });
    } else if (navigator.clipboard) {
      navigator.clipboard.writeText(hotel.url)
        .then(() => {
          showToast("Link copied to clipboard!", "default", "top-right");
        })
        .catch(() => {
          // Fallback for older browsers
          const textArea = document.createElement('textarea');
          textArea.value = hotel.url;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          showToast("Link copied to clipboard!", "default", "top-right");
        });
    }
  }, [mounted, canShare]);

  // Function to get hotel ID from context, URL params, or session storage
  const getHotelId = useCallback((): string => {
    // First check context
    if (selectedHotelId) {
      return selectedHotelId;
    }

    // Then check URL parameters
    if (urlHotelId) {
      localStorage.setItem('selectedHotelId', urlHotelId);
      setSelectedHotelId(urlHotelId);
      return urlHotelId;
    }

    // Finally check session storage
    if (typeof window !== 'undefined') {
      const selectedHotel = localStorage.getItem('selectedHotel');
      if (selectedHotel) {
        try {
          const hotelData = JSON.parse(selectedHotel);
          if (hotelData.hotelId) {
            return hotelData.hotelId.toString();
          }
        } catch (error) {
          console.log(error);
          
          // Silent error handling
        }
      }
    }
    return '';
    // Fallback hotel ID if none found
    // return '15219007';
  }, [urlHotelId]);

  const getRecommendationList = useCallback(async (hotelId:string) => {
      setIsRoomsAndRatesLoading(true);
      const keyToUse = searchKey || localStorage.getItem('searchKey');

      if (keyToUse && (hotelId)) {
        try {
          const roomsAndRatesResponse = await getRoomsAndRates(keyToUse, hotelId);
          console.log("Rooms and rates response:", roomsAndRatesResponse);
          // Update the rooms and rates state
          setIsRoomsAndRatesLoading(false);
          setRoomsAndRatesResponse(roomsAndRatesResponse);
        } catch (roomsError) {
          console.error("Rooms and rates API failed:", roomsError);
        }
      }
  },[searchKey])


  const fetchHotelDetails = useCallback(async (hotelId:string) => {
    setIsLoading(true);
    try {
      const response = await getHotelDetails(hotelId);
      setHotelDetailsResponse(response.data);
      setIsLoading(false);

      // Store hotel details in localStorage for persistence
      storeHotelDetails(response.data);

      // Update context with the hotel ID if it wasn't already set
      if (!selectedHotelId) {
        localStorage.setItem('selectedHotelId', response.data.hotelId || response.data.hotel_id);
        setSelectedHotelId(response.data.hotelId || response.data.hotel_id);
      }


    } catch (err) {
      console.error("API call failed:", err);
      // Use static fallback data if API fails
      const staticFallback = createStaticFallbackData();
      setHotelDetailsResponse(staticFallback);

      // Store fallback data in localStorage as well
      storeHotelDetails(staticFallback);
    } finally {
    }
  }, [setHotelDetailsResponse, setIsLoading, selectedHotelId, setSelectedHotelId]);

  useEffect(() => {
    const hotelsId = getHotelId();
    if(hotelsId){
      fetchHotelDetails(hotelsId);
      getRecommendationList(hotelsId);
    }
  }, [fetchHotelDetails,getRecommendationList,getHotelId]);

  // const fetchRoomsAndRates = useCallback(async () => {
  //   const hotelId = getHotelId();

  //   // Use searchKey from context or localStorage
  //   const keyToUse = searchKey || localStorage.getItem('searchKey');

  //   if (!keyToUse) {
  //     console.warn("No search key available for rooms and rates API");
  //     return;
  //   }

  //   try {
  //     const roomsAndRatesApiResponse = await getRoomsAndRates(keyToUse, hotelId);
  //     console.log("Rooms and rates response:", roomsAndRatesApiResponse);
  //     setRoomsAndRatesResponse(roomsAndRatesApiResponse);
  //   } catch (err) {
  //     console.error("Rooms and rates API call failed:", err);
  //   }
  // }, [getHotelId, searchKey, setRoomsAndRatesResponse]);

  // useEffect(() => {
  //   fetchRoomsAndRates();
  // }, [fetchRoomsAndRates]);

  // Transform rooms and rates data for UI consumption
  const transformedRoomsData = useMemo(() => {
    if (!roomsAndRatesResponse) return [];
    return transformRoomsAndRates(roomsAndRatesResponse);
  }, [roomsAndRatesResponse]);

  // Separate useEffect for API call that runs immediately
  // useEffect(() => {
  //   fetchHotelDetails();
  //   console.log("fetched hotel details:", hotelDetailsResponse);

  // }, [fetchHotelDetails]);





  // Keep the original mounted effect for UI state
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      setHotelId(params.get('hotelId'));
    }
  }, [])

  const handleReserveButtonClick = () => {
    if (!mounted) return; // Don't execute before client hydration

    if (isMobile) {
      router.push("/RoomSelection");
    } else {
      handleReserveClick();
    }
  };

  // Show loading state during hydration to prevent mismatch
  if (!mounted) {
    return (
      <div className="hotel-detail-container">
        <div className="overview-section common-container">
          <ShimmerInfoHeader />
          <ShimmerImageGallery />

        </div>
      </div>
    );
  }
  const handleBookingClick = () => {
    router.push("/RoomSelection");
  }

  return (
    <div className="hotel-detail-container">

      {/* <div className="booking-section-bottom-0">
                <div className="totalAmtLabelValue">
                  <p className="value">₹1,32,868.<span className="xs-text">
                  70
                  </span></p>

                  <span className="label">Total amount</span>
                </div>
                <button  onClick={handleBookingClick} className="bookingBtn">  <i className="fa-solid fa-share-from-square"></i> Reserve</button>
              </div> */}

      {/* <div className="search-bar-container common-container">

          {" "}
          <HotelSearchBar />{" "}

      </div> */}

      {isShimmerLoading ? (
        <div className="overview-section common-container">
          <ShimmerImageGallery />
          <ShimmerInfoHeader />
        </div>
      ) : (
        <>
          <div
            className="overview-section common-container"
            ref={parentOverviewRef}
          >
            {/* <ImageGallery newImageInfo={hotelDetailsResponse?.newImageInfo} images={hotelDetailsResponse?.carouselImages ?? []} /> */}

            {/* <div className="hotel-info-header ">
              <div className="info-header">
                <div className="heading">
                  <h2>{hotelDetailsResponse?.name || "..."}</h2>
                  <div className="rating">
                    {
                      Array.from({length: hotelDetailsResponse?.starRating ?? 0}).map((_, index) => (
                           <i key={index} className="fa-solid fa-star"></i>
                      ))
                    }
              
                
                 
                  </div>
                </div>

                <div className="buttons">
                  <div className="hotel-info-header-btn" onClick={handlaSave}>
                    {t("hotel.detail.save")}{" "}
                    <i className="fa-solid fa-heart"></i>
                  </div>

                  <div
                    className="hotel-info-header-btn"
                    onClick={() =>
                      shareHotel({
                        name: "Santana Beach Resort",
                        url: "http://localhost:3000/HotelDetail",
                      })
                    }
                  >
                    {t("hotel.detail.share")}
                    <i className="fa-solid fa-share-nodes"></i>
                  </div>

                  <div
                    className="hotel-info-header-reserveBtn"
                    onClick={handleReserveButtonClick}
                    style={{ marginLeft: "10px" }}
                  >
                    {t("hotel.detail.reserve")}
                    <i className="fa-solid fa-share-from-square"></i>
                  </div>
                </div>
              </div>

              <div className="review-location">
                <div className="review">
                  <div className="rating">{hotelDetailsResponse?.ratingView?.averageRating}</div>

                  <div className="rating-detail">
                    <p className="detail detail1">
                      {hotelDetailsResponse?.userRatingCategory}
                    </p>
                    <p className="detail detail2">
                      {hotelDetailsResponse?.ratingView?.ratingCount} {t("hotel.detail.ratings")}
                     </p>
                  </div>
                </div>
                <div className="location">
                  <div className="icon">
                    <i className="fa-solid fa-location-dot"></i>
                  </div>
                  <div className="details">
                    <div className="detail detail1">
                      {hotelDetailsResponse?.address}
                    </div>

                    <Link href={""}>
                      {" "}
                      <div className="detail detail2">
                        {t("hotel.detail.viewOnMap")}
                      </div>
                    </Link>
                  </div>
                </div>
                <DateRangeDisplay 
                  checkInDate={hotelSearchFormData?.checkInDate ?? ''} checkOutDate={hotelSearchFormData?.checkOutDate ?? ''} />
              </div>
            </div> */}
          </div>


          <div className="hotel-room-selection-section">
            <RoomBookingComponent
              isLoading={isRoomsAndRatesLoading}
              ref={roomBookingRef}
              setIsMobileFacilitiesActive={setIsMobileFacilitiesActive}
              // scrollToParentOverview={scrollToOverview}
              // parentOverviewRef={parentOverviewRef}
              images={hotelDetailsResponse?.images ?? []}
              transformedRoomsData={ transformedRoomsData }
            />
          </div>
        </>
      )}

      <div className={`detail-page-overlay ${isMobileFacilitiesActive ? 'show' : ''}`}>

      </div>

      <div
        className={`mobile-bottom-to-top-modal-container ${isMobileFacilitiesActive ? "active" : ""
          }`}
      >
        <div className="mobile-bottom-to-top-modal-header">
          <div
            className="mobile-bottom-to-top-modal-header__close-bttn"
            onClick={() => {
              setIsMobileFacilitiesActive(false);
            }}
          >
            <i className="fa-solid fa-xmark"></i>
          </div>
          <h3 className="mobile-bottom-to-top-modal-heading">Hotel Facilities:</h3>
        </div>

        <div className="mobile-bottom-to-top-modal-content">
          <div className="hotel-facilities-mobile-container common-container">
            <HotelFacilities
              hotelData={hotelDetailsResponse}
              backendFacilities={hotelDetailsResponse?.facilities}
              amenities={hotelDetailsResponse?.amenities}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Page;



