// 'use client';

// import React, { useState, useEffect, useCallback } from 'react';
// import { Map, Marker, ViewState } from 'react-map-gl/mapbox';
// import { MapPin } from 'lucide-react';
// import 'mapbox-gl/dist/mapbox-gl.css';
// import FullScreenMap from '@/components/map/FullScreenMap';
// import { useTranslation } from '@/app/hooks/useTranslation';
// import { useLanguage } from '@/app/contexts/languageContext';
// import './HotelSearchMap.scss';
// import DetailMap from '@/components/map/detail-map/DetailMap';
// import HotelDetailMapPopup from '@/app/components/map-box-map/components/HotelDetailMapPopup';
// import { HotelFilterData } from '@/models/hotel/filter.model';
// import { applyFilters } from '@/helpers/hotel/filter-helper';
// import { Hotel } from '@/models/hotel/list-page.model';

// interface HotelSearchMapProps {
//   hotels?: Hotel[]
//   destination?: {
//     name: string;
//     latitude: number;
//     longitude: number;
//   };
//   onHotelSelect?: (hotelId: string) => void;
//   actualHotels?: Hotel[];
//   filterData?: HotelFilterData;
//   onFilterChange?: (filterData: HotelFilterData | undefined) => void;
//   // New prop to determine if we should show the detail map
//   isDetail?: boolean;
//   // Data needed for DetailMap
//   detailData?: {
//     name: string;
//     rating: number;
//     stars: string;
//     position: [number, number];
//     description: string;
//     reviews: string;
//     locationScore: string;
//     address: string;
//     attractions: Array<{
//       name: string;
//       distance: string;
//     }>;
//   };
//   // Additional handlers for DetailMap
//   onDetailFavoriteClick?: () => void;
//   onDetailCloseClick?: () => void;
//   onDetailShowPrices?: () => void;
// }



// // Dummy destination
// const DUMMY_DESTINATION = {
//   name: "Goa, India",
//   latitude: 15.5,
//   longitude: 73.85
// };

// // Default detail data if needed
// const DEFAULT_DETAIL_DATA = {
//   name: "Blue Lagoon Resort",
//   rating: 8.7,
//   stars: "★★★★",
//   position: [15.7, 73.91] as [number, number],
//   description: "Excellent",
//   reviews: "1,234 reviews",
//   locationScore: "Great location - 9.2",
//   address: "Arambol Beach, Goa, India",
//   attractions: [
//     { name: "Arambol Beach", distance: "0.1 km" },
//     { name: "Sweet Water Lake", distance: "1.5 km" },
//     { name: "Mandrem Beach", distance: "3.2 km" }
//   ]
// };

// const HotelSearchMap: React.FC<HotelSearchMapProps> = ({
//   hotels = [],
//   destination,
//   onHotelSelect,
//   actualHotels: parentActualHotels,
//   filterData: parentFilterData,
//   onFilterChange,
//   isDetail = false,
//   detailData,
//   onDetailFavoriteClick,
//   onDetailCloseClick,
//   onDetailShowPrices
// }) => {
//   const { t } = useTranslation();
//   const { isRTL } = useLanguage();
//   const [mapHotels, setMapHotels] = useState(hotels);
//   const [mapDestination, setMapDestination] = useState(destination);
//   const [actualHotels, setActualHotels] = useState<Hotel[]>(parentActualHotels || []);
//   const [mapFilterData, setMapFilterData] = useState<HotelFilterData | undefined>(parentFilterData);
//   const [filteredHotels, setFilteredHotels] = useState<Hotel[]>([]);

//   // State to control maps visibility
//   const [isMapOpen, setIsMapOpen] = useState(false);
//   const [isDetailMapOpen, setIsDetailMapOpen] = useState(false);

//   // Interactive map state
//   const [viewport, setViewport] = useState<Partial<ViewState>>({
//     latitude: mapDestination?.latitude || 15.5,
//     longitude: mapDestination?.longitude || 73.85,
//     zoom: 11,
//   });
//   const [selectedPin, setSelectedPin] = useState<Hotel | null>(null);

//   // Initialize detail data if not provided
//   const [activeDetailData, setActiveDetailData] = useState(detailData || DEFAULT_DETAIL_DATA);

//   // Use dummy data if no hotels are provided
//   useEffect(() => {
//     if (hotels.length === 0) {
//       setMapHotels(DUMMY_HOTELS);
//       setMapDestination(DUMMY_DESTINATION);
//     } else {
//       setMapHotels(hotels);
//       setMapDestination(destination);
//     }
//   }, [hotels, destination]);

//   // Update viewport when destination changes
//   useEffect(() => {
//     if (mapDestination) {
//       setViewport((prev: Partial<ViewState>) => ({
//         ...prev,
//         latitude: mapDestination.latitude,
//         longitude: mapDestination.longitude,
//         zoom: 11,
//       }));
//     }
//   }, [mapDestination]);



//   // Update detail data when prop changes
//   useEffect(() => {
//     if (detailData) {
//       setActiveDetailData(detailData);
//     }
//   }, [detailData]);

// // Use actual hotels from parent if provided, otherwise use mapHotels directly
// useEffect(() => {
//   if (parentActualHotels && parentActualHotels.length > 0) {
//     setActualHotels(parentActualHotels);
//   } else if (mapHotels.length > 0) {
//     // Since mapHotels is now of type Hotel[], use it directly
//     setActualHotels(mapHotels);
//   }
// }, [parentActualHotels, mapHotels]);

//   // Use filter data from parent if provided
//   useEffect(() => {
//     if (parentFilterData && typeof parentFilterData === 'object') {
//       // Check if parentFilterData has the expected properties
//       if (parentFilterData.priceRange || parentFilterData.ratings || parentFilterData.amenities) {
//         setMapFilterData(parentFilterData);
//       }
//     }
//   }, [parentFilterData]);

//   // Apply filters when filter data or actual hotels change
//   useEffect(() => {
//     try {
//       if (mapFilterData && actualHotels.length > 0) {
//         const filtered = applyFilters(mapFilterData, actualHotels);
//         setFilteredHotels(filtered);
//       } else {
//         setFilteredHotels(actualHotels);
//       }
//     } catch (error) {
//       console.error('Error applying filters in HotelSearchMap:', error);
//       // Fallback to unfiltered hotels
//       setFilteredHotels(actualHotels);
//     }
//   }, [mapFilterData, actualHotels]);

//   // Handle filter changes from FullScreenMap
//   const handleFilterChange = useCallback((newFilterData: HotelFilterData | undefined) => {
//     setMapFilterData(newFilterData);
//     // Notify parent component of filter changes
//     if (onFilterChange) {
//       onFilterChange(newFilterData);
//     }
//   }, [onFilterChange]);

//   // Handle hotel details view from interactive map
//   const handleViewDetails = useCallback((hotelId: number) => {
//     if (onHotelSelect) {
//       onHotelSelect(hotelId.toString());
//     }
//   }, [onHotelSelect]);

//   // Define center location
//   const center = mapDestination ? {
//     latitude: mapDestination.latitude,
//     longitude: mapDestination.longitude,
//     address: mapDestination.name
//   } : undefined;

//   // Open normal map
//   const openMap = () => {
//     setIsMapOpen(true);
//   };

//   // Open detail map
//   const openDetailMap = () => {
//     setIsDetailMapOpen(true);
//   };

//   // Close maps
//   const closeMap = () => {
//     setIsMapOpen(false);
//   };

//   const closeDetailMap = () => {
//     setIsDetailMapOpen(false);
//     if (onDetailCloseClick) onDetailCloseClick();
//   };

//   // Define map center
//   const mapCenter: [number, number] = center
//     ? [center.latitude, center.longitude]
//     : [15.5, 73.85]; // Default center (Goa)

//   // Create markers for hotels
//   const hotelMarkers = mapHotels.map(hotel => ({
//     id: hotel.hotelId.toString(),
//     position: [hotel.geoLocationInfo.lat, hotel.geoLocationInfo.lon] as [number, number],
//     iconType: 'hotel' as const,
//     details: {
//       id: hotel.hotelId.toString(),
//       name: hotel.name,
//       rating: hotel.starRating,
//       price: `$${hotel.fareDetail.totalPrice}`
//     }
//   }));

//   return (
//     <div className="hotel-search-map idle">
//       {/* Interactive map background */}
//       <div className="map-background">
//         <div style={{ height: '100%', width: '100%', borderRadius: '8px', overflow: 'hidden' }}>
//           <Map
//             mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_GL_ACCESS_TOKEN}
//             {...viewport}
//             style={{ width: '100%', height: '100%' }}
//             mapStyle="mapbox://styles/mapbox/streets-v11"
//             onMove={(evt: { viewState: Partial<ViewState> }) => setViewport(evt.viewState)}
//           >
//             {(filteredHotels.length > 0 ? filteredHotels : actualHotels)?.map(hotel => {
//               const isSelectedOnDetailPage = isDetail && hotel.hotelId.toString() === detailData?.name;

//               return (
//                 <Marker
//                   key={hotel.hotelId}
//                   latitude={hotel.geoLocationInfo.lat}
//                   longitude={hotel.geoLocationInfo.lon}
//                   anchor="bottom"
//                 >
//                   <div
//                     className={isSelectedOnDetailPage ? "selected-marker" : "price-marker"}
//                     onClick={(e: React.MouseEvent) => {
//                       e.stopPropagation();
//                       if (!isDetail) {
//                         setSelectedPin(hotel);
//                       }
//                     }}
//                     style={{
//                       background: isSelectedOnDetailPage ? '#D9534F' : '#ffffff',
//                       border: isSelectedOnDetailPage ? '2px solid #ffffff' : '2px solid #007bff',
//                       borderRadius: isSelectedOnDetailPage ? '50%' : '20px',
//                       padding: isSelectedOnDetailPage ? '8px' : '4px 8px',
//                       fontSize: '12px',
//                       fontWeight: '600',
//                       color: isSelectedOnDetailPage ? '#ffffff' : '#007bff',
//                       cursor: 'pointer',
//                       boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
//                       whiteSpace: 'nowrap',
//                       minWidth: isSelectedOnDetailPage ? 'auto' : '40px',
//                       textAlign: 'center',
//                       display: 'flex',
//                       alignItems: 'center',
//                       justifyContent: 'center'
//                     }}
//                   >
//                     {isSelectedOnDetailPage ? (
//                       <MapPin size={16} fill="#ffffff" color="#ffffff" />
//                     ) : (
//                       `₹${hotel.fareDetail.totalPrice.toLocaleString('en-IN')}`
//                     )}
//                   </div>
//                 </Marker>
//               );
//             })}

//             {/* Hotel popup for non-detail view */}
//             {!isDetail && selectedPin && (
//               <HotelDetailMapPopup
//                 pin={selectedPin}
//                 onClose={() => setSelectedPin(null)}
//                 onViewDetails={handleViewDetails}
//               />
//             )}
//           </Map>
//         </div>

//         <div className="show-map-button-container">
//           <button
//             className="show-map-button"
//             onClick={isDetail ? openDetailMap : openMap}
//             aria-label={t('hotel.card.showOnMap')}
//           >
//             <i className={`fa-solid fa-location-dot ${isRTL ? 'ml-2' : 'mr-2'}`}></i>
//             {t('hotel.card.showOnMap')}
//           </button>
//         </div>
//       </div>

//       {/* Full-screen map component */}
//       {!isDetail && (
//         <FullScreenMap
//           isOpen={isMapOpen}
//           onClose={closeMap}
//           center={mapCenter}
//           zoom={12}
//           markers={hotelMarkers}
//           title={mapDestination?.name || t('map.selectedArea')}
//           hotels={filteredHotels.length > 0 ? filteredHotels : actualHotels}
//           destination={center ? {
//             name: center.address || '',
//             latitude: center.latitude,
//             longitude: center.longitude
//           } : undefined}
//           isHotelSearchMap={true}
//           filterData={mapFilterData}
//           onFilterChange={handleFilterChange}
//           onHotelSelect={(hotelId) => {
//             if (onHotelSelect) {
//               onHotelSelect(hotelId.toString());
//             }
//           }}
//         />
//       )}

//       {/* Fixed position DetailMap */}
//       {isDetail && (
//         <div className={`detail-map-fixed-container ${isDetailMapOpen ? 'active' : ''}`}>
//           <div className="detail-map-content">
//             {/* Custom close button */}
//             <button 
//               className="detail-map-close" 
//               onClick={closeDetailMap}
//               aria-label={t('common.close')}
//             >
//               <i className="fa-solid fa-times"></i>
//             </button>
            
//             {/* DetailMap component */}
//             <DetailMap
//               location={activeDetailData}
//               style={{ height: '100%', width: '100%' }}
//               onFavoriteClick={onDetailFavoriteClick}
//               onCloseClick={closeDetailMap}
//               onShowPrices={onDetailShowPrices}
//             />
            
//             {/* Optional footer with hotel information */}
//             <div className="detail-map-footer">
//               <div className="hotel-details">
//                 <h4>{activeDetailData.name}</h4>
//                 <p className="location">{activeDetailData.address}</p>
//               </div>
//               <div className="hotel-actions">
//                 <button onClick={onDetailFavoriteClick}>
//                   <i className="fa-regular fa-heart"></i> {t('hotel.favorite')}
//                 </button>
//                 <button className="primary" onClick={onDetailShowPrices}>
//                   {t('hotel.showPrices')}
//                 </button>
//               </div>
//             </div>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default HotelSearchMap;