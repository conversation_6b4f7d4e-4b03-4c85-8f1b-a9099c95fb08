'use client';

import { Popup } from 'react-map-gl/mapbox';
import { Star } from 'lucide-react';
import '../InteractiveMap.css'; // Import from parent directory
import { Hotel } from '@/models/hotel/list-page.model';

type HotelPopupProps = {
  pin: Hotel;
  onClose: () => void;
  onViewDetails: (hotelId: number) => void;
};

export default function HotelDetailMapPopup({ pin, onClose, onViewDetails }: HotelPopupProps) {
  return (
    <Popup
      latitude={pin.geoLocationInfo.lat}
      longitude={pin.geoLocationInfo.lon}
      onClose={onClose}
      closeOnClick={false}
      anchor="left"
      className="hotel-popup"
    >
      <div>
        <img 
          src={pin.imageInfoList?.[0]?.url || 'https://placehold.co/300x200/cccccc/666666?text=No+Image'} 
          alt={pin.name} 
          className="popup-image" 
        />
        <h3>{pin.name}</h3>
        <div className="popup-rating">
          <div className="stars">
            {Array.from({ length: pin.starRating }, (_, i) => (
              <Star key={i} color="#FFC107" fill="#FFC107" size={14} />
            ))}
          </div>
          <span className="rating-text">{pin.userRating} ({pin.userRatingCategory})</span>
        </div>
        <p className="popup-price">
          <span className="current-price">₹{pin.fareDetail.totalPrice.toLocaleString('en-IN')}</span>
          <span className="per-night"> per night</span>
        </p>
        {pin.fareDetail.refundable && (
          <p className="popup-info">✓ Free cancellation</p>
        )}
        <button className="popup-button" onClick={() => onViewDetails(pin.hotelId)}>
          View Details
        </button>
      </div>
    </Popup>
  );
}