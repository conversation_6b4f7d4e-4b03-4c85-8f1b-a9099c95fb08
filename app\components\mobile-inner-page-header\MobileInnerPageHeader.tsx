'use client';

import React from 'react';

interface MobileInnerPageHeaderProps {
  onBackClick: () => void;
  children?: React.ReactNode;
}

function MobileInnerPageHeader({
  onBackClick,
  children,
}: MobileInnerPageHeaderProps) {
  return (
    <div className="w-full bg-white border-b border-black/10 z-50 sticky top-0">
      <div className="flex items-center px-5 py-3 gap-4">
        <button
          className="bg-transparent border-none text-lg text-blue-600 cursor-pointer p-2 rounded-full flex items-center justify-center transition-colors duration-200 ease-in-out flex-shrink-0 hover:bg-blue-100 active:bg-blue-200"
          onClick={onBackClick}
        >
          <i className="fa-solid fa-arrow-left"></i>
        </button>
        <div className="flex-1 flex flex-col gap-1 min-w-0">{children}</div>
      </div>
    </div>
  );
}

export default MobileInnerPageHeader;

