/**
 * Utility functions for consistent currency formatting across the application
 */

export interface CurrencyFormatOptions {
  currency?: string;
  locale?: string;
  showSymbol?: boolean;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
}

/**
 * Format currency with proper symbol and decimal places
 * Handles floating point precision issues by rounding to 2 decimal places
 */
export const formatCurrency = (
  amount: number, 
  currency: string = 'INR',
  options: CurrencyFormatOptions = {}
): string => {
  // Handle invalid amounts
  if (isNaN(amount) || amount === null || amount === undefined) {
    return '0.00';
  }

  // Round to 2 decimal places to avoid floating point precision issues
  const roundedAmount = Math.round(amount * 100) / 100;

  // Handle "not available" currency
  if (currency.includes('not available')) {
    return roundedAmount.toFixed(2);
  }

  const {
    locale = 'en-IN',
    showSymbol = true,
    minimumFractionDigits = 2,
    maximumFractionDigits = 2
  } = options;

  // Get currency symbol
  const getCurrencySymbol = (currencyCode: string): string => {
    const currencyUpper = currencyCode.toUpperCase();
    switch (currencyUpper) {
      case 'INR': return '₹';
      case 'USD': return '$';
      case 'EUR': return '€';
      case 'GBP': return '£';
      case 'AED': return 'AED ';
      case 'SAR': return 'SAR ';
      case 'JPY': return '¥';
      case 'CNY': return '¥';
      case 'KWD': return 'د.ك ';
      case 'OMR': return '﷼ ';
      case 'BHD': return '.د.ب ';
      case 'PKR': return '₨';
      case 'AFN': return '؋ ';
      case 'AUD': return 'A$';
      case 'BRL': return 'R$';
      default: return `${currencyCode} `;
    }
  };

  // Format the number with locale-specific formatting
  const formattedAmount = roundedAmount.toLocaleString(locale, {
    minimumFractionDigits,
    maximumFractionDigits
  });

  if (!showSymbol) {
    return formattedAmount;
  }

  const symbol = getCurrencySymbol(currency);
  
  // For currencies that have symbols before the amount
  if (['INR', 'USD', 'EUR', 'GBP', 'JPY', 'CNY', 'PKR', 'AUD', 'BRL'].includes(currency.toUpperCase())) {
    return `${symbol}${formattedAmount}`;
  }
  
  // For currencies that have codes/symbols after the amount
  return `${symbol}${formattedAmount}`;
};

/**
 * Format currency for display with separate symbol and amount parts
 * Useful for complex UI layouts where symbol and amount need different styling
 */
export const formatCurrencyParts = (
  amount: number, 
  currency: string = 'INR'
): { symbol: string; mainAmount: string; decimal: string } => {
  // Handle invalid amounts
  if (isNaN(amount) || amount === null || amount === undefined) {
    return { symbol: '₹', mainAmount: '0', decimal: '00' };
  }

  // Round to 2 decimal places to avoid floating point precision issues
  const roundedAmount = Math.round(amount * 100) / 100;
  const formattedAmount = roundedAmount.toFixed(2);
  const [mainAmount, decimal] = formattedAmount.split('.');

  const getCurrencySymbol = (currencyCode: string): string => {
    const currencyUpper = currencyCode.toUpperCase();
    switch (currencyUpper) {
      case 'INR': return '₹';
      case 'USD': return '$';
      case 'EUR': return '€';
      case 'GBP': return '£';
      case 'AED': return 'AED';
      case 'SAR': return 'SAR';
      case 'JPY': return '¥';
      case 'CNY': return '¥';
      default: return currencyCode;
    }
  };

  return {
    symbol: getCurrencySymbol(currency),
    mainAmount: Number(mainAmount).toLocaleString('en-IN'),
    decimal: decimal
  };
};

/**
 * Simple currency formatter for basic use cases
 * Always shows 2 decimal places and handles floating point precision
 */
export const formatSimpleCurrency = (amount: number, currencySymbol: string = '₹'): string => {
  if (isNaN(amount) || amount === null || amount === undefined) {
    return `${currencySymbol}0.00`;
  }

  // Round to 2 decimal places to avoid floating point precision issues
  const roundedAmount = Math.round(amount * 100) / 100;
  return `${currencySymbol}${roundedAmount.toFixed(2)}`;
};
