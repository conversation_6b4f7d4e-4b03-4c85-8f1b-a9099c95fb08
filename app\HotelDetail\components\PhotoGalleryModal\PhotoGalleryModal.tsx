import React, { useState } from "react";
import Image from "next/image";
import "./PhotoGalleryModal.scss";
import ImageShowCase from "../ImageShowcase/ImageShowcase";
import { CarouselImage } from "../../hotel-detail-result.model";
import { Images, HotelDetailApiResponse } from "../../hotel-detail-api-response.model";
import { useRouter } from "next/navigation";

interface PhotoGalleryModalProps {
  isOpen: boolean;
  onClose: () => void;
  hotelName: string;
  images?: Images[]; // Add backend images
  hotelData?: HotelDetailApiResponse; // Add hotel data
}

// Define a type for the tabs
type TabName = "Overview" | "Deluxe Room" | "Superior Double" | "King Room";

// Define a type for the images object
type ImagesType = {
  [key in TabName]: string[];
};

const PhotoGalleryModal: React.FC<PhotoGalleryModalProps> = ({
  isOpen,
  onClose,
  hotelName = "Le Celestium",
  images = [], // Add backend images with default
  hotelData, // Add hotel data
}) => {
  const router = useRouter();

  // Helper function to categorize images from backend
  const categorizeImages = (backendImages: Images[]) => {
    const categories: { [key: string]: string[] } = {};

    backendImages.forEach(image => {
      const category = image.image_category_type || 'Overview';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(image.image_path);
    });

    // Ensure we always have an Overview category
    if (!categories['Overview'] && backendImages.length > 0) {
      categories['Overview'] = backendImages.slice(0, 8).map(img => img.image_path);
    }

    return categories;
  };

  // Create dynamic image categories from backend data
  const dynamicImages = categorizeImages(images);
  const availableTabs = Object.keys(dynamicImages);

  // Fallback to hardcoded data if no dynamic images available
  const fallbackImages = {
    Overview: [
      "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
      "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
    ]
  };

  // Use dynamic images if available, otherwise use fallback
  const finalImages = availableTabs.length > 0 ? dynamicImages : fallbackImages;
  const finalTabs = availableTabs.length > 0 ? availableTabs : Object.keys(fallbackImages);

  // Create dynamic images for ImageShowCase
  const showcaseImages: CarouselImage[] = images.map(img => ({
    pictureId: img.id.toString(),
    url: img.image_path,
    urlHigh: img.image_path,
    urlMedium: img.image_path,
    urlLow: img.image_path,
    caption: img.alt_text,
    imageCategory: img.image_category_type,
    rank: null,
    srpRank: img.sort_order,
    detailRank: img.sort_order,
    categoryRank: img.sort_order,
    withinCategoryRank: img.sort_order
  }));

  const [activeTab, setActiveTab] = useState<string>(
    finalTabs.length > 0 ? finalTabs[0] : "Overview"
  );
  const [isImageLightBoxActive, setIsImageLightBoxActive] = useState<boolean>(false);

  // Sample data for the tabs and images (COMMENTED OUT - USING DYNAMIC DATA)
  // const tabs: TabName[] = [
  //   "Overview",
  //   "Deluxe Room",
  //   "Superior Double",
  //   "King Room",
  // ];

  const images2: CarouselImage[] = [
          {
            "pictureId": "carousel_101",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Hotel Overview",
            "imageCategory": "Room",
            "rank": null,
            "srpRank": 1,
            "detailRank": 1,
            "categoryRank": 1,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Exterior",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Public Areas",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Bathroom",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/cfc1cc12d06b9bb675e7d26ccee0b7ec.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Dining Areas",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Food & Beverages",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Facilities",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Room",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
            {
            "pictureId": "carousel_101",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Hotel Overview",
            "imageCategory": "Room",
            "rank": null,
            "srpRank": 1,
            "detailRank": 1,
            "categoryRank": 1,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Exterior",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Public Areas",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Bathroom",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/cfc1cc12d06b9bb675e7d26ccee0b7ec.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Dining Areas",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Food & Beverages",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Facilities",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Room",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          }
        ]

  // HARDCODED IMAGES DATA (COMMENTED OUT - USING DYNAMIC DATA)
  // const images: ImagesType = {
  //   Overview: [
  //     "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
  //     "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
  //     "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
  //     "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
  //     "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
  //     "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
  //     "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
  //     "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
  //   ],
  //   "Deluxe Room": [
  //     "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
  //     "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
  //     "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
  //   ],
  //   "Superior Double": [
  //     "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
  //     "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
  //   ],
  //   "King Room": [
  //     "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
  //     "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
  //   ],
  // };

  // Create dynamic ratings from backend data
  const dynamicRatings = hotelData ? {
    overall: parseFloat(hotelData.userRating || "0"),
    reviews: hotelData.reviews?.length || 0,
    categories: {
      "Overall Rating": parseFloat(hotelData.userRating || "0"),
      "Star Rating": (hotelData.starRating || 0) * 2, // Scale to 10
      "Reviews": hotelData.reviews?.length > 0 ?
        hotelData.reviews.reduce((sum, review) => sum + review.rating, 0) / hotelData.reviews.length : 0,
      "Facilities": Math.min(10, (hotelData.facilities?.length || 0) * 1.5), // Scale to 10
      "Amenities": Math.min(10, (hotelData.amenities?.length || 0) * 0.8), // Scale to 10
      "Location": parseFloat(hotelData.userRating || "0"), // Use overall rating as proxy
    }
  } : {
    // Fallback ratings (HARDCODED DATA COMMENTED OUT)
    overall: 7.2,
    reviews: 392,
    categories: {
      Facilities: 7.2,
      Staff: 5.9,
      Cleanliness: 7.7,
      Comfort: 7.8,
      "Value for money": 7.7,
      Location: 7.7,
      "Free Wifi": 10,
    },
  };

  // HARDCODED RATING DATA (COMMENTED OUT - USING DYNAMIC DATA)
  // const ratings = {
  //   overall: 7.2,
  //   reviews: 392,
  //   categories: {
  //     Facilities: 7.2,
  //     Staff: 5.9,
  //     Cleanliness: 7.7,
  //     Comfort: 7.8,
  //     "Value for money": 7.7,
  //     Location: 7.7,
  //     "Free Wifi": 10,
  //   },
  // };

  if (!isOpen) return null;

  const handleReserveBtn = () => {
    router.push('/BookingPreviewPage');

  }

  return (
    <div className="photo-gallery-modal">
      <div className="photo-gallery-modal__container">
        <div className="photo-gallery-modal__header">
         {
          isImageLightBoxActive ? ( <button onClick={() =>  setIsImageLightBoxActive(false)} className="photo-gallery-modal__go-to-prevpage-btn"><i className="fa-solid fa-arrow-left "></i> Gallery</button>) : (<div></div>)
         }
       <div className="flex items-center">
           <div className="photo-gallery-modal__title">{hotelName}</div>
          <button className="photo-gallery-modal__reserve-btn" onClick={handleReserveBtn}>
            Reserve now
          </button>
       </div>
          <button className="photo-gallery-modal__close-btn" onClick={onClose}>
            Close <i className="fa-solid fa-xmark"></i>
          </button>
        </div>

        <div className="photo-gallery-modal__content">
          {
            isImageLightBoxActive ? 
            
            (
              <div className="image-showcase-main-container">
               <ImageShowCase images={showcaseImages.length > 0 ? showcaseImages : images2} />
              </div>
          
          
          
        ) : (  
        
        <div className="photo-gallery-modal__gallery">
            <div className="photo-gallery-modal__tabs">
              {finalTabs.map((tab) => {
                // Get the correct thumbnail image for each tab (first image in that category)
                const thumbnailImage =
                  (finalImages as any)[tab] && (finalImages as any)[tab].length > 0
                    ? (finalImages as any)[tab][0]
                    : "/api/placeholder/100/60";

                return (
                  <div
                    key={tab}
                    className={`photo-gallery-modal__tab ${
                      activeTab === tab ? "active" : ""
                    }`}
                    onClick={() => setActiveTab(tab)}
                  >
                    <div
                      className={`photo-gallery-modal__tab-image ${
                        activeTab === tab ? "active" : ""
                      }`}
                    >
                      <Image
                        src={thumbnailImage}
                        alt={tab}
                        width={100}
                        height={60}
                        objectFit="cover"
                      />
                    </div>
                    <div
                      className={`photo-gallery-modal__tab-name ${
                        activeTab === tab ? "active" : ""
                      }`}
                    >
                      {tab}
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="photo-gallery-modal__gallery-row">
              {(finalImages as any)[activeTab] &&
                (finalImages as any)[activeTab].map((imageUrl: string, index: number) => (
                  <div
                    key={index}
                    className="photo-gallery-modal__gallery-item"
                  >
                    <div className="image-container" onClick={() => setIsImageLightBoxActive(true)}>
                      <Image
                        src={imageUrl}
                        alt={`${activeTab} image ${index + 1}`}
                        layout="fill"
                        objectFit="cover"
                      />
                    </div>
                  </div>
                ))}
            </div>
          </div>
          
        )
          }
        

          <div className="photo-gallery-modal__sidebar">
            <div className="photo-gallery-modal__rating">
              <div className="rating-score">{dynamicRatings.overall}</div>
              <div className="rating-text">
                <div className="rating-label">{hotelData?.userRatingCategoty || "Good"}</div>
                <div className="rating-reviews">{dynamicRatings.reviews} reviews</div>
              </div>
            </div>

            <div className="photo-gallery-modal__categories">
              <h3>Categories:</h3>
              {Object.entries(dynamicRatings.categories).map(([category, score]) => (
                <div key={category} className="category-item">
                  <div className="category-name-score-wrapper">
                    <div className="category-name">
                      {category}
                      {score === 10 && (
                        <i className="fa-solid fa-arrow-up category-icon"></i>
                      )}
                      
                    </div>
                    <div className="category-score">{score}</div>
                  </div>

                  <div className="category-score-container">
                    <div
                      className={`category-score-bar ${
                        (Number(score) / 10) * 100 === 100 ? "highscore" : Number(score) <= 6 ? "lowscore" : ""
                      }`}
                      style={{ width: `${(Number(score) / 10) * 100}%` }}
                    />
                  </div>
                </div>
              ))}
             <div className="score-note-container">
     
                <div className="score-note">
                <i className="fa-solid fa-arrow-up highscore"></i> High score for
                Goa
              </div>

                <div className="score-note">
                <i className="fa-solid fa-arrow-down lowscore"></i> Low score for
                Goa
              </div>
    
             </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhotoGalleryModal;
