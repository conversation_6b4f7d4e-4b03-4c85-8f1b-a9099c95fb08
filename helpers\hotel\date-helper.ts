// utils/dateHelpers.ts
export function getNightsCount(checkIn: Date | string, checkOut: Date | string): number {
  if (!checkIn || !checkOut) return 0;

  const start = new Date(checkIn);
  const end = new Date(checkOut);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0;

  const diffTime = end.getTime() - start.getTime();
  const diffDays = diffTime / (1000 * 60 * 60 * 24);

  return diffDays > 0 ? Math.floor(diffDays) : 0;
}
