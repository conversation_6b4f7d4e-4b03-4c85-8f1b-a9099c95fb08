"use client";
import React from "react";
import { useTranslation } from "@/app/hooks/useTranslation";
import "./date-range-display.scss";

interface Prop {
  checkInDate: string;
  checkOutDate: string;
  compact?: boolean; // New prop for compact single-line display
}

const DateRangeDisplay = ({ checkInDate, checkOutDate, compact = false }: Prop) => {
  const { t } = useTranslation();

  const calculateNights = () => {
    if (!checkInDate || !checkOutDate) return 0;
    const start = new Date(checkInDate);
    const end = new Date(checkOutDate);
    const diff = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diff / (1000 * 3600 * 24));
  };

  const formatDate = (date: string) => {
    if (!date) return "";
    return new Date(date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  const nights = calculateNights();

  // Compact single-line format for mobile header
  if (compact) {
    return (
      <div className="date-range compact">
        <span className="compact-date-text">
          {formatDate(checkInDate)} - {formatDate(checkOutDate)} ({nights} {nights === 1 ? t("hotel.night") : t("hotel.nights")})
        </span>
      </div>
    );
  }

  // Default format with icon and two lines
  return (
    <div className="date-range">
      <div className="icon">
        <i className="fa-solid fa-calendar-days"></i>
      </div>
      <div className="details">
        <div className="detail detail1">
          {formatDate(checkInDate)} - {formatDate(checkOutDate)}
        </div>
        <div className="detail detail2">
          {nights} {nights === 1 ? t("hotel.night") : t("hotel.nights")}
        </div>
      </div>
    </div>
  );
};

export default DateRangeDisplay;
