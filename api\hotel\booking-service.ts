import { ApiBookingResponse } from "@/models/hotel/booking.model";
import apiService from "../api-service";

export const getBookingWithReference = async (bookingReference: string) : Promise<ApiBookingResponse> => {
  const response = await apiService.get3<ApiBookingResponse>(
    `api/v1/booking/${bookingReference}`
  );
  return response;
};

export const getBookingList = async (userId: string) : Promise<ApiBookingResponse[]> => {
  const response = await apiService.get3<ApiBookingResponse[]>(`api/v1/booking/user/${userId}`);
  return response;
};
