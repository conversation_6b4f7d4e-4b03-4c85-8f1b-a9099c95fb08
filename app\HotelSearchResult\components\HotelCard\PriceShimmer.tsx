"use client";
import React from "react";
import "./PriceShimmer.scss";

interface PriceShimmerProps {
  layout?: "list" | "grid";
}

const PriceShimmer: React.FC<PriceShimmerProps> = ({ layout = "list" }) => {
  return (
    <div className={`price-shimmer ${layout === "grid" ? "price-shimmer--grid" : "price-shimmer--list"}`}>
      {/* Simple single price shimmer */}
      <div className="price-shimmer__single">
        <div className="shimmer-line"></div>
      </div>
    </div>
  );
};

export default PriceShimmer;
