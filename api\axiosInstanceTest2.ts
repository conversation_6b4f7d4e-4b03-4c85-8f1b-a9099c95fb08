import axios, {
    AxiosResponse,
    InternalAxiosRequestConfig,
  } from 'axios';
  
  // Create an Axios instance with a base URL for booking services
  const axiosInstance3 = axios.create({
    // baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://192.168.0.197:8000/',
    // baseURL: process.env.NEXT_PUBLIC_BOOKING_DETAILS_API_BASE_URL || 'http://172.20.10.14:8001/', //booking details
       baseURL: process.env.NEXT_PUBLIC_BOOKING_DETAILS_API_BASE_URL || 'http://103.214.234.68:30001/',   //booking success
  });
  
  // Request interceptor
  axiosInstance3.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      const token = localStorage.getItem('token') || 'Guest';
  
      if (config.url?.includes('/json/')) {
        // No modifications for URLs containing '/json/'
      } else if (config.url?.includes('formData/')) {
        config.url = config.url.replace('formData/', '');
        config.headers['Authorization'] = `Bearer ${token}`;
      } else if (config.url?.includes('autosuggest')) {
        // No modifications for autosuggest endpoint - it might not require auth headers
      } else {
        config.headers['Content-Type'] = 'application/json';
        config.headers['Authorization'] = `Bearer ${token}`;
      }
  
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
  
  // Response interceptor
  axiosInstance3.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error) => {
      if (error.response && error.response.status === 403 ||  error.response.status === 401) {
        window.location.href = '/';
      }
      return Promise.reject(error);
    }
  );
  
  export default axiosInstance3;
  