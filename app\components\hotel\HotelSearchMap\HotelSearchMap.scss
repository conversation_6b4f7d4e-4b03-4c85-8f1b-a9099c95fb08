// @use "/styles/variable" as *;

// .hotel-search-map {
//   width: 100%;
//   height: 100%;
//   max-width: 100%;
//   border-radius: 8px;
//   overflow: hidden;
//   box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
//   margin-bottom: 20px;
//   padding: 0;
//   position: relative;

//   // Styles for expanded state (original map)
//   &.expanded {
//     .map-title {
//       position: absolute;
//       top: 10px;
//       left: 10px;
//       z-index: 1000;
//       background-color: rgba(255, 255, 255, 0.9);
//       padding: 8px 15px;
//       border-radius: 4px;
//       font-size: 16px;
//       font-weight: 600;
//       box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

//       // RTL support
//       html[dir="rtl"] & {
//         left: auto;
//         right: 10px;
//       }
//     }

//     .map-info {
//       background-color: $white_color;
//       padding: 15px;
//       border-top: 1px solid $border_color;

//       p {
//         margin: 0 0 5px;
//         font-size: 14px;
//         color: $gray_color;

//         &:last-child {
//           margin-bottom: 0;
//           font-weight: 600;
//           color: $primary_color;
//         }
//       }
//     }
//   }

//   // Styles for idle state (clean map with button)
//   &.idle {
//     height: 100%;

//     .map-background {
//       position: relative;
//       width: 100%;
//       height: 100%;
//       overflow: hidden;
//       border-radius: 8px;

//       // Hide zoom controls and attribution in idle state
//       :global(.leaflet-control-container) {
//         display: none;
//       }

//       .show-map-button-container {
//         position: absolute;
//         top: 0;
//         left: 0;
//         width: 100%;
//         height: 100%;
//         display: flex;
//         justify-content: center;
//         align-items: center;
//         z-index: 1000;
//         pointer-events: none; // Allow clicks to pass through to the map

//         .show-map-button {
//           background-color: $primary_color;
//           color: white;
//           border: none;
//           border-radius: 4px;
//           padding: 10px 20px;
//           font-size: 14px;
//           font-weight: 600;
//           display: flex;
//           align-items: center;
//           justify-content: center;
//           box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
//           cursor: pointer;
//           transition: background-color 0.2s ease;
//           pointer-events: auto; // Make button clickable
//           z-index: 1500; // Ensure button is above map

//           i {
//             font-size: 16px;
//           }

//           &:hover {
//             background-color: darken($primary_color, 5%);
//           }

//           // RTL support
//           html[dir="rtl"] & {
//             flex-direction: row-reverse;
//           }
//         }
//       }
//     }
//   }
// }
@use "/styles/variable" as *;
@use "/styles/zIndex" as *;
@use "sass:color";

.hotel-search-map {
  width: 100%;
  height: 100%;
  max-width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 0;
  position: relative;

  // Styles for expanded state (original map)
  &.expanded {
    .map-title {
      position: absolute;
      top: 10px;
      left: 10px;
      z-index: z-index(modal);
      background-color: rgba(255, 255, 255, 0.9);
      padding: 8px 15px;
      border-radius: 4px;
      font-size: 16px;
      font-weight: 600;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

      // RTL support
      html[dir="rtl"] & {
        left: auto;
        right: 10px;
      }
    }

    .map-info {
      background-color: $white_color;
      padding: 15px;
      border-top: 1px solid $border_color;

      p {
        margin: 0 0 5px;
        font-size: 14px;
        color: $gray_color;

        &:last-child {
          margin-bottom: 0;
          font-weight: 600;
          color: $primary_color;
        }
      }
    }
  }

  // Styles for idle state (clean map with button)
  &.idle {
    height: 100%;

    .map-background {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
      border-radius: 8px;
      z-index: z-index(base);

      // Hide zoom controls and attribution in idle state
      :global(.leaflet-control-container) {
        display: none;
      }

      // Hide mapbox controls in idle state
      :global(.mapboxgl-control-container) {
        display: none;
      }

      // Interactive map marker styles
      .price-marker {
        background: #ffffff;
        border: 2px solid #007bff;
        border-radius: 20px;
        padding: 4px 8px;
        font-size: 12px;
        font-weight: 600;
        color: #007bff;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transition: all 0.2s ease;
        white-space: nowrap;
        min-width: 40px;
        text-align: center;

        &:hover {
          background: #007bff;
          color: #ffffff;
          transform: scale(1.05);
          box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
      }

      .selected-marker {
        background: #D9534F;
        border: 2px solid #ffffff;
        border-radius: 50%;
        padding: 8px;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(217, 83, 79, 0.4);
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 16px rgba(217, 83, 79, 0.5);
        }
      }

      .show-map-button-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: z-index(modal);
        pointer-events: none; // Allow clicks to pass through to the map

        .show-map-button {
          width: 100%;
          height: 100%;
          background-color: #003b9554;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 10px 20px;
          font-size: 14px;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          cursor: pointer;
          transition: background-color 0.2s ease;
          pointer-events: auto; // Make button clickable
          z-index: z-index(popover); // Ensure button is above map

          i {
            font-size: 16px;
          }

          &:hover {
            background-color: #003b95ab;
          }

          // RTL support
          html[dir="rtl"] & {
            flex-direction: row-reverse;
          }
        }
      }
    }
  }
}

// Fixed position DetailMap styles
.detail-map-fixed-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: z-index(base); // Higher than other elements but below modals
  background-color: rgba(0, 0, 0, 0.5); // Semi-transparent overlay
  display: none;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  &.active {
    display: flex;
  }

  // Style for the detail map content
  .detail-map-content {
    width: 90%;
    height: 90%;
    max-width: 1200px;
    max-height: 800px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    background-color: $white_color;
    position: relative;
  }

  // Custom close button for the detail map
  .detail-map-close {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: z-index(base);
    background-color: $white_color;
    color: $gray_color;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: darken($white_color, 5%);
      color: $primary_color;
    }

    // RTL support
    html[dir="rtl"] & {
      right: auto;
      left: 15px;
    }
  }

  // DetailMap footer
  .detail-map-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: $white_color;
    padding: 12px 15px;
    border-top: 1px solid $border_color;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .hotel-details {
      h4 {
        margin: 0 0 5px;
        font-size: 16px;
        font-weight: 600;
        color: $primary-text-color;
      }

      .location {
        font-size: 14px;
        color: $gray_color;
        margin: 0;
      }
    }

    .hotel-actions {
      display: flex;
      gap: 10px;

      button {
        background-color: $white_color;
        border: 1px solid $border_color;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;

        &.primary {
          background-color: $primary_color;
          color: $white_color;
          border-color: $primary_color;

          &:hover {
            background-color: darken($primary_color, 5%);
          }
        }

        &:hover {
          background-color: darken($white_color, 5%);
        }
      }
    }
  }
}

// Media queries for responsive design
@media (max-width: 768px) {
  .detail-map-fixed-container {
    .detail-map-content {
      width: 95%;
      height: 95%;
    }

    .detail-map-footer {
      flex-direction: column;
      gap: 10px;

      .hotel-actions {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}