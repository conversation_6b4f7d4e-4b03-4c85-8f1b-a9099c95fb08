"use client";
import React, { ChangeEvent, useState } from "react";

// Define the component's props
interface SpecialRequestProps {
  requests: string[];
  onRequestsChange: (newRequests: string[]) => void;
  maxRequests?: number;
  maxLength?: number;
}

function SpecialRequest({
  requests,
  onRequestsChange,
  maxRequests = 5,
  maxLength = 200,
}: SpecialRequestProps) {
  const [inputValue, setInputValue] = useState<string>("");
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingValue, setEditingValue] = useState<string>("");

  const handleInputChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    if (e.target.value.length <= maxLength) {
      setInputValue(e.target.value);
    }
  };

  const handleAddRequest = () => {
    if (inputValue.trim() !== "" && requests.length < maxRequests) {
      const updatedRequests = [...requests, inputValue.trim()];
      onRequestsChange(updatedRequests);
      setInputValue("");
    }
  };

  const handleRemoveRequest = (indexToRemove: number) => {
    const updatedRequests = requests.filter((_, index) => index !== indexToRemove);
    onRequestsChange(updatedRequests);
    // Reset editing state if we're removing the item being edited
    if (editingIndex === indexToRemove) {
      setEditingIndex(null);
      setEditingValue("");
    }
  };

  const startEditing = (index: number, currentValue: string) => {
    setEditingIndex(index);
    setEditingValue(currentValue);
  };

  const handleEditingChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    if (e.target.value.length <= maxLength) {
      setEditingValue(e.target.value);
      // Real-time update to parent
      if (editingIndex !== null) {
        const updatedRequests = [...requests];
        updatedRequests[editingIndex] = e.target.value.trim();
        onRequestsChange(updatedRequests);
      }
    }
  };

  const finishEditing = () => {
    if (editingIndex !== null && editingValue.trim() !== "") {
      const updatedRequests = [...requests];
      updatedRequests[editingIndex] = editingValue.trim();
      onRequestsChange(updatedRequests);
    }
    setEditingIndex(null);
    setEditingValue("");
  };

  const cancelEditing = () => {
    setEditingIndex(null);
    setEditingValue("");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (editingIndex !== null) {
        finishEditing();
      } else {
        handleAddRequest();
      }
    }
    if (e.key === "Escape" && editingIndex !== null) {
      cancelEditing();
    }
  };

  return (
    <div className="w-full mx-auto bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className=" px-8 py-6 border-b border-gray-100">
        <div className="flex items-center space-x-3">

          <div>
            <h3 className="text-xl font-bold text-gray-900">Special Requests</h3>
            <p className="text-sm text-gray-600 mt-1">
              Customize your stay with personalized requests ({requests.length}/{maxRequests})
            </p>
          </div>
        </div>
      </div>

      <div className="p-8">
        {/* Description */}
        <div className="mb-8">
          <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-100">
            <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-blue-800 leading-relaxed">
              We'll forward your requests to the hotel. All requests are subject to availability 
              and may incur additional charges. Click on any request to edit it.
            </p>
          </div>
        </div>

        {/* Current Requests */}
        {requests.length > 0 && (
          <div className="mb-8">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Your Requests
            </h4>
            <div className="space-y-3">
              {requests.map((request, index) => (
                <div
                  key={index}
                  className="group bg-white border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200"
                >
                  {editingIndex === index ? (
                    <div className="p-4">
                      <div className="relative">
                        <textarea
                          className="w-full px-4 py-3 border border-blue-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-gray-900 bg-blue-50"
                          value={editingValue}
                          onChange={handleEditingChange}
                          onKeyDown={handleKeyPress}
                          placeholder="Edit your request..."
                          maxLength={maxLength}
                          rows={3}
                          autoFocus
                        />
                        <div className="absolute bottom-3 right-3 text-xs text-gray-500 bg-white px-2 py-1 rounded">
                          {editingValue.length}/{maxLength}
                        </div>
                      </div>
                      <div className="flex justify-end space-x-2 mt-3">
                        <button
                          type="button"
                          onClick={cancelEditing}
                          className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                        >
                          Cancel
                        </button>
                        <button
                          type="button"
                          onClick={finishEditing}
                          className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors duration-200"
                        >
                          Save
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-start justify-between p-4">
                      <button
                        type="button"
                        onClick={() => startEditing(index, request)}
                        className="flex-1 text-left text-gray-800 hover:text-blue-600 transition-colors duration-200 mr-4"
                      >
                        <div className="flex items-start space-x-3">
                          <span className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold mt-0.5">
                            {index + 1}
                          </span>
                          <p className="text-sm leading-relaxed break-words">{request}</p>
                        </div>
                      </button>
                      <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <button
                          type="button"
                          onClick={() => startEditing(index, request)}
                          className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-all duration-200"
                          title="Edit request"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          type="button"
                          onClick={() => handleRemoveRequest(index)}
                          className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-all duration-200"
                          title="Remove request"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Add New Request Form */}
        {requests.length < maxRequests && (
          <div className="space-y-4">
            <div className="space-y-3">
              <div className="relative">
                <textarea
                  className="w-full px-4 py-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 placeholder-gray-500 bg-white shadow-sm"
                  onChange={handleInputChange}
                  onKeyDown={handleKeyPress}
                  value={inputValue}
                  placeholder="Describe your special request in detail..."
                  maxLength={maxLength}
                  rows={4}
                />
                <div className="absolute bottom-4 right-4 text-xs text-gray-500 bg-white px-2 py-1 rounded shadow-sm">
                  {inputValue.length}/{maxLength}
                </div>
              </div>
              
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={handleAddRequest}
                  disabled={inputValue.trim() === ""}
                  className="px-8 py-3 bg-[#003b95] text-white rounded-lg font-medium  disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:shadow-none flex items-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span>Add Request</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Max Requests Reached Message */}
        {requests.length >= maxRequests && (
          <div className="p-6 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <svg className="w-6 h-6 text-amber-600 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div>
                <h4 className="text-amber-800 font-medium mb-1">Maximum Requests Reached</h4>
                <p className="text-sm text-amber-700">
                  You've reached the maximum of {maxRequests} special requests. 
                  Remove an existing request to add a new one, or edit any current request by clicking on it.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default SpecialRequest;