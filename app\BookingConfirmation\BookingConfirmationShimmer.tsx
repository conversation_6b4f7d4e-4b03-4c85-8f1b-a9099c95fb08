// src/app/BookingConfirmation/BookingConfirmationShimmer.tsx

import React from 'react';

const ShimmerBlock = ({ className }: { className?: string }) => {
  return (
    <div
      className={`bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:1000px_100%] animate-shimmer rounded-md ${className}`}
    ></div>
  );
};

export default function BookingConfirmationShimmer() {
  return (
    <>
      {/* Shimmer Header */}
      <div className="p-[30px] text-center bg-gray-200">
        <div className="h-[50px] w-[50px] mx-auto bg-gray-300 rounded-full mb-4"></div>
        <ShimmerBlock className="h-7 w-48 mx-auto mb-2.5" />
        <ShimmerBlock className="h-5 w-64 mx-auto" />
      </div>

      {/* Shimmer Confirmation Code */}
      <div className="bg-gray-100 p-4 text-center border-b border-gray-200">
        <div className="flex justify-center items-center">
          <ShimmerBlock className="h-5 w-32 mr-2" />
          <ShimmerBlock className="h-6 w-40" />
        </div>
      </div>

      <div className="p-[25px]">
        {/* Shimmer Reservation Details */}
        <div className="mb-[30px]">
          <div className="h-7 w-40 bg-gray-200 rounded-md mb-5 pb-2.5 border-b border-gray-200"></div>
          <div className="flex flex-wrap mb-4">
            <div className="flex-1 min-w-[250px] pr-2.5 mb-4">
              <div className="flex items-start">
                <div className="h-5 w-5 bg-gray-300 rounded-full mr-4"></div>
                <div className="flex-1">
                  <ShimmerBlock className="h-4 w-16 mb-2" />
                  <ShimmerBlock className="h-5 w-40" />
                </div>
              </div>
            </div>
            <div className="flex-1 min-w-[250px] pl-2.5 mb-4">
              <div className="flex items-start">
                <div className="h-5 w-5 bg-gray-300 rounded-full mr-4"></div>
                <div className="flex-1">
                  <ShimmerBlock className="h-4 w-20 mb-2" />
                  <ShimmerBlock className="h-5 w-32" />
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap mb-4">
            {/* Repeat for other details */}
            <div className="flex-1 min-w-[250px] pr-2.5 mb-4">
              <div className="flex items-start">
                <div className="h-5 w-5 bg-gray-300 rounded-full mr-4"></div>
                <div className="flex-1">
                  <ShimmerBlock className="h-4 w-16 mb-2" />
                  <ShimmerBlock className="h-5 w-36" />
                </div>
              </div>
            </div>
            <div className="flex-1 min-w-[250px] pl-2.5 mb-4">
              <div className="flex items-start">
                <div className="h-5 w-5 bg-gray-300 rounded-full mr-4"></div>
                <div className="flex-1">
                  <ShimmerBlock className="h-4 w-20 mb-2" />
                  <ShimmerBlock className="h-5 w-36" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Shimmer Payment Summary */}
        <div className="mb-[30px]">
          <div className="h-7 w-44 bg-gray-200 rounded-md mb-5 pb-2.5 border-b border-gray-200"></div>
          <div className="bg-gray-100 p-5 rounded-md">
            <div className="flex justify-between py-2.5 border-b border-gray-200">
              <ShimmerBlock className="h-5 w-32" />
              <ShimmerBlock className="h-5 w-20" />
            </div>
            <div className="flex justify-between py-2.5 border-b border-gray-200">
              <ShimmerBlock className="h-5 w-24" />
              <ShimmerBlock className="h-5 w-16" />
            </div>
            <div className="flex justify-between pt-4 mt-2 border-t border-gray-200">
              <ShimmerBlock className="h-6 w-28" />
              <ShimmerBlock className="h-6 w-24" />
            </div>
          </div>
        </div>

        {/* Shimmer Actions */}
        <div className="flex gap-3 justify-center mt-[30px]">
          <ShimmerBlock className="h-10 w-32 rounded" />
          <ShimmerBlock className="h-10 w-32 rounded" />
          <ShimmerBlock className="h-10 w-24 rounded" />
        </div>
      </div>
    </>
  );
}