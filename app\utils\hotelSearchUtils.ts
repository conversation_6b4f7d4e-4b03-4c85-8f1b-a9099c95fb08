import { HotelSearchData, HotelSearchFormData } from "@/models/hotel/search-page.model";

/**
 * Utility functions for hotel search data management
 */

/**
 * Load hotel search data from localStorage
 * @returns HotelSearchData object or null if not found
 */
export const loadHotelSearchDataFromStorage = (): HotelSearchData | null => {
  if (typeof window === 'undefined') return null;
  
  try {
    const savedData = localStorage.getItem('hotelSearchData');
    if (savedData) {
      return JSON.parse(savedData) as HotelSearchData;
    }
  } catch (error) {
    console.error('Error loading hotel search data from localStorage:', error);
  }
  
  return null;
};

/**
 * Load hotel search form data from localStorage
 * @returns HotelSearchFormData object or null if not found
 */
export const loadHotelSearchFormDataFromStorage = (): HotelSearchFormData | null => {
  if (typeof window === 'undefined') return null;
  
  try {
    const savedData = localStorage.getItem('hotelSearchFormData');
    if (savedData) {
      return JSON.parse(savedData) as HotelSearchFormData;
    }
  } catch (error) {
    console.error('Error loading hotel search form data from localStorage:', error);
  }
  
  return null;
};

/**
 * Save hotel search data to localStorage
 * @param searchData - The search data to save
 */
export const saveHotelSearchDataToStorage = (searchData: HotelSearchData): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('hotelSearchData', JSON.stringify(searchData));
    console.log('Hotel search data saved to localStorage:', searchData);
  } catch (error) {
    console.error('Error saving hotel search data to localStorage:', error);
  }
};

/**
 * Save hotel search form data to localStorage
 * @param formData - The form data to save
 */
export const saveHotelSearchFormDataToStorage = (formData: HotelSearchFormData): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('hotelSearchFormData', JSON.stringify(formData));
    console.log('Hotel search form data saved to localStorage:', formData);
  } catch (error) {
    console.error('Error saving hotel search form data to localStorage:', error);
  }
};

/**
 * Clear all hotel search data from localStorage
 */
export const clearHotelSearchDataFromStorage = (): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem('hotelSearchData');
    localStorage.removeItem('hotelSearchFormData');
    console.log('Hotel search data cleared from localStorage');
  } catch (error) {
    console.error('Error clearing hotel search data from localStorage:', error);
  }
};

/**
 * Convert form data to API search data format
 * @param formData - The form data to convert
 * @param geoCode - Optional geo coordinates override
 * @param locationId - Optional location ID override
 * @returns HotelSearchData object
 */
export const convertFormDataToSearchData = (
  formData: HotelSearchFormData,
  geoCode?: { lat: string; long: string },
  locationId?: string
): HotelSearchData => {
  // Use coordinates from form data if available, otherwise use override, otherwise default to Dubai
  const finalGeoCode = formData.geoCode || geoCode || {
    lat: "25.27063",
    long: "55.30037"
  };

  // Use location ID from form data if available, otherwise use override, otherwise default to Dubai
  const finalLocationId = formData.locationId || locationId || "328619";

  // Convert date format from ISO to YYYY-MM-DD
  const formatDateForAPI = (isoDate: string | null): string => {
    if (!isoDate) return new Date().toISOString().split('T')[0];
    return new Date(isoDate).toISOString().split('T')[0];
  };

  // Convert rooms data to API format
  const rooms = formData.roomsData.map(room => ({
    adults: room.adults.toString(),
    children: room.children.toString(),
    childAges: room.childrenAges.map(child => child.age.toString())
  }));

  console.log('Converting form data to search data:', {
    formDataLocationId: formData.locationId,
    formDataGeoCode: formData.geoCode,
    finalLocationId,
    finalGeoCode,
    searchQuery: formData.searchQuery
  });

  return {
    geoCode: finalGeoCode,
    locationId: finalLocationId,
    currency: "INR",
    culture: "en-US",
    checkIn: formatDateForAPI(formData.checkInDate),
    checkOut: formatDateForAPI(formData.checkOutDate),
    rooms: rooms
  };
};

/**
 * Validate hotel search data
 * @param searchData - The search data to validate
 * @returns boolean indicating if the data is valid
 */
export const validateHotelSearchData = (searchData: HotelSearchData): boolean => {
  if (!searchData) return false;
  
  // Check required fields
  if (!searchData.geoCode?.lat || !searchData.geoCode?.long) return false;
  if (!searchData.locationId) return false;
  if (!searchData.checkIn || !searchData.checkOut) return false;
  if (!searchData.rooms || searchData.rooms.length === 0) return false;
  
  // Validate dates
  const checkInDate = new Date(searchData.checkIn);
  const checkOutDate = new Date(searchData.checkOut);
  if (checkInDate >= checkOutDate) return false;
  
  // Validate rooms
  for (const room of searchData.rooms) {
    if (!room.adults || parseInt(room.adults) < 1) return false;
    if (parseInt(room.children) < 0) return false;
  }
  
  return true;
};

/**
 * Extract city name from full location string
 * @param fullLocation - Full location string like "Dubai, United Arab Emirates"
 * @returns City name like "Dubai"
 */
export const extractCityName = (fullLocation: string): string => {
  if (!fullLocation) return fullLocation;

  // Split by comma and take the first part (city/landmark name)
  const parts = fullLocation.split(',');
  return parts[0].trim();
};

/**
 * Get current search data from context and localStorage for debugging
 * @returns Object with both form data and search data
 */
export const getCurrentSearchData = () => {
  const formData = loadHotelSearchFormDataFromStorage();
  const searchData = loadHotelSearchDataFromStorage();

  console.log('Current Hotel Search Form Data:', formData);
  console.log('Current Hotel Search API Data:', searchData);

  return {
    formData,
    searchData
  };
};
