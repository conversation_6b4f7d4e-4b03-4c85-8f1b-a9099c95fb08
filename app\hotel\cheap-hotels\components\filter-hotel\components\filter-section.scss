@use '/styles/variable' as *;
@use "sass:color";


.type-filter {
  width: 100%;
  height: auto;
  padding: 12px 16px 8px 13px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  &:last-child {
    border-bottom: none;
  }

  .head {
    font-size: 14px;
    font-weight: 700;
  }

  .filter-section {
    display: flex;
    flex-direction: column;
    cursor: pointer;

    .filter-item {
      padding: 5px 0;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 6px;

      .checkBox {
        input[type="checkbox"] {
          transform: scale(1.3);
        }
      }

      .content {
        flex: 1;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .label {
          font-size: 13px;
          color: $filter_black1;
        }

        .count {
          font-size: 12px;
          color: $filter_black2;
        }
      }
    }
  }

  .show-more-button {
    color: $secondary_color;
    padding: 8px 0;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    transition: color 0.2s ease;
    background: none;
    border: none;
    text-decoration: underline;

    &:hover {
      color: color.adjust($secondary_color, $lightness: -20%);
    }
  }
}