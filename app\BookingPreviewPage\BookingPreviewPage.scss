@use "/styles/variable" as *;
@use "sass:color";
@use "/styles/zIndex" as * ;

.booking-preview-page {
  background-color: #f5f7fa;
  padding-top: 20px;
}

.booking-preview-container {
  // max-width: 1280px;
  // padding: 20px;
  // margin: 0 auto;
  padding: 0 0 30px 0;

  display: flex;
  flex-direction: row;
  column-gap: 20px;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    height: calc(100vh - 160px);
  }

  @media (max-width: $breakpoint-sm) {
    row-gap: 10px;
  }

  .left-section {
    width: calc(100% - 400px);

    display: flex;
    flex-direction: column;
    row-gap: 20px;

    @media (max-width: $breakpoint-md) {
      width: 100%;
    }

    .booking-preview-footer {
      display: flex;
      justify-content: end;

      .footer-btn {
        width: 250px;
        min-height: 50px;
        padding: 0 20px;
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        background-color: $primary_color;
        border-radius: 10px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: color.adjust($primary_color, $lightness: -5%);
        }
      }
    }
  }

  .right-section {
    max-width: 400px;
    @media (max-width: $breakpoint-md) {
      max-width: 100%;
    }

    width: 100%;
    display: flex;
    flex-direction: column;
    row-gap: 20px;

      .payment-disclaimer-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .booking-error {
      position: relative;
      margin-bottom: 1rem;
      
      button {
        &:hover {
          opacity: 0.8;
        }
      }
    }

      p {
        font-size: 16px;
        color: $booking_black;
        margin-bottom: 10px;

        @media (max-width: $breakpoint-sm) {
          font-size: 14px;

        }

        a {
          color: #0770e4;
          text-decoration: underline;
          font-weight: 500;
        }
      }

      .visa-footer {
        display: flex;
        flex-direction: column;

        &__label {
          font-size: 14px;
          color: #848794;
          margin-bottom: 0;
        }

        img {
          width: 220px;
          height: 34px;
        }
      }
    }
  }
}

.payment-disclaimer-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  // Hide on screens larger than 768px (show only on md screens and below)
  @media (min-width: calc($breakpoint-md + 1px)) {
    display: none;
  }

  @media (max-width: $breakpoint-md) {
    padding: 20px;
    margin-bottom: 40px;

  }

  p {
    font-size: 16px;
    color: $booking_black;
    margin-bottom: 10px;

    a {
      color: #0770e4;
      text-decoration: underline;
      font-weight: 500;
    }
  }

  .visa-footer {
    display: flex;
    flex-direction: column;

    @media (max-width: $breakpoint-md) {
      align-items: center;

    }

    &__label {
      font-size: 14px;
      color: #848794;
      margin-bottom: 0;
    }

    img {
      width: 220px;
      height: 34px;
    }
  }
}

.booking-preview-footer-bottom-0{
  background-color: #ffffff;
  width: 100%;
  padding: 10px 20px;
  display: none;
  justify-content: space-between;

  @media (max-width: $breakpoint-md){
    display: flex;
    
  }

  //position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  border-top:1px solid rgba(0, 0, 0, 0.1) ;
  z-index: z-index(modal);

  .totalAmtLabelValue{
    display: flex;
    flex-direction: column;

    .value{
      font-size: 20px;
      font-weight: 700;

      .xs-text{
        font-size: 12px;

      }
    }

    .label{
      font-size: 12px;
      font-weight: 500;

    }
  }

  .footer-btn2 {
    width: 180px;
    min-height: 50px;
    padding: 0 20px;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    background-color: $primary_color;
    border-radius: 10px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: color.adjust($primary_color, $lightness: -5%);
    }
  }
}




// Mobile modal content styles
.mobile-bottom-to-top-content {
  width: 100%;
  height: 100%;
  padding-top: 60px; // Account for fixed header
  overflow-y: auto;
   
}

.room-benefits-mobile-container {
  padding: 20px;
  background-color: #fff;

}

// Mobile room benefits with higher specificity
.mobile-bottom-to-top-modal-container {
  .room-benefits-mobile-container {
    .mobile-room-benefits {
      width: 100% !important;
      display: block !important;

      &__header {
        font-size: 18px !important;
        font-weight: bold !important;
        color: $booking_black !important;
        margin-bottom: 15px !important;
      }

      &__list {
        display: flex !important;
        flex-direction: column !important;
        gap: 15px !important;
        width: 100% !important;
        list-style: none !important;
        margin: 0 !important;
        padding: 0 !important;

        .list-item {
          display: flex !important;
          flex-direction: row !important;
          align-items: center !important;
          column-gap: 10px !important;
          color: $booking_black !important;
          padding: 12px 0 !important;
          border-bottom: 1px solid #e0e0e0 !important;
          background-color: transparent !important;
          margin: 0 !important;

          &:last-child {
            border-bottom: none !important;
          }

          .fa-check, .fa-solid.fa-check {
            color: #28a745 !important;
            font-size: 16px !important;
            min-width: 20px !important;
            width: 20px !important;
            height: 20px !important;
            display: inline-block !important;
            text-align: center !important;
          }

          .label {
            font-size: 16px !important;
            font-weight: 500 !important;
            line-height: 1.4 !important;
            flex: 1 !important;
            color: #333 !important;
            margin: 0 !important;
            padding: 0 !important;
          }
        }
      }
    }
  }
}

// Additional mobile modal heading style
.mobile-bottom-to-top-modal-heading {
  font-size: 18px;
  font-weight: 600;
  color: $booking_black;
  margin: 0;
}




