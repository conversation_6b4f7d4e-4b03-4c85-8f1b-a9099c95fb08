'use client'
import { useCommonContext } from '@/app/contexts/commonContext';
import { SimplifiedHotelBooking } from '@/models/hotel/booking.model';
import React, { useState } from 'react'

function Page() {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [bookingData, setBookingData] = useState<SimplifiedHotelBooking | null>(null);
    const { hotelSearchData , setHotelSearchData } = useCommonContext();
  return (
    <div>

    </div>
  )
}

export default Page