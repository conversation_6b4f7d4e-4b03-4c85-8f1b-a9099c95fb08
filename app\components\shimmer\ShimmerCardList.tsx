"use client";
import React from "react";

interface ShimmerCardListProps {
  viewMode: "list" | "grid";
  ShimmerComponent: React.ComponentType<{ type: "list" | "grid" }>;
  listCount?: number;
  gridCount?: number;
}

/**
 * Reusable component that renders multiple shimmer cards based on view mode
 * @param viewMode - Current view mode (list or grid)
 * @param ShimmerComponent - The shimmer component to render
 * @param listCount - Number of shimmer cards to show in list view (default: 3)
 * @param gridCount - Number of shimmer cards to show in grid view (default: 6)
 */
const ShimmerCardList: React.FC<ShimmerCardListProps> = ({
  viewMode,
  ShimmerComponent,
  listCount = 3,
  gridCount = 6
}) => {
  const count = viewMode === "list" ? listCount : gridCount;

  return (
    <>
      {Array.from({ length: count }, (_, index) => (
        <ShimmerComponent key={index} type={viewMode} />
      ))}
    </>
  );
};

export default ShimmerCardList;
