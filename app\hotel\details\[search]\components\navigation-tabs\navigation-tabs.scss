@use "@styles/variable" as *;
@use "/styles/zIndex" as *;
.navigation-tabs-container {
    &.sticky {
    position: sticky;
    width: 100%;
    top: 60px;
    background-color: #f2f2f2;
    left: 0;
    z-index: z-index(sticky);
    //border-top: 1px solid rgba(0, 0, 0, 0.2);

    // @media(max-width: $isMobile){
    //   top: 60px;
    // }
    }

    .navigation-tabs {
    display: flex;
    flex-direction: row;
    width: 100%;
    overflow-y: auto;
    //border-bottom: 1px solid rgba(0, 0, 0, 0.2);

    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // Internet Explorer and Edge

    &::-webkit-scrollbar {
        display: none; // Chrome, Safari, and Opera
    }

    .navigation-tab {
        padding: 10px 15px;
        font-size: 16px;
        font-weight: 500;
        color: #17181c;
        position: relative;
        cursor: pointer;
        user-select: none;

        @media (max-width: $breakpoint-sm) {
        font-size: 15px;
        }

        &:hover {
        background: rgba(0, 0, 0, 0.05);
        }

        &.active {
        //color: #0770e4;
        color: $secondary-color;
        }

        &.active::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 3px;
        //background-color: #0770e4;
        background-color: $secondary-color;
        border-radius: 5px;
        }
    }
    }
}