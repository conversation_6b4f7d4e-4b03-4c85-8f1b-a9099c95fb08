import { getBookingCurrency, getTotalPrice } from '../bookingTransformUtils';
import { UserBookingItem } from '@/models/user/user-bookings.model';

describe('bookingTransformUtils', () => {
  const mockBookingWithCurrency: UserBookingItem = {
    id: 1,
    booking_reference: 'TEST123',
    service_type: 'HOTEL',
    status: 'CONFIRMED',
    payment_status: 'PAID',
    user_id: 123,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    hotel_booking: {
      id: 1,
      master_booking_reference: 'MASTER123',
      provider_booking_id: 'PROVIDER123',
      booking_ref_id: 'REF123',
      hotel_id: 'HOTEL123',
      search_key: 'SEARCH123',
      rate_ids: ['RATE123'],
      rooms_allocations: [],
      billing_contact: {} as any,
      credit_card_info: null,
      special_requests: null,
      provider_response: {
        hotelBooking: {
          totalRate: 1234.*********,
          rates: [
            {
              currency: 'USD',
              totalRate: 1234.*********,
              baseRate: 1000,
              taxes: [],
              id: 'RATE123',
              availability: 'AVAILABLE',
              needsPriceCheck: false,
              isPackageRate: false,
              providerId: 'PROVIDER123',
              providerName: 'Test Provider',
              isContractedRate: false,
              type: 'STANDARD',
              minSellingRate: 1000,
              publishedRate: 1200,
              fees: null,
              dailyRates: [],
              refundable: true,
              refundability: 'REFUNDABLE',
              allGuestsInfoRequired: false,
              onlineCancellable: true,
              specialRequestSupported: false,
              payAtHotel: false,
              cardRequired: true,
              policies: [],
              boardBasis: { description: 'Room Only', type: 'RO' },
              offers: [],
              cancellationPolicies: [],
              includes: null,
              additionalCharges: [],
              depositRequired: false,
              guaranteeRequired: false,
              distributionType: 'STANDARD',
              distributionChannel: 'ONLINE',
              publishedRateProviderId: 'PROVIDER123',
              publishedRateProviderName: 'Test Provider',
              IsPassportMandatory: false,
              IsPANMandatory: false,
              providerHotelId: 'HOTEL123',
              isChildConvertedToAdult: false,
              traderInformation: { trader: [] },
              deposits: [],
              occupancies: []
            }
          ],
          supplierRates: [],
          hotel: {} as any,
          rooms: [],
          roomsAllocations: [],
          billingContact: {} as any,
          tripStartDate: '2024-01-01',
          tripEndDate: '2024-01-03',
          providerName: 'Test Provider'
        }
      }
    }
  };

  describe('getBookingCurrency', () => {
    it('should extract currency from rates', () => {
      expect(getBookingCurrency(mockBookingWithCurrency)).toBe('USD');
    });

    it('should return INR as fallback when no currency found', () => {
      const bookingWithoutCurrency = {
        ...mockBookingWithCurrency,
        hotel_booking: {
          ...mockBookingWithCurrency.hotel_booking,
          provider_response: {
            hotelBooking: {
              ...mockBookingWithCurrency.hotel_booking.provider_response.hotelBooking,
              rates: []
            }
          }
        }
      };
      expect(getBookingCurrency(bookingWithoutCurrency)).toBe('INR');
    });

    it('should handle missing hotel booking data', () => {
      const bookingWithoutHotelBooking = {
        ...mockBookingWithCurrency,
        hotel_booking: null as any
      };
      expect(getBookingCurrency(bookingWithoutHotelBooking)).toBe('INR');
    });

    it('should ignore "not available" currency', () => {
      const bookingWithNotAvailableCurrency = {
        ...mockBookingWithCurrency,
        hotel_booking: {
          ...mockBookingWithCurrency.hotel_booking,
          provider_response: {
            hotelBooking: {
              ...mockBookingWithCurrency.hotel_booking.provider_response.hotelBooking,
              rates: [
                {
                  ...mockBookingWithCurrency.hotel_booking.provider_response.hotelBooking.rates[0],
                  currency: 'Currency not available'
                }
              ]
            }
          }
        }
      };
      expect(getBookingCurrency(bookingWithNotAvailableCurrency)).toBe('INR');
    });
  });

  describe('getTotalPrice', () => {
    it('should extract total price from totalRate', () => {
      expect(getTotalPrice(mockBookingWithCurrency)).toBe(1234.*********);
    });

    it('should sum rates when totalRate is not available', () => {
      const bookingWithoutTotalRate = {
        ...mockBookingWithCurrency,
        hotel_booking: {
          ...mockBookingWithCurrency.hotel_booking,
          provider_response: {
            hotelBooking: {
              ...mockBookingWithCurrency.hotel_booking.provider_response.hotelBooking,
              totalRate: 0,
              rates: [
                { ...mockBookingWithCurrency.hotel_booking.provider_response.hotelBooking.rates[0], totalRate: 500 },
                { ...mockBookingWithCurrency.hotel_booking.provider_response.hotelBooking.rates[0], totalRate: 300 }
              ]
            }
          }
        }
      };
      expect(getTotalPrice(bookingWithoutTotalRate)).toBe(800);
    });

    it('should return 0 when no price data available', () => {
      const bookingWithoutPrice = {
        ...mockBookingWithCurrency,
        hotel_booking: {
          ...mockBookingWithCurrency.hotel_booking,
          provider_response: {
            hotelBooking: {
              ...mockBookingWithCurrency.hotel_booking.provider_response.hotelBooking,
              totalRate: 0,
              rates: []
            }
          }
        }
      };
      expect(getTotalPrice(bookingWithoutPrice)).toBe(0);
    });
  });
});
