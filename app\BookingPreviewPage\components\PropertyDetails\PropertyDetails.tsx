"use client";
import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import "./PropertyDetails.scss";
import Image from "next/image";
import {
  RoomBenefits,
  RoomDetail,
  TravelDetails,
} from "../../hotel-booking-details.model";
import roomDummyImage from 'public/assets/img/hotel-images/roomDummyImage.webp';

type PropertyDetailsProps = {
  setIsMobileBenefitsActive?: Dispatch<SetStateAction<boolean>>;
  thumbnailImageURL?: string;
  name?: string;
  address?: string;
  starRating?: string;
  travelDetails?: TravelDetails;
  roomDetail?: RoomDetail;
  roomBenefits?: RoomBenefits;
};

function PropertyDetails({
  setIsMobileBenefitsActive,
  thumbnailImageURL,
  name,
  address,
  starRating,
  travelDetails,
  roomDetail,
  roomBenefits,
}: PropertyDetailsProps) {

    const [windowWidth, setWindowWidth] = useState<number | null>(null);
    const [showAllProperties, setShowAllProperties] = useState<boolean>(false);

    useEffect(() => {
    // Runs only on the client
    const handleResize = () => setWindowWidth(window.innerWidth);

    handleResize(); // set initially
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  return (
    <div className="property-details-container">
      <div className="propery-header">
        <div className="property-info">
          <div className="property-info__image">
            <Image src={thumbnailImageURL || '' } alt="property-image" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"  fill priority/>
          </div>
          <div className="property-info__info">
            <div className="property-info__title-star-rating">
              <h6 className="title">{name} </h6>
              <span className="star-rating">
                <span className="star-rating">
                  {Number.isFinite(Number(starRating)) && Number(starRating) > 0
                    ? [...Array(Number(starRating))].map((_, index) => (
                        <i key={index} className="fa-solid fa-star" />
                      ))
                    : null}
                </span>
              </span>
            </div>
            <p className="property-info__location">{address || "address"}</p>

            <div className="property-info__room-info">
              <div className="info">
                <i className="fa-solid fa-door-open"></i>
                <span>
                  {travelDetails?.roomCount || "0"} x{" "}
                  {travelDetails?.roomType || ""}
                </span>
              </div>

              <div className="info">
                <i className="fa-solid fa-user-group"></i>
                <span>
                  {Number(travelDetails?.adultCount) ||
                    0 + Number(travelDetails?.childCount) ||
                    0}{" "}
                  Guests
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="checkin-checkout-container">
          <div className="checkin-out-section">
            <p className="date-label">CHECK-IN</p>
            <h6 className="date">
              {travelDetails?.travelDates?.formattedCheckinDate || ""}
            </h6>
            <p className="time">
              {travelDetails?.travelDates?.checkinTime || ""}
            </p>
          </div>

          <div className="day-count">
            <span>{travelDetails?.nightCount} Night</span>
          </div>

          <div className="checkin-out-section">
            <p className="date-label">CHECK-OUT</p>
            <h6 className="date">
              {travelDetails?.travelDates?.formattedCheckoutDate || ""}
            </h6>
            <p className="time">
              {travelDetails?.travelDates?.checkoutTime || ""}
            </p>
          </div>
        </div>
      </div>
      {windowWidth && windowWidth > 768 && <hr />}

      <div className="room-details-container">
        <h6 className="room-header">Room Details</h6>

        {windowWidth && windowWidth <= 768 && (
          <>
            <div className="checkin-checkout-container">
              <div className="checkin-out-section">
                <p className="date-label">
                  <i className="fa-solid fa-right-to-bracket"></i> CHECK-IN
                </p>
                <h6 className="date">
                  {travelDetails?.travelDates?.formattedCheckinDate || ""}
                </h6>
              </div>

              <div className="day-count">
                <span>{travelDetails?.nightCount} Night</span>
              </div>

              <div className="checkin-out-section">
                <p className="date-label">
                  <i className="fa-solid fa-right-from-bracket"></i>CHECK-OUT
                </p>
                <h6 className="date">
                  {travelDetails?.travelDates?.formattedCheckoutDate || ""}
                </h6>
              </div>
            </div>
            <hr />
          </>
        )}

        <div className="room-info">
          <div className="room-info__image">
            {roomDetail?.roomImageUrl && (
              <Image src={roomDetail?.roomImageUrl} alt="room-image" fill />
            )}
          </div>
          <div className="room-info__details">
            <h6 className="room-name">{travelDetails?.roomType || ""}</h6>
            <div className="room-details">
              {windowWidth && windowWidth <= 640 ? (
                <div className="detail-xs">
                  <p className="label">
                    {roomDetail?.properties?.slice(0, showAllProperties ? undefined : 3).map((property, index) => (
                      <span key={index}>
                        {property.data || ""}
                        {index !== Math.min(roomDetail.properties.length, showAllProperties ? roomDetail.properties.length : 3) - 1 && ", "}
                      </span>
                    ))}
                    {roomDetail?.properties && roomDetail.properties.length > 3 && !showAllProperties && (
                      <span
                        onClick={() => setShowAllProperties(true)}
                        style={{ color: '#0770E4', cursor: 'pointer', fontWeight: '600' }}
                      >
                        , +{roomDetail.properties.length - 3} more
                      </span>
                    )}
                    {showAllProperties && roomDetail?.properties && roomDetail.properties.length > 3 && (
                      <span
                        onClick={() => setShowAllProperties(false)}
                        style={{ color: '#0770E4', cursor: 'pointer', fontWeight: '600' }}
                      >
                        , show less
                      </span>
                    )}
                  </p>
                </div>
              ) : (
                <>
                  {roomDetail?.properties?.slice(0, showAllProperties ? undefined : 6).map((roomdetail, index) => (
                    <div key={index} className="detail">
                      <i className={`fa-solid ${roomdetail?.code}`}></i>
                      <span className="label" title={roomdetail?.data || ""}>{roomdetail?.data || ""}</span>
                    </div>
                  ))}
                  {roomDetail?.properties && roomDetail.properties.length > 6 && !showAllProperties && (
                    <div className="detail more-properties" onClick={() => setShowAllProperties(true)}>
                      <span className="label">+{roomDetail.properties.length - 6} more</span>
                    </div>
                  )}
                  {showAllProperties && roomDetail?.properties && roomDetail.properties.length > 6 && (
                    <div className="detail more-properties" onClick={() => setShowAllProperties(false)}>
                      <span className="label">show less</span>
                    </div>
                  )}
                </>
              )}
            </div>
            {windowWidth && windowWidth <= 768 && (
              <div
                onClick={() => setIsMobileBenefitsActive?.(true)}
                className="view-benefits-btn"
              >
                View Benefits
              </div>
            )}
          </div>
        </div>
        <div className="room-benefits">
          <h6 className="room-benefits__header">Room Benefits</h6>
          <div className="room-benefits__list">
            {roomBenefits?.otherBenefits.map((benefit, index) => (
              <div key={index} className="list-item">
                <i className="fa-solid fa-check"></i>
                <span className="label">{benefit || ""}</span>
              </div>
            ))}
          </div>
        </div>

        {windowWidth && windowWidth <= 768 && (
          <>
            <hr />
            <div className="guest-room-info">
              <p className="icon-label">
                <i className="fa-solid fa-user-group"></i> GUEST & ROOMS
              </p>
              <p className="value">
                {travelDetails?.roomCount || "0"} Room,{" "}
                {Number(travelDetails?.adultCount) ||
                  0 + Number(travelDetails?.childCount) ||
                  0}{" "}
                Guests
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default PropertyDetails;
