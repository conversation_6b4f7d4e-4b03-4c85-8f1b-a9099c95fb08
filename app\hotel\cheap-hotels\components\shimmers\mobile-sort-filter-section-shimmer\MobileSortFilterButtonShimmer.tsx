import React from 'react';

const MobileSortFilterButtonsShimmer = () => {
  return (
    <div className="fixed inset-x-0 bottom-0 w-full flex bg-white shadow-xl">
      <div className="flex-1 flex items-center justify-center p-4 border-r border-gray-200 last:border-r-0">
        <div className="w-2/3 h-4 rounded-md bg-gray-200 animate-shimmer"></div>
      </div>
      <div className="flex-1 flex items-center justify-center p-4 border-r border-gray-200 last:border-r-0">
        <div className="w-2/3 h-4 rounded-md bg-gray-200 animate-shimmer"></div>
      </div>
      <div className="flex-1 flex items-center justify-center p-4 border-r border-gray-200 last:border-r-0">
        <div className="w-2/3 h-4 rounded-md bg-gray-200 animate-shimmer"></div>
      </div>
    </div>
  );
};

export default MobileSortFilterButtonsShimmer;