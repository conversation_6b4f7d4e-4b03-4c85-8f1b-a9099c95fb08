import { useCommonContext } from "@/app/contexts/commonContext";
import Link from "next/link";
import DateRangeDisplay from "../date-range/DateRangeDisplay";
import { useTranslation } from "@/app/hooks/useTranslation";
import "./hotel-info-header.scss";

interface HotelInfoHeaderProps {
  handleSave: () => void;
  shareHotel: () => void;
  handleReserveClick: () => void;
}

export default function HotelInfoHeader({
  handleSave,
  shareHotel,
  handleReserveClick,
}: HotelInfoHeaderProps) {
    const { t } = useTranslation();
    const { hotelDetailsResponse , hotelSearchFormData } = useCommonContext();

  return (
    <div className="hotel-info-header">
      <div className="info-header">
        <div className="heading">
          <h2>{hotelDetailsResponse?.name || "..."}</h2>
          <div className="rating">
            {Array.from({ length: hotelDetailsResponse?.starRating ?? 0 }).map(
              (_, index) => (
                <i key={index} className="fa-solid fa-star"></i>
              )
            )}
          </div>
        </div>

        <div className="buttons">
          <div className="hotel-info-header-btn" onClick={handleSave}>
            {t("hotel.detail.save")} <i className="fa-solid fa-heart"></i>
          </div>

          <div
            className="hotel-info-header-btn"
            onClick={() =>shareHotel()}
          >
            {t("hotel.detail.share")}
            <i className="fa-solid fa-share-nodes"></i>
          </div>

          <div
            className="hotel-info-header-reserveBtn"
            onClick={handleReserveClick}
            style={{ marginLeft: "10px" }}
          >
            <i className="fa-solid fa-share-from-square"></i>
            {t("hotel.detail.reserve")}
          </div>
        </div>
      </div>

      <div className="review-location">
        <div className="review">
          <div className="rating">{hotelDetailsResponse?.userRating}</div>

          <div className="rating-detail">
            <p className="detail detail1">
              {hotelDetailsResponse?.userRatingCategoty}
            </p>
            <p className="detail detail2">
              {hotelDetailsResponse?.reviews?.length || 0}{" "}
              {t("hotel.detail.ratings")}
            </p>
          </div>
        </div>
        <div className="location">
          <div className="icon">
            <i className="fa-solid fa-location-dot"></i>
          </div>
          <div className="details">
            <div className="detail detail1">
              {hotelDetailsResponse?.address}
            </div>

            <Link href="">
              <div className="detail detail2">{t("hotel.detail.viewOnMap")}</div>
            </Link>
          </div>
        </div>
        <DateRangeDisplay
          checkInDate={hotelSearchFormData?.checkInDate ?? ""}
          checkOutDate={hotelSearchFormData?.checkOutDate ?? ""}
        />
      </div>
    </div>
  );
}
