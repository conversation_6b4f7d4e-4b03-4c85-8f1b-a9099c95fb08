// src/components/RecommendationCard/RecommendationCard.tsx

import React from 'react';
import { GroupedRate, Tax } from '@/models/hotel/rooms-and-rates.model';
import { getConvertedCurrency } from '@/helpers/hotel/currency-helper';
import { useCommonContext } from '@/app/contexts/commonContext';
import RecommendationCardItem from './components/RecommendationCardItem';

interface Recommendation {
  recommendationId: string;
  groupedRates: GroupedRate[];
}

interface RecommendationCardProps {
  recommendation: Recommendation;
  bookingInProgress: string | null;
  onBookNow: (groupedRate: GroupedRate, recommendation: Recommendation) => void;
  onRoomClick: (groupedRate: GroupedRate) => void;
  calculateTaxesFromAPI: (taxes: Tax[]) => number;
}

const RecommendationCard: React.FC<RecommendationCardProps> = ({
  recommendation,
  bookingInProgress,
  onBookNow,
  onRoomClick,
  calculateTaxesFromAPI
}) => {
  const totalSumOfBaseRates = recommendation.groupedRates.reduce((sum, current) => sum + current.rate.baseRate, 0);
  const { selectedCurrency } = useCommonContext();

  const totalOriginalSum = recommendation.groupedRates.reduce((sum, current) => {
    const originalPrice = current.rate.dailyRates.reduce((dailySum, dailyRate) => dailySum + (dailyRate.amount + dailyRate.discount), 0);
    return sum + originalPrice;
  }, 0);

  const hasRealDiscounts = recommendation.groupedRates.some(rate => rate.rate.dailyRates.some(dailyRate => dailyRate.discount > 0));

  const totalTaxesSum = recommendation.groupedRates.reduce((sum, current) => sum + calculateTaxesFromAPI(current.rate.taxes), 0);

  const bestRate = recommendation.groupedRates.reduce((best, current) => current.rate.totalRate < best.rate.totalRate ? current : best);

  const buttonId = `${recommendation.recommendationId}-${bestRate.rate.id}`;

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <div className="flex flex-col lg:flex-row">
        <div className="flex-1">
          <div className="divide-y divide-gray-200">
            {recommendation.groupedRates.map((groupedRate, index) => (
              <RecommendationCardItem
                key={`${recommendation.recommendationId}-${index}`}
                groupedRate={groupedRate}
                onClick={onRoomClick}
              />
            ))}
          </div>
        </div>

        <div className="lg:w-64 bg-gray-50 p-6 border-l border-gray-200">
          <div className="sticky top-4">
            <div className="text-center space-y-3">
              <div className="text-sm font-medium text-gray-600">
                Total for {recommendation.groupedRates.length} option{recommendation.groupedRates.length !== 1 ? 's' : ''}
              </div>

              {hasRealDiscounts && (
                <div className="text-sm text-gray-500 line-through">
                  {getConvertedCurrency(totalOriginalSum, selectedCurrency)}
                </div>
              )}

              <div className="text-3xl font-bold text-primary-color">
                {getConvertedCurrency(totalSumOfBaseRates, selectedCurrency)}
              </div>

              <div className="text-xs text-gray-500">
                + {getConvertedCurrency(totalTaxesSum, selectedCurrency)} Tax and Fees
              </div>

              {hasRealDiscounts && (
                <div className="text-xs text-green-600 font-medium">
                  You save {getConvertedCurrency((totalOriginalSum - totalSumOfBaseRates), selectedCurrency)}
                </div>
              )}

              <button
                className="w-full bg-primary-color hover:bg-primary-color-dark text-white font-semibold py-3 px-4 rounded-lg transition-colors mt-4 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={() => onBookNow(bestRate, recommendation)}
                disabled={bookingInProgress === buttonId}
              >
                {bookingInProgress === buttonId ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Booking...</span>
                  </div>
                ) : 'Book Now'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecommendationCard;