import { autoSuggestion, HotelSearchData, HotelSearchFormData, Room } from "@/models/hotel/search-page.model";

export const saveToRecentLocations = (newLocation: autoSuggestion) => {
  if (typeof window === "undefined") return;
  try {
    const existingRaw = localStorage.getItem("RecentLocations");
    let locations: autoSuggestion[] = existingRaw ? JSON.parse(existingRaw) : [];
    
    // Remove if it already exists to move it to the top
    locations = locations.filter(loc => loc.id !== newLocation.id);
    
    // Add to the beginning
    locations.unshift(newLocation);
    
    // Trim the array if it's too long
    if (locations.length > 5) {
      locations = locations.slice(0, 5);
    }
    
    localStorage.setItem("RecentLocations", JSON.stringify(locations));
  } catch (error) {
    console.error("Failed to save recent location:", error);
  }
};

export const getRecentLocations = (): autoSuggestion[] => {
    if (typeof window === "undefined") return [];
    try {
        const stored = localStorage.getItem("RecentLocations");
        return stored ? JSON.parse(stored) : [];
    } catch (error) {
        console.error("Failed to get recent locations:", error);
        return [];
    }
};

export const saveToRecentSearches = (newSearch: HotelSearchFormData) => {
  if (typeof window === "undefined") return;
  try {
    const existingRaw = localStorage.getItem("recentSearches");
    let searches: HotelSearchFormData[] = existingRaw ? JSON.parse(existingRaw) : [];

    // Use locationId as a unique identifier for the search
    searches = searches.filter(search => search.locationId !== newSearch.locationId);

    searches.unshift(newSearch);

    if (searches.length > 3) {
      searches = searches.slice(0, 3);
    }

    localStorage.setItem("recentSearches", JSON.stringify(searches));
  } catch (error) {
    console.error("Failed to save recent search:", error);
  }
};

export const getRecentSearches = (): HotelSearchFormData[] => {
    if (typeof window === "undefined") return [];
    try {
        const stored = localStorage.getItem("recentSearches");
        return stored ? JSON.parse(stored) : [];
    } catch (error) {
        console.error("Failed to get recent searches:", error);
        return [];
    }
};

export function filterHotelLocationByName( suggestions: autoSuggestion[], searchTerm: string): autoSuggestion[] {
  const term = searchTerm.trim().toLowerCase();
  return suggestions.filter(s =>
    s.name.toLowerCase().includes(term)
  );
}


export const convertSearchFormDataToSearchData = (
  formData: HotelSearchFormData,
  currency: string,
  culture: string
): HotelSearchData => {
  if (!formData.locationId) {
    throw new Error("Validation Error: locationId is missing from the search form data.");
  }
  if (!formData.geoCode || !formData.geoCode.lat || !formData.geoCode.long) {
    throw new Error("Validation Error: geoCode is missing or incomplete in the search form data.");
  }
  if (!formData.checkInDate) {
    throw new Error("Validation Error: Check-in date is missing.");
  }
  if (!formData.checkOutDate) {
    throw new Error("Validation Error: Check-out date is missing.");
  }
  if (!formData.roomsData || formData.roomsData.length === 0) {
      throw new Error("Validation Error: Room data is missing or empty.");
  }

  const apiRooms: Room[] = formData.roomsData.map(room => {
    if (typeof room.adults !== 'number' || room.adults < 1) {
        throw new Error(`Validation Error: Invalid number of adults for room ID ${room.id}. At least 1 adult is required.`);
    }
    if (typeof room.children !== 'number' || room.children < 0) {
        throw new Error(`Validation Error: Invalid number of children for room ID ${room.id}.`);
    }
    if (room.childrenAges.length !== room.children) {
        throw new Error(`Validation Error: The number of child ages provided does not match the children count for room ID ${room.id}.`);
    }

    const childAges = room.childrenAges.map(child => {
        if (typeof child.age !== 'number' || child.age < 0) {
            throw new Error(`Validation Error: An invalid age was provided for a child in room ID ${room.id}.`);
        }
        return String(child.age);
    });

    return {
      adults: String(room.adults),
      children: String(room.children),
      childAges: childAges,
    };
  });

  const apiRequestData: HotelSearchData = {
    locationId: formData.locationId,
    geoCode: {
        lat: formData.geoCode.lat,
        long: formData.geoCode.long,
    },
    checkIn: formData.checkInDate,
    checkOut: formData.checkOutDate,
    currency: 'INR',
    culture,
    rooms: apiRooms,
  };

  return apiRequestData;
};
