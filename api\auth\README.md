# Authentication API Integration

This directory contains the real API integration for authentication services, replacing the mock services.

## Files

- `authService.ts` - Main authentication service with real API calls

## API Endpoints

The service integrates with your backend API running on `http://localhost:8007`:

### 1. Initiate <PERSON>TP
```
POST /auth/initiate/otp
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
// OR
{
  "phone": "1234567890"
}
```

**Response:**
```json
{
  "success": true,
  "message": "O<PERSON> sent successfully",
  "data": {
    "otpSent": true
  }
}
```

### 2. Verify OTP
```
POST /auth/verify-otp
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
// OR
{
  "phone": "1234567890",
  "otp": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "O<PERSON> verified successfully",
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe"
    },
    "token": "jwt-token-here",
    "isNewUser": false,
    "verified": true
  }
}
```

## Configuration

Set the API base URL in your `.env.local` file:

```env
NEXT_PUBLIC_AUTH_API_BASE_URL=http://localhost:8007
```

## Usage

```typescript
import authService from '@/api/auth/authService';

// Send OTP
const otpResponse = await authService.initiateOtp('<EMAIL>');

// Verify OTP
const verifyResponse = await authService.verifyOtp('<EMAIL>', '123456');

// Check authentication status
const isLoggedIn = authService.isAuthenticated();

// Logout
authService.logout();
```

## Error Handling

The service provides consistent error handling with structured error responses:

```typescript
{
  success: false,
  message: "Error description",
  error: { /* detailed error info */ }
}
```

## Testing

You can test the API connectivity:

```typescript
const connectionTest = await authService.testConnection();
console.log(connectionTest.message);
```

## Migration from Mock Service

The old mock service in `app/components/login/api/authService.ts` has been deprecated. All authentication flows now use this real API integration.
