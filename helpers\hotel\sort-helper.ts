import { Hotel } from "@/models/hotel/list-page.model";

export function applySort(hotels: Hotel[], sortOption: string): Hotel[] {
  // Create a shallow copy to avoid mutating the original array
  const sortedHotels = [...hotels];

  switch (sortOption) {
    case 'Price (lowest first)':
      sortedHotels.sort((a, b) => a.fareDetail.totalPrice - b.fareDetail.totalPrice);
      break;
    
    case 'Price (highest first)':
      sortedHotels.sort((a, b) => b.fareDetail.totalPrice - a.fareDetail.totalPrice);
      break;
      
    case 'Property rating (high to low)':
      // Convert userRating from string to number for sorting
      sortedHotels.sort((a, b) => parseFloat(b.userRating) - parseFloat(a.userRating));
      break;

    case 'Property rating (low to high)':
      // Convert userRating from string to number for sorting
      sortedHotels.sort((a, b) => parseFloat(a.userRating) - parseFloat(b.userRating));
      break;

    // For 'Top picks for long stays' or any other default, we don't re-sort.
    // The original order is assumed to be the "Featured" order.
    case 'Top picks for long stays':
    default:
      break;
  }

  return sortedHotels;
}