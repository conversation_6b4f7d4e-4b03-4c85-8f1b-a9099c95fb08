"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter, usePathname } from "next/navigation";
import HotelSearchBar from "../hotel/HotelSearchBar/HotelSearchBar";
import { HotelSearchFormData } from "@/models/hotel/search-page.model";
import { useCommonContext } from "@/app/contexts/commonContext";
import { convertSearchFormDataToSearchData } from "@/helpers/hotel/search-page-helper";

interface LandingPageContainerProps {
  searchBgImageSrc: string;
}

// Services navigation items (same as in mobile menu)
const SERVICES_NAV_ITEMS = [
  {
    id: "hotels",
    icon: "/assets/img/Kindali Service icons/BLUE HOTEL PNG.png",
    label: "Stays",
    path: "/hotel"
  },
  {
    id: "flight",
    icon: "/assets/img/Kindali Service icons/BLUE FLIGHT PNG.png",
    label: "Flights",
    path: "/flights"
  },
  {
    id: "visa",
    icon: "/assets/img/Kindali Service icons/BLUE VISA PNG.png",
    label: "Visa",
    path: "/visa"
  },
  {
    id: "cruise",
    icon: "/assets/img/Kindali Service icons/BLUE SHIP PNG.png",
    label: "Cruise",
    path: "/cruise"
  }
];

const LandingPageContainer: React.FC<LandingPageContainerProps> = ({ searchBgImageSrc }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [isMobileView, setIsMobileView] = useState<boolean>(false);
  const { setHotelSearchFormData , setHotelSearchData ,setSearchKey , selectedCurrency } = useCommonContext();

  // Check if we're on the homepage
  const isHomePage = pathname === '/';

  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== 'undefined') {
        setIsMobileView(window.innerWidth <= 950);
      }
    };

    // Set initial value
    handleResize();

    // Add event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
    }

    // Cleanup
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, []);

  // Handle service navigation
  const handleServiceClick = (path: string) => {
    router.push(path);
  };

  // Conditional background style
  const backgroundStyle = isMobileView
    ? {} // No background for mobile
    : {
        backgroundImage: `linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url(${searchBgImageSrc})`,
      };

  const handleSearch = (formData: HotelSearchFormData) => {
    if(formData){
      setSearchKey("");
      localStorage.removeItem("searchKey");
      setHotelSearchFormData(formData);
      localStorage.setItem("hotelSearchFormData", JSON.stringify(formData));
      const searchData = convertSearchFormDataToSearchData(formData,selectedCurrency.to_currency_code , 'en-US');
      localStorage.setItem("hotelSearchData", JSON.stringify(searchData));
      setHotelSearchData(searchData);
      router.push(`/hotel/cheap-hotels/${encodeURIComponent(formData.searchQuery.toLowerCase().replace(/\s+/g, '-'))}`);
    }
  };

  return (
    <div className="hotel-searchbar-landing-main-container " style={backgroundStyle}>
     <div className={`${isMobileView? 'w-full' : 'common-container'}`}>
       {/* Hero Text Section */}
      <div className="hero-text-section">
        <h1 className="hero-heading">
          Find Your Perfect Stay
        </h1>
        <p className="hero-description">
          Discover amazing hotels, resorts, and accommodations worldwide with the best prices guaranteed.
        </p>
      </div>

      {/* Conditional rendering based on page and mobile view */}
      {isHomePage && isMobileView ? (
        /* Services Navigation Grid (Only on Homepage) */
        <div className="services-navigation-container">
          <div className="services-grid">
            {SERVICES_NAV_ITEMS.map((item, index) => (
              <div
                key={index}
                className="service-item"
                onClick={() => handleServiceClick(item.path)}
              >
                <div className="service-icon">
                  <Image
                    src={item.icon}
                    alt={item.label}
                    width={64}
                    height={64}
                  />
                </div>
                <span className="service-label">
                  {item.label}
                </span>
              </div>
            ))}
          </div>
        </div>
      ) : (
        /* Search Bar (On all other pages) */
        <div className="search-bar-landing" >
          <HotelSearchBar scrollOnOpen={true} onSearch={handleSearch} />
        </div>
      )}
     </div>
    </div>
  );
};

export default LandingPageContainer;
