import React, { useState } from 'react';
import './filter-section.scss';

// Reusable component for rendering a single filter section
interface FilterSectionProps<T> {
  title: string;
  items: T[];
  category: string;
  onCheckboxChange: (category: string, key: string | number, isChecked: boolean) => void;
  showCounts: boolean;
  t: (key: string) => string;
}

export const FilterSection = <T extends { key: string | number, isSelected: boolean, count?: number }>(
  { title, items, category, onCheckboxChange, showCounts, t }: FilterSectionProps<T>
) => {
    const [showAll, setShowAll] = useState(false);
    const displayedItems = showAll ? items : items.slice(0, 5);

    return (
        <div className="type-filter">
            <div className="head"><h5>{title}</h5></div>
            <div className="filter-section">
                {displayedItems.length > 0 && displayedItems.map((item, index) => (
                    <div className="filter-item" key={item.key || index}>
                        <div className="checkBox">
                            <input 
                                type="checkbox" 
                                checked={item.isSelected} 
                                onChange={(e) => onCheckboxChange(category, item.key, e.target.checked)} 
                            />
                        </div>
                        <div className="content">
                            <span className="label">
                                {item.key}
                                {category === 'hotelStarRating' && ` ${t("filters.stars")}`}
                            </span>
                            {showCounts && item.count !== undefined && <span className="count">{item.count}</span>}
                        </div>
                    </div>
                ))}
            </div>
            {items.length > 5 && (
                <button className="show-more-button" onClick={() => setShowAll(!showAll)}>
                    {showAll ? t('filters.showLess') : t('filters.showMore')}
                </button>
            )}
        </div>
    );
};