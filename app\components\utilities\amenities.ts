export interface AmenityDetails {
  key: string;
  displayName: string;
  iconName: string;
}

const AMENITY_MAP: Map<string, AmenityDetails> = new Map([
  ['wifi', { key: 'wifi', displayName: 'Free WiFi', iconName: 'Wifi' }],
  ['pool', { key: 'pool', displayName: 'Swimming Pool', iconName: 'Waves' }],
  ['parking', { key: 'parking', displayName: 'Parking', iconName: 'ParkingSquare' }],
  ['ac', { key: 'ac', displayName: 'Air Conditioning', iconName: 'AirVent' }],
  ['restaurant', { key: 'restaurant', displayName: 'Restaurant', iconName: 'UtensilsCrossed' }],
  ['bar', { key: 'bar', displayName: 'Bar/Lounge', iconName: 'GlassWater' }],
  ['room_service', { key: 'room_service', displayName: 'Room Service', iconName: 'Bellboy' }],
  ['airport_shuttle', { key: 'airport_shuttle', displayName: 'Airport Shuttle', iconName: 'Bus' }],
  ['front_desk', { key: 'front_desk', displayName: '24-hour Front Desk', iconName: 'Clock' }],
  ['fitness', { key: 'fitness', displayName: 'Fitness Center', iconName: 'Dumbbell' }],
  ['spa', { key: 'spa', displayName: 'Spa', iconName: 'Sparkles' }],
  ['family_friendly', { key: 'family_friendly', displayName: 'Family Friendly', iconName: 'Baby' }],
  ['pets', { key: 'pets', displayName: 'Pets Allowed', iconName: 'Dog' }],
  ['business_center', { key: 'business_center', displayName: 'Business Center', iconName: 'Briefcase' }],
  ['free_cancellation', { key: 'free_cancellation', displayName: 'Free Cancellation', iconName: 'CheckCircle2' }],
  ['breakfast', { key: 'breakfast', displayName: 'Breakfast Included', iconName: 'Coffee' }],
  ['pay_at_hotel', { key: 'pay_at_hotel', displayName: 'Pay at Hotel', iconName: 'CreditCard' }],
  ['partial_cancellation_allowed', { key: 'partial_cancellation_allowed', displayName: 'Partial Cancellation', iconName: 'Percent' }],
]);

export function getAmenityDetails(apiName: string): AmenityDetails {
  const lowerCaseName = apiName.toLowerCase();

  if (lowerCaseName.includes('wifi') || lowerCaseName.includes('internet')) return AMENITY_MAP.get('wifi')!;
  if (lowerCaseName.includes('pool')) return AMENITY_MAP.get('pool')!;
  if (lowerCaseName.includes('fitness') || lowerCaseName.includes('gym')) return AMENITY_MAP.get('fitness')!;
  if (lowerCaseName.includes('spa')) return AMENITY_MAP.get('spa')!;
  if (lowerCaseName.includes('restaurant')) return AMENITY_MAP.get('restaurant')!;
  if (lowerCaseName.includes('parking')) return AMENITY_MAP.get('parking')!;
  if (lowerCaseName.includes('pets allowed')) return AMENITY_MAP.get('pets')!;
  if (lowerCaseName.includes('free breakfast')) return AMENITY_MAP.get('breakfast')!;
  if (lowerCaseName.includes('air conditioning') || lowerCaseName.includes('air-conditioned')) return AMENITY_MAP.get('ac')!;
  if (lowerCaseName.includes('bar') || lowerCaseName.includes('lounge')) return AMENITY_MAP.get('bar')!;
  if (lowerCaseName.includes('room service')) return AMENITY_MAP.get('room_service')!;
  if (lowerCaseName.includes('shuttle')) return AMENITY_MAP.get('airport_shuttle')!;
  if (lowerCaseName.includes('front desk')) return AMENITY_MAP.get('front_desk')!;
  if (lowerCaseName.includes('business center')) return AMENITY_MAP.get('business_center')!;
  if (lowerCaseName.includes('partial cancellation')) return AMENITY_MAP.get('partial_cancellation_allowed')!;
  if (lowerCaseName.includes('free cancellation')) return AMENITY_MAP.get('free_cancellation')!;
  if (lowerCaseName.includes('pay at hotel')) return AMENITY_MAP.get('pay_at_hotel')!;

  return {
    key: lowerCaseName.replace(/\s+/g, '_'),
    displayName: apiName,
    iconName: 'Info',
  };
}