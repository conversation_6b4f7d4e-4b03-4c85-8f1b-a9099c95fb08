import { calculateNights, formatDateForDisplay } from "@/app/components/utilities/date-util";
import { CurrencyItem } from "@/models/common.model";
import { Hotel, SearchInitApiResponse, SearchInitHotel, SearchResultBatch } from "@/models/hotel/list-page.model";
import { HotelSearchFormData, SearchRoom } from "@/models/hotel/search-page.model";
import { getConvertedCurrency } from "./currency-helper";

function getUserRatingCategory(rating: number): string {
  if (rating >= 4.5) return 'Excellent';
  if (rating >= 4.0) return 'Very Good';
  if (rating >= 3.5) return 'Good';
  if (rating >= 3.0) return 'Average';
  if (rating > 0) return 'Fair';
  return 'Not Rated';
}

export function transformSearchInitApiResponse(apiResponse: SearchInitApiResponse): Hotel[] {
  const allHotels: Hotel[] = [];

  if (!apiResponse || !apiResponse.results) {
    return [];
  }

  apiResponse.results.forEach((resultBatch: SearchResultBatch) => {
    if (!resultBatch || !resultBatch.hotels) {
      return; 
    }

    resultBatch.hotels.forEach((apiHotel: SearchInitHotel) => {
      const mainFare = apiHotel.fareDetail?.[0] || { totalPrice: 0, taxes: 0 };
      const userRatingCategory = apiHotel.userRatingCategoty ?? getUserRatingCategory(apiHotel.userRating);
      const specialAmenitiesSet = new Set(apiHotel.specialAmenities);
      const sanitizedAmenities = apiHotel.amenities.filter(amenity => !specialAmenitiesSet.has(amenity));

      const transformedHotel: Hotel = {
        id: apiHotel.id,
        hotelId: parseInt(apiHotel.hotelId && apiHotel.hotelId.trim() !== "" ? apiHotel.hotelId : apiHotel.hotel_id, 10),
        locationId: parseInt(resultBatch.location_id, 10),
        name: apiHotel.name,
        address: apiHotel.address,
        city: apiHotel.city,
        userRating: apiHotel.userRating ? apiHotel.userRating.toString() : '0',
        userRatingCategory: userRatingCategory,
        starRating: parseInt(apiHotel.starRating, 10) || 0,
        comfortRating: apiHotel.comfortRating ?? 0,
        category: apiHotel.category,
        hotelType: apiHotel.HotelType ?? 'Hotel',
        accommodationType: apiHotel.HotelType ?? 'Hotel',
        roomsCountLeft: apiHotel.roomCountLeft ?? 0,
        geoLocationInfo: apiHotel.geoLocationInfo,
        isVisible: apiHotel.isVisible,
        about: apiHotel.about ?? '',
        amenities: sanitizedAmenities.map((name: string) => ({
          name: name,
          amenityUrl: '',
        })),
        specialAmenities: apiHotel.specialAmenities ?? [],
        imageInfoList: apiHotel.images.map((img) => ({
          pictureId: img.id.toString(),
          url: img.image_path,
          caption: img.alt_text,
          imageCategory: img.image_category_type,
          rank: img.sort_order,
        })),
        // topOfferings: apiHotel.attributes?.map((attr) => attr.value) ?? [],
        topOfferings: [],

        roomDetails: [
          {
            type: apiHotel.roomDetails.type ?? 'Standard Room',
            bedroom: parseInt(apiHotel.roomDetails.bedroom ?? '1', 10),
            livingRoom: parseInt(apiHotel.roomDetails.livingRoom ?? '0', 10),
            bathroom: parseInt(apiHotel.roomDetails.bathroom ?? '1', 10),
            size: apiHotel.roomDetails.size ?? 'N/A',
            bed: apiHotel.roomDetails.bed ?? 'Standard',
          },
        ],
        fareDetail: {
          totalPrice: mainFare.totalPrice,
          displayedBaseFare: mainFare.displayedBaseRate,
          board_basis: mainFare.board_basis ?? 'Room Only',
          currency_code: mainFare.currency_code,
          refundable: mainFare.refundable,
          taxes: mainFare.taxes,
        },
        taxesAndCharges: mainFare.taxes ?? 0,
        userRatingCount: 0, // Not available in the new API response
        distanceFromSearchedEntity: '', // Needs to be calculated separately
        fomoTags: [], // Not available in the new API response
        offer: {
          couponCode: '',
          instantDiscount: 0,
          displayedInstantDiscount: 0,
          cashback: 0,
          displayedCashback: 0,
          applyMessage: '',
        },
      };

      allHotels.push(transformedHotel);
    });
  });

  return allHotels;
}

export const updatePaginatedHotels = (
  newHotels: Hotel[],
  previousPages: Hotel[][],
  pageSize: number = 40
): Hotel[][] => {
  // If there are no previous pages, just paginate the new hotels and return.
  if (!previousPages || previousPages.length === 0) {
    const pages: Hotel[][] = [];
    for (let i = 0; i < newHotels.length; i += pageSize) {
      pages.push(newHotels.slice(i, i + pageSize));
    }
    return pages;
  }

  // Create a mutable copy of the previous pages.
  const updatedPages = [...previousPages];
  
  // Get the last page from the list. It might be partially filled.
  const lastPage = updatedPages.pop() || [];
  
  // Combine the hotels from the last page with the new batch of hotels.
  const combinedHotels = [...lastPage, ...newHotels];

  // Re-paginate the combined list.
  for (let i = 0; i < combinedHotels.length; i += pageSize) {
    const newPage = combinedHotels.slice(i, i + pageSize);
    updatedPages.push(newPage);
  }

  return updatedPages;
};


export const buildHotelSearchUrl = (formData: HotelSearchFormData): string => {
  if (!formData) return "";
  if (typeof window === "undefined") return ""; 

  const baseUrl = `${window.location.origin}${window.location.pathname}`;

  // Helper to format date as YYYY-MM-DD
  const formatDate = (date: string | null) => date ? date.split("T")[0] : "";

  // Convert roomsData into a compact safe string
  const serializeRooms = (rooms: SearchRoom[]): string => {
    return rooms.map(room => {
      const adults = `${room.adults}A`;
      const children = room.children > 0 
        ? `${room.children}C(${room.childrenAges.map(c => c.age).join(",")})`
        : "0C";
      return `${adults}${children}`;
    }).join("-");
  };

  const params = new URLSearchParams();

  if (formData.searchQuery) params.set("searchQuery", formData.searchQuery);
  if (formData.fullLocationData) params.set("fullLocationData", formData.fullLocationData);
  if (formData.locationId) params.set("locationId", formData.locationId);

  if (formData.geoCode) {
    if (formData.geoCode.lat) params.set("lat", formData.geoCode.lat);
    if (formData.geoCode.long) params.set("long", formData.geoCode.long);
  }

  if (formData.checkInDate) params.set("checkInDate", formatDate(formData.checkInDate));
  if (formData.checkOutDate) params.set("checkOutDate", formatDate(formData.checkOutDate));

  if (formData.roomsData && formData.roomsData.length > 0) {
    params.set("rooms", serializeRooms(formData.roomsData));
  }

  return `${baseUrl}?${params.toString()}`;
};

export const parseHotelSearchUrl = (url?: string): HotelSearchFormData | null => {
  try {
    // Use current browser URL if not passed explicitly
    const searchParams = new URL(url || window.location.href).searchParams;

    const formatDate = (date: string | null) => (date ? date.split("T")[0] : null);

    // Parse rooms string back to structured data
    const parseRooms = (roomsStr: string): SearchRoom[] => {
      if (!roomsStr) return [];
      return roomsStr.split("-").map((room, index) => {
        const match = room.match(/(\d+)A(\d+)C(?:\((.*?)\))?/);
        if (!match) return { id: index + 1, adults: 0, children: 0, childrenAges: [] };

        const adults = parseInt(match[1], 10);
        const children = parseInt(match[2], 10);
        const ages = match[3]
          ? match[3].split(",").map((age, i) => ({ id: i + 1, age: parseInt(age, 10) }))
          : [];

        return {
          id: index + 1,
          adults,
          children,
          childrenAges: ages,
        };
      });
    };

    const roomsData = parseRooms(searchParams.get("rooms") || "");

    return {
      searchQuery: searchParams.get("searchQuery") || "",
      fullLocationData: searchParams.get("fullLocationData") || undefined,
      locationId: searchParams.get("locationId") || undefined,
      geoCode: searchParams.get("lat") && searchParams.get("long")
        ? { lat: searchParams.get("lat")!, long: searchParams.get("long")! }
        : undefined,
      checkInDate: formatDate(searchParams.get("checkInDate")),
      checkOutDate: formatDate(searchParams.get("checkOutDate")),
      travelers: {
        adults: 0, // ignored in URL
        rooms: roomsData.length,
        children: roomsData.reduce((sum, r) => sum + r.children, 0),
      },
      roomsData,
    };
  } catch (e) {
    console.error("Failed to parse hotel search URL", e);
    return null;
  }
};

export const prepareFormattedMessage = ({
  formData,
  selectedHotels,
  selectedCurrency,
}: {
  formData: HotelSearchFormData;
  selectedHotels: Hotel[] | undefined;
  selectedCurrency: CurrencyItem;
}): string => {
  if (!selectedHotels || selectedHotels.length === 0) {
    return "No hotels selected";
  }

  const location = formData.searchQuery || "Your destination";
  const checkIn = formData.checkInDate ?
    formatDateForDisplay(formData.checkInDate) :
    "Not specified";
  const checkOut = formData.checkOutDate ?
    formatDateForDisplay(formData.checkOutDate) :
    "Not specified";
  const nights = calculateNights(
    formData.checkInDate ?? undefined,
    formData.checkOutDate ?? undefined
  );

  const hotelsList = selectedHotels
    .map(
      (hotel, index) =>
      `${index + 1}. *${hotel.name}*\n   Price: 
      ${getConvertedCurrency(hotel.fareDetail.totalPrice, selectedCurrency)}`
    )
    .join("\n\n");

  const searchUrl = buildHotelSearchUrl(formData);

  return `*Your Hotel Information for ${location}*

*Check In:* ${checkIn}
*Check Out:* ${checkOut}
*Details:* ${formData.travelers.adults} Pax, ${
    formData.travelers.rooms
  } Rooms, ${nights} Nights

-----------------------------------

${hotelsList}

-----------------------------------

*Thank you for choosing KindAli Travels*
Contact: 1234567890
Email: <EMAIL>

 *search page:*
${searchUrl}`;
};
