import React, { useEffect, useRef } from 'react';
import { X } from 'lucide-react';

interface SideModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  width?: number;
  position?: 'left' | 'right';
}

const SideModal: React.FC<SideModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  width = 50,
  position = 'right',
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }
    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    if (isOpen) {
      modalRef.current?.focus();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const positionClasses = position === 'left' ? 'justify-start' : 'justify-end';
  
  const dynamicStyles = {
    width: `${width}vw`,
    transform: isOpen
      ? 'translateX(0)'
      : position === 'left'
      ? 'translateX(-100%)'
      : 'translateX(100%)',
  };

  return (
    <div className={`fixed inset-0 z-50 flex ${positionClasses}`} role="dialog" aria-modal="true">
      <div className="fixed inset-0 bg-gray-800 bg-opacity-75 transition-opacity duration-300 ease-in-out" onClick={onClose} style={{ opacity: isOpen ? 1 : 0 }}></div>

      <div ref={modalRef} className="relative flex h-full flex-col overflow-hidden bg-white shadow-xl transition-transform duration-300 ease-in-out" style={dynamicStyles} onClick={(e) => e.stopPropagation()} tabIndex={-1}>
        <header className="flex flex-shrink-0 items-center justify-between border-b border-gray-200 p-4">
          {title && (
            <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
          )}
          <button onClick={onClose} className="ml-auto rounded-md p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-indigo-500" aria-label="Close modal">
            <X className="h-6 w-6" />
          </button>
        </header>

        <div className="flex-grow overflow-y-auto p-6">
          {children}
        </div>
      </div>
    </div>
  );
};

export default SideModal;