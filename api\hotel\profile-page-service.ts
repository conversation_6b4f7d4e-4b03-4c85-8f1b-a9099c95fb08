import { UserBookingsApiResponse, BookingQueryParams } from "@/models/user/user-bookings.model";
import apiService from "../api-service";

/**
 * Service for fetching user's booking history and managing bookings
 * This service handles all user-specific booking operations for the profile page
 */

/**
 * Fetch all bookings for a specific user
 * @param userId - The user ID to fetch bookings for
 * @param params - Optional query parameters for filtering, sorting, and pagination
 * @returns Promise<UserBookingsApiResponse> - Array of user bookings
 */
export const getUserBookings = async (
  userId: string | number,
  params?: BookingQueryParams
): Promise<UserBookingsApiResponse> => {
  try {
    // Build query string from parameters
    const queryParams = new URLSearchParams();

    if (params?.status) {
      queryParams.append('status', params.status);
    }
    if (params?.service_type) {
      queryParams.append('service_type', params.service_type);
    }
    if (params?.payment_status) {
      queryParams.append('payment_status', params.payment_status);
    }
    if (params?.limit) {
      queryParams.append('limit', params.limit.toString());
    }
    if (params?.offset) {
      queryParams.append('offset', params.offset.toString());
    }
    if (params?.sort_by) {
      queryParams.append('sort_by', params.sort_by);
    }
    if (params?.sort_order) {
      queryParams.append('sort_order', params.sort_order);
    }

    const queryString = queryParams.toString();
    const url = `api/v1/booking/user/${userId}${queryString ? `?${queryString}` : ''}`;

    console.log('🔄 Fetching user bookings from:', url);

    const response = await apiService.get3<UserBookingsApiResponse>(url);

    console.log('✅ User bookings fetched successfully:', {
      totalBookings: Array.isArray(response) ? response.length : 0,
      userId: userId
    });

    return response;
  } catch (error) {
    console.error('❌ Failed to fetch user bookings:', error);
    throw error;
  }
};



