// Base interfaces for common structures
interface Money {
  amount: number;
  currency: string;
}

interface Charge extends Money {
  description: string;
  frequency: string;
  type: string;
  unit: string;
}

interface AdditionalCharge {
  charge: Charge;
  text: string;
}

interface AdditionalInformation {
  text: string;
  type: string;
}

interface CreditCard {
  code: string;
  processingCountry?: string;
}

interface BoardBasis {
  description: string;
  type: string;
}

interface CancellationRule {
  end: string; // ISO datetime string
  estimatedValue: number;
  start: string; // ISO datetime string
  value: number;
  valueType: string;
}

interface CancellationPolicy {
  rules: CancellationRule[];
}

interface DailyRate {
  amount: number;
  date: string; // ISO datetime string
  discount: number;
  taxIncluded: boolean;
}

interface Occupancy {
  childAges: string[];
  numOfAdults: string;
  numOfChildren: string;
  roomId: string;
}

interface Offer {
  description: string;
  discountOffer: string;
  percentageDiscountOffer: string;
  title: string;
}

interface Policy {
  text: string;
  type: string;
}

interface Tax {
  amount: number;
  description: string;
  isIncludedInBaseRate: boolean;
}

interface TraderInformation {
  trader: any[]; // Based on the data, this appears to be an empty array
}

interface ConversionRate {
  conversionFactor: number;
  fromCurrency: string;
  toCurrency: string;
}

// Main rate interface
interface HotelRate {
  IsPANMandatory: boolean;
  IsPassportMandatory: boolean;
  additionalCharges: AdditionalCharge[];
  additionalInformation?: AdditionalInformation[]; // Optional - not always present
  allGuestsInfoRequired: boolean;
  allowedCreditCards?: CreditCard[]; // Optional - not always present
  availability: string;
  baseRate: number;
  boardBasis: BoardBasis;
  cancellationPolicies: CancellationPolicy[];
  cardRequired: boolean;
  conversionRate?: ConversionRate; // Optional - present in actual response
  currency: string;
  dailyRates: DailyRate[];
  depositRequired: boolean;
  deposits: any[]; // Based on the data, this appears to be an empty array
  distributionChannel: string;
  distributionType: string;
  guaranteeRequired: boolean;
  id: string;
  includes?: string[]; // Optional - not always present
  isChildConvertedToAdult: boolean;
  isContractedRate: boolean;
  isPackageRate?: boolean; // Optional - not always present
  minSellingRate: number;
  needsPriceCheck: boolean;
  occupancies: Occupancy[];
  offers: Offer[];
  onlineCancellable: boolean;
  payAtHotel: boolean;
  policies: Policy[];
  providerHotelId: string;
  providerId: string;
  providerName: string;
  publishedBaseRate: number;
  publishedRate: number;
  refundability: string;
  refundable: boolean;
  specialRequestSupported: boolean;
  taxes: Tax[];
  totalRate: number;
  traderInformation: TraderInformation;
  type: string;
}

// Room interfaces
interface Bed {
  count: string;
  type: string;
}

interface RoomFacility {
  name: string;
}

interface Room {
  beds: Bed[];
  description: string;
  facilities: RoomFacility[];
  id: string;
  maxGuestAllowed: string; // Present in actual response
  name: string;
  smokingAllowed: boolean;
  views: any[]; // Based on the data, this appears to be an empty array
}

// Hotel interface
interface Hotel {
  discounts: any[]; // Based on the data, this appears to be an empty array
  fees: any[]; // Based on the data, this appears to be an empty array
  hotelName?: string; // Optional - not always present in response
  id: string;
  onRequest: boolean;
  rates: HotelRate[];
  rooms: Room[];
  supplierRates: HotelRate[]; // Same structure as rates
}

// Main response interfaces
interface HotelBookingData {
  currency: string;
  hotel: Hotel;
  token: string;
  totalRate: number;
}

interface HotelBookingResponse {
  provider: string;
  data: HotelBookingData;
}

// Request payload interface
interface BookingInitRequest {
  search_key: string;
  hotel_id: string;
  recommendation_id: string;
}

export type {
  HotelBookingResponse,
  HotelBookingData,
  Hotel,
  HotelRate,
  Room,
  Bed,
  RoomFacility,
  Policy,
  Occupancy,
  CancellationPolicy,
  CancellationRule,
  AdditionalCharge,
  Charge,
  Money,
  CreditCard,
  BoardBasis,
  DailyRate,
  Offer,
  Tax,
  AdditionalInformation,
  TraderInformation,
  ConversionRate,
  BookingInitRequest
};
