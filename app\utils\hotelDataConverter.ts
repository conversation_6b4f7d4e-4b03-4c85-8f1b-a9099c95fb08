import {
    SearchInitApiResponse,
    Hotel
} from "@/app/HotelSearchResult/hotel-search-result.model";

/**
 * Utility functions for converting Search Init API response data to frontend Hotel model format
 */

/**
 * Check if the search is completed based on the API response
 * @param searchInitResponse - The API response from Search Init
 * @returns boolean indicating if search is completed
 */
export const isSearchCompleted = (searchInitResponse: SearchInitApiResponse): boolean => {
    return searchInitResponse.isCompleted;
};
