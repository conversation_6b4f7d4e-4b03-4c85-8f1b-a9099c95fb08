import { formatCurrency, formatCurrencyParts, formatSimpleCurrency } from '../currencyFormatter';

describe('currencyFormatter', () => {
  describe('formatCurrency', () => {
    it('should format INR currency correctly', () => {
      expect(formatCurrency(1234.56, 'INR')).toBe('₹1,234.56');
      expect(formatCurrency(1000, 'INR')).toBe('₹1,000.00');
    });

    it('should format USD currency correctly', () => {
      expect(formatCurrency(1234.56, 'USD')).toBe('$1,234.56');
      expect(formatCurrency(1000, 'USD')).toBe('$1,000.00');
    });

    it('should handle floating point precision issues', () => {
      // This is the exact issue from the screenshot: 6245.370000000001
      expect(formatCurrency(6245.370000000001, 'INR')).toBe('₹6,245.37');
      expect(formatCurrency(27127.49, 'INR')).toBe('₹27,127.49');
    });

    it('should handle invalid amounts', () => {
      expect(formatCurrency(NaN, 'INR')).toBe('0.00');
      expect(formatCurrency(null as any, 'INR')).toBe('0.00');
      expect(formatCurrency(undefined as any, 'INR')).toBe('0.00');
    });

    it('should handle "not available" currency', () => {
      expect(formatCurrency(1234.56, 'Currency not available')).toBe('1234.56');
    });

    it('should format other currencies correctly', () => {
      expect(formatCurrency(1234.56, 'EUR')).toBe('€1,234.56');
      expect(formatCurrency(1234.56, 'GBP')).toBe('£1,234.56');
      expect(formatCurrency(1234.56, 'AED')).toBe('AED 1,234.56');
    });
  });

  describe('formatCurrencyParts', () => {
    it('should split currency into parts correctly', () => {
      const result = formatCurrencyParts(1234.56, 'INR');
      expect(result).toEqual({
        symbol: '₹',
        mainAmount: '1,234',
        decimal: '56'
      });
    });

    it('should handle floating point precision issues', () => {
      const result = formatCurrencyParts(6245.370000000001, 'INR');
      expect(result).toEqual({
        symbol: '₹',
        mainAmount: '6,245',
        decimal: '37'
      });
    });

    it('should handle invalid amounts', () => {
      const result = formatCurrencyParts(NaN, 'INR');
      expect(result).toEqual({
        symbol: '₹',
        mainAmount: '0',
        decimal: '00'
      });
    });
  });

  describe('formatSimpleCurrency', () => {
    it('should format simple currency correctly', () => {
      expect(formatSimpleCurrency(1234.56, '₹')).toBe('₹1234.56');
      expect(formatSimpleCurrency(1000, '$')).toBe('$1000.00');
    });

    it('should handle floating point precision issues', () => {
      expect(formatSimpleCurrency(6245.370000000001, '₹')).toBe('₹6245.37');
    });

    it('should handle invalid amounts', () => {
      expect(formatSimpleCurrency(NaN, '₹')).toBe('₹0.00');
      expect(formatSimpleCurrency(null as any, '₹')).toBe('₹0.00');
      expect(formatSimpleCurrency(undefined as any, '₹')).toBe('₹0.00');
    });
  });

  describe('Profile page integration', () => {
    it('should format booking prices correctly for different currencies', () => {
      // Test cases for profile page booking display
      expect(formatCurrency(1234.567890123, 'USD')).toBe('$1,234.57');
      expect(formatCurrency(5678.999999999, 'EUR')).toBe('€5,679.00');
      expect(formatCurrency(9876.543210987, 'INR')).toBe('₹9,876.54');
      expect(formatCurrency(2468.135792468, 'AED')).toBe('AED 2,468.14');
    });

    it('should handle edge cases in booking price display', () => {
      expect(formatCurrency(0, 'USD')).toBe('$0.00');
      expect(formatCurrency(0.01, 'INR')).toBe('₹0.01');
      expect(formatCurrency(999999.999, 'EUR')).toBe('€1,000,000.00');
    });
  });
});
