export interface UrlLink {
  url: string;
}

export interface CustomerDetails {
  customer_id: string;
  customer_email: string;
  customer_phone: string;
}

export interface OrderMeta {
  return_url: string;
}

export interface OrderTags {
  booking_id: string;
}

export interface CashfreePaymentData {
  cf_order_id: number;
  created_at: string;
  customer_details: CustomerDetails;
  entity: string;
  order_amount: number;
  order_currency: string;
  order_expiry_time: string;
  order_id: string;
  order_meta: OrderMeta;
  order_note: string | null;
  order_splits: any[];
  order_status: string;
  order_tags: OrderTags;
  payment_session_id: string;
  payments: UrlLink;
  refunds: UrlLink;
  settlements: UrlLink;
}

export interface CreateBookingResponse {
  booking_reference: string;
  service_type: string;
  status: string;
  payment_status: string;
  payment_redirection_url: string;
  payment_provider: string;
  payment_provider_data: CashfreePaymentData;
}


// --- Helper Interfaces (Smallest, most nested parts) ---

interface GeoInfo {
  name: string;
  code: string;
}

interface Address {
  line1: string;
  line2: string;
  city: GeoInfo;
  state: GeoInfo;
  country: GeoInfo;
  postalCode: string;
}

interface Contact {
  phone: string;
  address: Address;
  email: string;
}

interface Guest {
  type: string;
  title: string;
  firstname: string;
  lastname: string;
  age: number;
  email: string;
}

export interface Tax {
  amount: number;
  description: string;
}

export interface CancellationRule {
  start: string;
  end: string;
  value: number;
  valueType: string;
}

export interface CancellationPolicy {
  rules: CancellationRule[];
}

interface Facility {
  name: string;
}

// --- Composed Interfaces ---

interface BillingContact {
  title: string;
  firstName: string;
  lastName: string;
  age: number;
  contact: Contact;
}

interface RoomAllocation {
  roomid: string;
  rateid: string;
  guests: Guest[];
}

interface Rate {
  id: string;
  baseRate: number;
  totalRate: number;
  currency: string;
  refundability: string;
  payAtHotel: boolean;
  taxes: Tax[];
  cancellationPolicies: CancellationPolicy[];
}

interface Room {
  id: string;
  name: string;
  description: string;
  facilities: Facility[];
}

interface BookingSnapshot {
  id: string;
  hotelName: string | null;
  rates: Rate[];
  rooms: Room[];
}

interface HotelBooking {
  id: number;
  master_booking_reference: string;
  provider_booking_id: string | null;
  booking_ref_id: string;
  hotel_id: string;
  search_key: string;
  rate_ids: string[];
  provider_response: any | null;
  booking_snapshot: BookingSnapshot;
  rooms_allocations: RoomAllocation[];
  billing_contact: BillingContact;
  credit_card_info: any | null;
  special_requests: any | null;
}

// --- Top-Level API Response Interface ---

export interface ApiBookingResponse {
  id: number;
  booking_reference: string;
  service_type: string;
  status: string;
  payment_status: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  hotel_booking: HotelBooking;
}

// Define interfaces for nested objects to keep the main interface clean
export interface Tax {
  amount: number;
  description: string;
}

export interface CancellationPolicy {
  rules: {
    start: string;
    end: string;
    value: number;
    valueType: string;
  }[];
}

export interface allGuests {
  adults: number;
  children: number;
}

export interface SimplifiedBillingContact {
  name: string;
  email: string;
  phone: string;
}


export interface SimplifiedHotelBooking {
  id: number;
  booking_reference: string;
  status: string;
  payment_status: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  hotel_id: string;
  search_key: string;
  hotelName: string | null;
  baseRate: number;
  totalRate: number;
  payAtHotel: boolean;
  taxes: Tax[];
  cancellationPolicies: CancellationPolicy[];
  roomId: string; // Using camelCase `roomId` as is standard in code
  roomName: string;
  description: string;
  checkInDate: string;
  checkOutDate: string;
  checkInTime: string;
  checkOutTime: string;
  guests: allGuests;
  nights: number;
  rooms_allocations: any[]; // Kept as 'any[]' for simplicity, but could be typed further
  billing_contact: SimplifiedBillingContact;
}

