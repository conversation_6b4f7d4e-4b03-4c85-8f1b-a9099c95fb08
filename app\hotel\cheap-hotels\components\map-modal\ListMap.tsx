import { Hotel } from '@/models/hotel/list-page.model';
import React from 'react'
import MapHotelList from './components/MapHotelList';
import FilterHotel from '../filter-hotel/FilterHotel';
import InteractiveMap from '@/app/components/map-box-map/InteractiveMap';
import { HotelFilterData } from '@/models/hotel/filter.model';

export interface ListMapProps {
  hotels: Hotel[];
  filterData: HotelFilterData | undefined;
  handleFilterChange: (filterData: HotelFilterData | undefined) => void;
}



function ListMap({ hotels , filterData , handleFilterChange }:ListMapProps) {

  return (
    <div className="w-full h-full flex flex-col md:flex-row min-h-screen bg-gray-100 p-4 gap-4">
        <div className="w-full md:w-1/3 flex flex-col gap-4 h-full">
            <div className="w-full h-auto bg-white rounded-lg shadow-md p-4">
                <FilterHotel filterData={filterData} handleChange={handleFilterChange} />
            </div>
            <div className="w-full flex-1 overflow-y-auto bg-white rounded-lg shadow-md">
                <MapHotelList hotels={hotels} isLoading={false} />
            </div>
        </div>
      
        <div className="w-full md:w-2/3 h-[50vh] md:h-full bg-white rounded-lg shadow-md">
            <InteractiveMap hotels={hotels} />
        </div>
    </div>
  )
}

export default ListMap