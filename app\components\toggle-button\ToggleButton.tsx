import React from 'react';
import { List, Grid3X3 } from 'lucide-react';
import { useTranslation } from '@/app/hooks/useTranslation';

interface ToggleButtonProps {
  viewMode: 'list' | 'grid';
  handleViewMode: (mode: 'list' | 'grid') => void;
}

const ToggleButton: React.FC<ToggleButtonProps> = ({ viewMode, handleViewMode, }) => {
    const { t } = useTranslation();
    
  return (
    <div className="flex items-center bg-gray-100 rounded-lg p-1">
      <button className={`
          flex items-center gap-2 px-3 py-2 text-sm font-medium transition-all duration-200 rounded-md
          ${viewMode === 'list'
            ? 'bg-white text-primary font-bold shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
          }
        `}
        onClick={() => handleViewMode('list')}
        type="button"
      >
        <List size={16} />
        {t("search.view_mode.list")}
      </button>
      
      <button
        className={`
          flex items-center gap-2 px-3 py-2 text-sm font-medium transition-all duration-200 rounded-md
          ${viewMode === 'grid'
            ? 'bg-white text-primary font-bold shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
          }
        `}
        onClick={() => handleViewMode('grid')}
        type="button"
      >
        <Grid3X3 size={16} />
        {t("search.view_mode.grid")}
      </button>
    </div>
  );
};

export default ToggleButton;