"use client";
import React from "react";
import "./CancellationPolicyShimmer.scss";

function CancellationPolicyShimmer() {
  return (
    <div className="cancellation-policy-shimmer">
      {/* Header shimmer */}
      <div className="cancellation-policy-shimmer__header"></div>

      {/* Description shimmer */}
      <div className="cancellation-policy-shimmer__description"></div>

      {/* Horizontal Timeline shimmer */}
      <div className="cancellation-policy-shimmer__timeline">
        {/* Timeline stages */}
        <div className="timeline-stage">
          <div className="stage-title"></div>
          <div className="stage-dot start"></div>
          <div className="stage-label"></div>
        </div>

        <div className="timeline-stage">
          <div className="stage-title"></div>
          <div className="stage-dot middle"></div>
          <div className="stage-info">
            <div className="stage-date"></div>
            <div className="stage-time"></div>
          </div>
        </div>

        <div className="timeline-stage">
          <div className="stage-title"></div>
          <div className="stage-dot middle"></div>
          <div className="stage-info">
            <div className="stage-date"></div>
            <div className="stage-time"></div>
          </div>
        </div>

        <div className="timeline-stage">
          <div className="stage-title"></div>
          <div className="stage-dot end"></div>
          <div className="stage-label"></div>
        </div>

        {/* Connecting line */}
        <div className="timeline-line"></div>
      </div>

      {/* Link shimmer */}
      <div className="cancellation-policy-shimmer__link"></div>
    </div>
  );
}

export default CancellationPolicyShimmer;
