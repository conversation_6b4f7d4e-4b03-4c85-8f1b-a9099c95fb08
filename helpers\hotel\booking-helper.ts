import { ApiBookingResponse, SimplifiedHotelBooking } from "@/models/hotel/booking.model";
import { HotelSearchData } from "@/models/hotel/search-page.model";

export function simplifyBookingData(
  originalBooking: ApiBookingResponse,
  searchData: HotelSearchData
): SimplifiedHotelBooking {
  const mainRate = originalBooking.hotel_booking.booking_snapshot.rates?.[0];
  const mainRoom = originalBooking.hotel_booking.booking_snapshot.rooms?.[0];
  const billing = originalBooking.hotel_booking.billing_contact;

  const guests = searchData.rooms.reduce(
    (acc, room) => {
      acc.adults += parseInt(room.adults, 10) || 0;
      acc.children += parseInt(room.children, 10) || 0;
      return acc;
    },
    { adults: 0, children: 0 }
  );

  const checkIn = new Date(searchData.checkIn);
  const checkOut = new Date(searchData.checkOut);
  const timeDiff = checkOut.getTime() - checkIn.getTime();
  const nights = Math.ceil(timeDiff / (1000 * 3600 * 24));

  return {
    id: originalBooking.id,
    booking_reference: originalBooking.booking_reference,
    status: originalBooking.status,
    payment_status: originalBooking.payment_status,
    user_id: originalBooking.user_id,
    created_at: originalBooking.created_at,
    updated_at: originalBooking.updated_at,
    hotel_id: originalBooking.hotel_booking.hotel_id,
    search_key: originalBooking.hotel_booking.search_key,
    hotelName: originalBooking.hotel_booking.booking_snapshot.hotelName,
    rooms_allocations: originalBooking.hotel_booking.rooms_allocations,
    billing_contact: {
      name: `${billing.firstName} ${billing.lastName}`,
      email: billing.contact.email,
      phone: billing.contact.phone,
    },
    baseRate: mainRate?.baseRate ?? 0,
    totalRate: mainRate?.totalRate ?? 0,
    payAtHotel: mainRate?.payAtHotel ?? false,
    taxes: mainRate?.taxes ?? [],
    cancellationPolicies: mainRate?.cancellationPolicies ?? [],
    roomId: mainRoom?.id ?? '',
    roomName: mainRoom?.name ?? '',
    description: mainRoom?.description ?? '',
    checkInDate: searchData.checkIn,
    checkOutDate: searchData.checkOut,
    nights: nights > 0 ? nights : 1,
    guests: guests,
    checkInTime: "14:00",
    checkOutTime: "12:00",
  };
}