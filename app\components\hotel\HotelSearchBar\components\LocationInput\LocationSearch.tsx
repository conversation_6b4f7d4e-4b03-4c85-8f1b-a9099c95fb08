import { useTranslation } from '@/app/hooks/useTranslation';
import { autoSuggestion } from '@/models/hotel/search-page.model';
import React, { useCallback, useEffect, useState } from 'react'
import LocationInputItem from './components/LocationInputItem';
import { LocationShimmerItem } from './components/LocationShimmerItem';
import { filterHotelLocationByName } from '@/helpers/hotel/search-page-helper';

export interface LocationSearchProps{
  searchQuery: string;
  handleSearchChange: (value: string) => void;
  handleClose: () => void;
  isLoading: boolean; 
  locationList: autoSuggestion[];
  recentSearches: autoSuggestion[];
  popularPlaces: autoSuggestion[];
  onLocationSelect: (location: autoSuggestion) => void;
  fetchCurrentLocation: (e: React.MouseEvent<HTMLElement>) => void;
}


function LocationSearch({isLoading , fetchCurrentLocation , searchQuery , handleClose , locationList , onLocationSelect , handleSearchChange , recentSearches , popularPlaces}:LocationSearchProps) {
  const { t } = useTranslation();
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  const [displayList,setDisplayList] = useState<autoSuggestion[]>(locationList);
  const [allAvailableItems, setAllAvailableItems] = useState<autoSuggestion[]>([]);
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);

  const handleSearchInputChange = useCallback((value: string) => {
    // Create a comprehensive list to filter from (recent searches + popular places)
    const allAvailableForFilter = [
      ...(recentSearches || []),
      ...(popularPlaces || []),
      ...(locationList || [])
    ];

    // Show immediate filtered results from available data
    if (value.trim()) {
      const filteredList = filterHotelLocationByName(allAvailableForFilter, value);
      setDisplayList(filteredList);
    } else {
      setDisplayList([]);
    }

    if(handleSearchChange){
      handleSearchChange(value);
    }
  }, [handleSearchChange, recentSearches, popularPlaces, locationList]);

  // Function to scroll selected item into view
  const scrollToSelectedItem = useCallback((index: number) => {
    if (index >= 0) {
      const selectedElement = document.getElementById(`suggestion-${index}`);
      if (selectedElement && scrollContainerRef.current) {
        const container = scrollContainerRef.current;
        const elementRect = selectedElement.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        // Check if element is above the visible area
        if (elementRect.top < containerRect.top) {
          selectedElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          });
        }
        // Check if element is below the visible area
        else if (elementRect.bottom > containerRect.bottom) {
          selectedElement.scrollIntoView({
            behavior: 'smooth',
            block: 'end',
            inline: 'nearest'
          });
        }
      }
    }
  }, []);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Get the current available items for keyboard navigation
      const currentItems = searchQuery ? displayList : allAvailableItems;
      // When not searching, we have "Current Location" as index 0, so items start from index 1
      const hasCurrentLocation = !searchQuery;
      const maxIndex = hasCurrentLocation ? currentItems.length : currentItems.length - 1;

      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedIndex(prev => (prev < maxIndex ? prev + 1 : prev));
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : -1));
      } else if (e.key === 'Enter') {
        e.preventDefault();
        if (selectedIndex === 0 && hasCurrentLocation) {
          // Handle "Current Location" selection
          fetchCurrentLocation(new MouseEvent('click') as any);
        } else if (selectedIndex > 0 && currentItems[selectedIndex - 1]) {
          // Handle regular location selection (adjust index for "Current Location")
          onLocationSelect(currentItems[selectedIndex - 1]);
        } else if (searchQuery && selectedIndex >= 0 && currentItems[selectedIndex]) {
          // Handle search results (no "Current Location" offset)
          onLocationSelect(currentItems[selectedIndex]);
        }
      } else if (e.key === 'Escape') {
        e.preventDefault();
        handleClose();
      }
  };


  // Build combined list of all available items for keyboard navigation when no search query
  useEffect(() => {
    if (!searchQuery) {
      const combinedItems: autoSuggestion[] = [];

      // Add recent searches
      if (recentSearches && recentSearches.length > 0) {
        combinedItems.push(...recentSearches);
      }

      // Add popular places
      if (popularPlaces && popularPlaces.length > 0) {
        combinedItems.push(...popularPlaces);
      }

      setAllAvailableItems(combinedItems);
      setSelectedIndex(-1); // Reset selection when switching between search and no-search
    } else {
      setSelectedIndex(-1); // Reset selection when starting to search
    }
  }, [searchQuery, recentSearches, popularPlaces])

  // Auto-scroll to selected item when selectedIndex changes
  useEffect(() => {
    if (selectedIndex >= 0) {
      scrollToSelectedItem(selectedIndex);
    }
  }, [selectedIndex, scrollToSelectedItem])

  return (
    <div className='w-full h-full flex flex-col'>
      {/* fixed search input header */}
      <div className="flex-shrink-0 bg-white sticky top-0 z-10 border-b border-gray-200">
        <div className='relative p-4'>
          <div className="absolute inset-y-0 left-0 pl-7 flex items-center pointer-events-none">
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            ) : (
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            )}
          </div>
          <input 
            type="text"
            placeholder={t("search.destination_placeholder")}
            value={searchQuery}
            onChange={(e) => handleSearchInputChange(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full h-10 pl-10 pr-10 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            autoFocus
            role="combobox"
            aria-controls="search-suggestion-list"
            aria-expanded={displayList.length > 0}
            aria-activedescendant={selectedIndex >= 0 ? `suggestion-${selectedIndex}` : undefined}
            aria-autocomplete="list"
          />
          {searchQuery && (
            <button onClick={handleClose} className="absolute inset-y-0 right-0 flex items-center pr-7">
              <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* content area */}
      <div ref={scrollContainerRef} className="flex-1 overflow-y-auto" role="listbox" aria-label="Location suggestions">
        {searchQuery ? (
            <>
              {/* Show internal filtered results immediately */}
              {locationList.length > 0 && (
                <LocationInputItem
                  header={t("common.search")}
                  items={locationList}
                  onSelect={onLocationSelect}
                  selectedIndex={selectedIndex}
                  startIndex={0}
                />
              )}

              {displayList.length > 0 && (
                <LocationInputItem
                  header={t("common.quickSearch")}
                  items={displayList}
                  onSelect={onLocationSelect}
                  selectedIndex={selectedIndex}
                  startIndex={0}
                />
              )}

              {/* Show loading indicator when API is fetching */}
              {isLoading && (
                <div className="py-1">
                  <div className="px-2 py-1 mb-1 bg-gray-100">
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-2"></div>
                      <p className="text-xs uppercase font-semibold text-gray-500 tracking-wider">Searching more results...</p>
                    </div>
                  </div>
                  <LocationShimmerItem />
                </div>
              )}

              {/* Show message when no results found */}
              {!isLoading && displayList.length === 0 && locationList.length === 0 && (
                <div className="py-4 px-4 text-center text-gray-500 text-sm">
                  No locations found for {`"${searchQuery}"`}
                </div>
              )}
            </>
        ):(
          <>
             {/* Current Location Option */}
            <div className='py-1'>
              <div
                id="suggestion-0"
                onClick={(e) => fetchCurrentLocation(e)}
                className={`flex items-center px-2 py-1.5 cursor-pointer transition-colors ${!searchQuery && selectedIndex === 0 ? 'bg-blue-50 border-l-2 border-blue-500' : 'hover:bg-gray-50'}`}
              >
                  <div className="w-5 h-5 flex items-center justify-center rounded-full text-white mr-1.5 bg-blue-500">
                    {isLoading ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                    ) : (
                      <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 616 0z" />
                      </svg>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {isLoading ? t("search.fetching_location") : t("search.current_location")}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {isLoading ? t("search.fetching_location") : t("search.detect_location_automatically")}
                    </p>
                  </div>                
              </div>
            </div>

            {/* Recent Searches Option */}
            {recentSearches && recentSearches.length > 0 && (
              <LocationInputItem
                header={t("common.recentSearches")}
                items={recentSearches}
                onSelect={onLocationSelect}
                selectedIndex={selectedIndex}
                startIndex={1} // Start from 1 because "Current Location" is at index 0
              />
            )}

            {/* Popular Places Option */}
            {popularPlaces && popularPlaces.length > 0 && (
              <LocationInputItem
                header={t("common.popularPlaces")}
                items={popularPlaces}
                onSelect={onLocationSelect}
                selectedIndex={selectedIndex}
                startIndex={1 + (recentSearches ? recentSearches.length : 0)} // Adjust for "Current Location" + recent searches
              />
            )}
          </>
        )}
      </div>

    </div>
  )
}

export default LocationSearch