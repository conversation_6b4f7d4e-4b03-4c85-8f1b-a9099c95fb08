import { HotelDetailApiResponse } from "@/app/HotelDetail/hotel-detail-api-response.model";

const HOTEL_DETAILS_STORAGE_KEY = 'hotelDetailsResponse';

/**
 * Store hotel details response in localStorage
 * @param hotelDetails - The hotel details to store
 * @returns boolean indicating success
 */
export const storeHotelDetails = (hotelDetails: HotelDetailApiResponse): boolean => {
  try {
    localStorage.setItem(HOTEL_DETAILS_STORAGE_KEY, JSON.stringify(hotelDetails));
    console.log('🏨 Hotel details stored in localStorage:', hotelDetails.name);
    return true;
  } catch (error) {
    console.error('Error storing hotel details in localStorage:', error);
    return false;
  }
};

/**
 * Retrieve hotel details response from localStorage
 * @returns HotelDetailApiResponse or null if not found
 */
export const getStoredHotelDetails = (): HotelDetailApiResponse | null => {
  try {
    const storedData = localStorage.getItem(HOTEL_DETAILS_STORAGE_KEY);
    if (!storedData) {
      return null;
    }
    const parsedData = JSON.parse(storedData) as HotelDetailApiResponse;
    console.log('🏨 Hotel details retrieved from localStorage:', parsedData.name);
    return parsedData;
  } catch (error) {
    console.error('Error retrieving hotel details from localStorage:', error);
    return null;
  }
};

/**
 * Clear hotel details from localStorage
 * @returns boolean indicating success
 */
export const clearStoredHotelDetails = (): boolean => {
  try {
    localStorage.removeItem(HOTEL_DETAILS_STORAGE_KEY);
    console.log('🏨 Hotel details cleared from localStorage');
    return true;
  } catch (error) {
    console.error('Error clearing hotel details from localStorage:', error);
    return false;
  }
};

/**
 * Check if hotel details exist in localStorage
 * @returns boolean indicating if data exists
 */
export const hasStoredHotelDetails = (): boolean => {
  try {
    return localStorage.getItem(HOTEL_DETAILS_STORAGE_KEY) !== null;
  } catch (error) {
    return false;
  }
};

/**
 * Clear all booking-related data from localStorage
 * This includes hotel details and selected room details
 * @returns boolean indicating success
 */
export const clearAllBookingData = (): boolean => {
  try {
    // Clear hotel details
    localStorage.removeItem(HOTEL_DETAILS_STORAGE_KEY);

    // Clear selected room details
    localStorage.removeItem('selectedRoomDetails');

    // Clear search-related data if needed
    localStorage.removeItem('searchKey');

    console.log('🧹 All booking data cleared from localStorage');
    return true;
  } catch (error) {
    console.error('Error clearing booking data from localStorage:', error);
    return false;
  }
};
