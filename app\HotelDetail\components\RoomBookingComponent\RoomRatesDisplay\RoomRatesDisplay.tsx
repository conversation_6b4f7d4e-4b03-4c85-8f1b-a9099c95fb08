import React, { useState, useMemo } from 'react';
import { GroupedRate, Tax, TransformedRoomsData } from '@/models/hotel/rooms-and-rates.model';
import { storeSelectedRoom, SelectedRoomData } from '@/utils/selectedRoomStorage';
import { useRouter } from 'next/navigation';
import RoomDetailsModal from '../RoomDetailsModal/RoomDetailsModal';
import { useCommonContext } from '@/app/contexts/commonContext';
import { getConvertedCurrency } from '@/helpers/hotel/currency-helper';

interface RoomRatesDisplayProps {
  transformedRoomsData: TransformedRoomsData;
}

// Filter state interface
interface FilterState {
  breakfast: boolean;
  fullBoard: boolean;
  halfBoard: boolean;
  transfer: boolean;
  cancellationAvailable: boolean;
}

// --- CONSTANTS ---
const INITIAL_ROOM_GROUPS_TO_SHOW = 2;
const INITIAL_RECOMMENDATIONS_TO_SHOW = 2;

function RoomRatesDisplay({ transformedRoomsData }: RoomRatesDisplayProps) {
  // Router for navigation
  const router = useRouter();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRoomData, setSelectedRoomData] = useState<GroupedRate | null>(null);
  const { selectedCurrency } = useCommonContext();
  // Filter and search state
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<FilterState>({
    breakfast: false,
    fullBoard: false,
    halfBoard: false,
    transfer: false,
    cancellationAvailable: false,
  });

  // Booking state for visual feedback
  const [bookingInProgress, setBookingInProgress] = useState<string | null>(null);

  // --- NEW STATE for "Show More/Less" functionality ---
  // State to control visibility of all room groups (outer list)
  const [showAllRoomGroups, setShowAllRoomGroups] = useState(false);
  // State to track which room group's recommendations are expanded (inner lists)
  const [expandedRecommendations, setExpandedRecommendations] = useState<{ [key: string]: boolean }>({});

  // --- NEW HANDLER for toggling recommendation visibility within a group ---
  const handleToggleRecommendations = (stdRoomId: string) => {
    setExpandedRecommendations(prev => ({
      ...prev,
      [stdRoomId]: !prev[stdRoomId], // Toggle the boolean value for the specific room ID
    }));
  };

  // Helper function to calculate taxes from API data
  const calculateTaxesFromAPI = (taxes: Tax[]) => {
    return taxes
      .filter(tax => !tax.isIncludedInBaseRate) // Only taxes not included in base rate
      .reduce((total, tax) => total + tax.amount, 0);
  };

  // Helper function to get tax breakdown for display
  const getTaxBreakdown = (taxes: Tax[]) => {
    return taxes
      .filter(tax => !tax.isIncludedInBaseRate)
      .map(tax => ({
        description: tax && tax.description && tax.description.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        amount: tax.amount
      }));
  };

  const handleRoomClick = (groupedRate: GroupedRate) => {
    setSelectedRoomData(groupedRate);
    setIsModalOpen(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedRoomData(null);
  };

  // Handle filter reset
  const handleResetFilters = () => {
    setSearchQuery('');
    setFilters({
      breakfast: false,
      fullBoard: false,
      halfBoard: false,
      transfer: false,
      cancellationAvailable: false,
    });
  };

  // Handle Book Now button click
  const handleBookNow = (groupedRate: GroupedRate, recommendation: any) => {
    const buttonId = `${recommendation.recommendationId}-${groupedRate.rate.id}`;
    setBookingInProgress(buttonId);

    // Structure the selected room data
    const selectedRoomData: SelectedRoomData = {
      // Room information
      room: {
        id: groupedRate.room.id,
        name: groupedRate.room.name,
        description: groupedRate.room.description,
        maxGuestAllowed: groupedRate.room.maxGuestAllowed,
        maxAdultAllowed: groupedRate.room.maxAdultAllowed,
        maxChildrenAllowed: groupedRate.room.maxChildrenAllowed,
        facilities: groupedRate.room.facilities,
        images: groupedRate.room.images,
        views: groupedRate.room.views,
        smokingAllowed: groupedRate.room.smokingAllowed,
      },
      // Rate information
      rate: {
        id: groupedRate.rate.id,
        baseRate: groupedRate.rate.baseRate,
        totalRate: groupedRate.rate.totalRate,
        publishedRate: groupedRate.rate.publishedRate,
        currency: groupedRate.rate.currency,
        refundable: groupedRate.rate.refundable,
        boardBasis: groupedRate.rate.boardBasis,
        occupancies: groupedRate.rate.occupancies,
        taxes: groupedRate.rate.taxes,
        policies: groupedRate.rate.policies,
        cancellationPolicies: groupedRate.rate.cancellationPolicies,
        includes: groupedRate.rate.includes,
        additionalCharges: groupedRate.rate.additionalCharges,
        providerId: groupedRate.rate.providerId,
        providerName: groupedRate.rate.providerName,
      },
      // Additional metadata
      recommendationId: recommendation.recommendationId,
      selectedAt: new Date().toISOString(),
      // Calculated values for easy access
      calculatedValues: {
        taxesAndFees: Math.round(calculateTaxesFromAPI(groupedRate.rate.taxes)),
        taxBreakdown: getTaxBreakdown(groupedRate.rate.taxes),
        savings: Math.round((groupedRate.rate.publishedRate || groupedRate.rate.baseRate * 1.2) - groupedRate.rate.totalRate),
        formattedTotalRate: Math.round(groupedRate.rate.totalRate).toLocaleString(),
      }
    };

    // Store using utility function
    const success = storeSelectedRoom(selectedRoomData);

    // Simulate a brief loading state for better UX
    setTimeout(() => {
      setBookingInProgress(null);

      if (success) {
        console.log('✅ Room selection stored successfully');
        // Navigate to BookingPreviewPage
        router.push('/BookingPreviewPage');
      } else {
        console.error('❌ Failed to store room selection');
        // You could show an error message to the user here
        alert('Failed to proceed with booking. Please try again.');
      }
    }, 800); // Brief delay to show the loading state
  };

  // Helper function to format text with proper capitalization
  const formatText = (text: string) => {
    if (!text) return '';

    return text
      .toLowerCase()
      .split(/[\s-_]+/) // Split on spaces, hyphens, underscores
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Search functionality
  const searchInText = (text: string, query: string): boolean => {
    if (!text || !query) return true;
    return text.toLowerCase().includes(query.toLowerCase());
  };

  const matchesSearch = (groupedRate: GroupedRate, query: string): boolean => {
    if (!query.trim()) return true;

    // Search in room name
    if (searchInText(groupedRate.room.name, query)) return true;

    // Search in board basis description
    if (searchInText(groupedRate.rate.boardBasis.description, query)) return true;

    // Search in room facilities
    const facilitiesMatch = groupedRate.room.facilities.some(facility =>
      searchInText(facility.name, query)
    );
    if (facilitiesMatch) return true;

    // Search in room description
    if (searchInText(groupedRate.room.description, query)) return true;

    return false;
  };

  // Filter functionality
  const matchesFilters = (groupedRate: GroupedRate, activeFilters: FilterState): boolean => {
    // If no filters are active, show all
    const hasActiveFilters = Object.values(activeFilters).some(filter => filter);
    if (!hasActiveFilters) return true;

    // Check breakfast filter
    if (activeFilters.breakfast) {
      const hasBreakfast = groupedRate.rate.boardBasis.type.toLowerCase().includes('breakfast') ||
        groupedRate.rate.boardBasis.description.toLowerCase().includes('breakfast');
      if (!hasBreakfast) return false;
    }

    // Check full board filter
    if (activeFilters.fullBoard) {
      const hasFullBoard = groupedRate.rate.boardBasis.type.toLowerCase().includes('fullboard') ||
        groupedRate.rate.boardBasis.type.toLowerCase().includes('full board') ||
        groupedRate.rate.boardBasis.description.toLowerCase().includes('full board');
      if (!hasFullBoard) return false;
    }

    // Check half board filter
    if (activeFilters.halfBoard) {
      const hasHalfBoard = groupedRate.rate.boardBasis.type.toLowerCase().includes('halfboard') ||
        groupedRate.rate.boardBasis.type.toLowerCase().includes('half board') ||
        groupedRate.rate.boardBasis.description.toLowerCase().includes('half board');
      if (!hasHalfBoard) return false;
    }

    // Check transfer filter (look in includes array or additional charges)
    if (activeFilters.transfer) {
      const hasTransfer = groupedRate.rate.includes?.some(include =>
        include.toLowerCase().includes('transfer') || include.toLowerCase().includes('transport')
      ) || groupedRate.rate.additionalCharges?.some(charge =>
        charge.charge.description.toLowerCase().includes('transfer') || charge.charge.description.toLowerCase().includes('transport')
      );
      if (!hasTransfer) return false;
    }

    // Check cancellation available filter
    if (activeFilters.cancellationAvailable) {
      if (!groupedRate.rate.refundable) return false;
    }

    return true;
  };

  // Apply filters and search to the data
  const filteredRoomsData = useMemo(() => {
    return transformedRoomsData.map(roomGroup => ({
      ...roomGroup,
      roomdata: roomGroup.roomdata.map(recommendation => ({
        ...recommendation,
        groupedRates: recommendation.groupedRates.filter(groupedRate => {
          // Apply search filter
          if (!matchesSearch(groupedRate, searchQuery)) return false;

          // Apply other filters
          if (!matchesFilters(groupedRate, filters)) return false;

          return true;
        })
      })).filter(recommendation => recommendation.groupedRates.length > 0) // Remove recommendations with no matching rates
    })).filter(roomGroup => roomGroup.roomdata.length > 0); // Remove room groups with no matching recommendations
  }, [transformedRoomsData, searchQuery, filters]);

  // --- NEW: Slice the room groups based on the showAllRoomGroups state ---
  const visibleRoomGroups = showAllRoomGroups
    ? filteredRoomsData
    : filteredRoomsData.slice(0, INITIAL_ROOM_GROUPS_TO_SHOW);

  return (
    <div className="max-w-6xl mx-auto p-4">
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-primary-color mb-4">Rooms & Rates</h2>

        {/* Filter Section */}
        <div className="flex flex-wrap items-center gap-4 mb-4">
          <span className="text-sm font-medium text-gray-700">Filter rooms by:</span>
          <div className="flex flex-wrap gap-3">
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-primary-color focus:ring-primary-color"
                checked={filters.breakfast}
                onChange={(e) => setFilters(prev => ({ ...prev, breakfast: e.target.checked }))}
              />
              <span>Breakfast</span>
            </label>
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-primary-color focus:ring-primary-color"
                checked={filters.fullBoard}
                onChange={(e) => setFilters(prev => ({ ...prev, fullBoard: e.target.checked }))}
              />
              <span>Full Board</span>
            </label>
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-primary-color focus:ring-primary-color"
                checked={filters.halfBoard}
                onChange={(e) => setFilters(prev => ({ ...prev, halfBoard: e.target.checked }))}
              />
              <span>Half Board</span>
            </label>
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-primary-color focus:ring-primary-color"
                checked={filters.transfer}
                onChange={(e) => setFilters(prev => ({ ...prev, transfer: e.target.checked }))}
              />
              <span>Transfer</span>
            </label>
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-primary-color focus:ring-primary-color"
                checked={filters.cancellationAvailable}
                onChange={(e) => setFilters(prev => ({ ...prev, cancellationAvailable: e.target.checked }))}
              />
              <span>Cancellation Available</span>
            </label>
          </div>

          {/* Reset Filters Button */}
          {(Object.values(filters).some(filter => filter) || searchQuery.trim()) && (
            <button
              onClick={handleResetFilters}
              className="text-sm text-primary-color hover:text-primary-color-dark font-medium underline"
            >
              Clear all filters
            </button>
          )}
        </div>

        {/* Search Bar */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search by room name, amenities, or meal plans..."
            className="w-full max-w-md px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-color focus:border-primary-color"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <div className="absolute right-3 top-2.5">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* Results Count */}
        <div className="text-sm text-gray-600 mt-4">
          {(() => {
            const totalRooms = filteredRoomsData.reduce((count, roomGroup) =>
              count + roomGroup.roomdata.reduce((roomCount, recommendation) =>
                roomCount + recommendation.groupedRates.length, 0), 0);
            return `Showing ${totalRooms} room option${totalRooms !== 1 ? 's' : ''}`;
          })()}
        </div>
      </div>

      {/* Room Groups */}
      <div className="space-y-8">
        {filteredRoomsData.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No rooms found</h3>
            <p className="text-gray-500 mb-4">
              No rooms match your current search and filter criteria.
            </p>
            <button
              onClick={handleResetFilters}
              className="bg-primary-color hover:bg-primary-color-dark text-white px-4 py-2 rounded-lg transition-colors"
            >
              Clear filters
            </button>
          </div>
        ) : (
          // --- UPDATED: Map over the 'visibleRoomGroups' instead of 'filteredRoomsData' ---
          visibleRoomGroups.map((roomGroup) => {
            // --- NEW: Check if the current group's recommendations are expanded ---
            const areRecommendationsExpanded = !!expandedRecommendations[roomGroup.stdRoomId];
            const visibleRecommendations = areRecommendationsExpanded
              ? roomGroup.roomdata
              : roomGroup.roomdata.slice(0, INITIAL_RECOMMENDATIONS_TO_SHOW);
            
            return (
              <div key={roomGroup.stdRoomId} className="bg-white rounded-lg shadow-sm border border-gray-200">
                {/* Room Group Header */}
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h3 className="text-xl font-semibold text-primary-color">{roomGroup.roomName}</h3>
                </div>

                {/* Room Options */}
                <div className="divide-y divide-gray-200">
                  {/* --- UPDATED: Map over 'visibleRecommendations' --- */}
                  {visibleRecommendations.map((recommendation) => {
                    const totalSumOfBaseRates = recommendation.groupedRates.reduce((sum, current) => sum + current.rate.baseRate, 0);
                    const bestRate = recommendation.groupedRates.reduce((best, current) => current.rate.totalRate < best.rate.totalRate ? current : best);
                    const totalOriginalSum = recommendation.groupedRates.reduce((sum, current) => {
                      const originalPrice = current.rate.dailyRates.reduce((dailySum, dailyRate) => dailySum + (dailyRate.amount + dailyRate.discount), 0);
                      return sum + originalPrice;
                    }, 0);
                    const hasRealDiscounts = recommendation.groupedRates.some(rate => rate.rate.dailyRates.some(dailyRate => dailyRate.discount > 0));
                    const totalTaxesSum = recommendation.groupedRates.reduce((sum, current) => sum + calculateTaxesFromAPI(current.rate.taxes), 0);
                    
                    return (
                      <div key={recommendation.recommendationId} className="p-6">
                        <div className="flex flex-col lg:flex-row border border-gray-200 rounded-lg overflow-hidden">
                          {/* Left Side - Rate Options */}
                          <div className="flex-1">
                            <div className="divide-y divide-gray-200">
                              {recommendation.groupedRates.map((groupedRate, index) => (
                                <div
                                  key={`${recommendation.recommendationId}-${index}`}
                                  className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                                  onClick={() => handleRoomClick(groupedRate)}
                                >
                                  <div className="flex flex-col sm:flex-row gap-4">
                                    {/* Room Image */}
                                    <div className="sm:w-32 h-24 sm:h-auto flex-shrink-0">
                                      {groupedRate.room.images && groupedRate.room.images[0]?.links && groupedRate.room.images[0].links[0] ? (
                                        <img
                                          src={groupedRate.room.images[0].links[0].url}
                                          alt={groupedRate.room.name}
                                          className="w-full h-full object-cover rounded-lg"
                                        />
                                      ) : (
                                        <div className="w-full h-full bg-gray-200 flex items-center justify-center rounded-lg">
                                          <span className="text-gray-400 text-xs">No Image</span>
                                        </div>
                                      )}
                                    </div>
                                    {/* Room Details */}
                                    <div className="flex-1 relative">
                                      <div className="mb-2">
                                        <h4 className="font-semibold text-primary-color text-sm">
                                          {groupedRate.room.name}
                                          {recommendation.groupedRates.length === 1 && groupedRate.rate.occupancies && groupedRate.rate.occupancies.length > 0 && (
                                            <span className="font-normal">
                                              {' x '}
                                              {groupedRate.rate.occupancies.length}
                                            </span>
                                          )}
                                        </h4>
                                      </div>
                                      {groupedRate.rate.occupancies && groupedRate.rate.occupancies.length > 0 && (
                                        <div className="mb-2">
                                          <div className="flex flex-wrap gap-2">
                                            {groupedRate.rate.occupancies.map((occupancy, occIndex) => (
                                              <div key={occIndex} className="flex items-center gap-1 text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded-full">
                                                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" /></svg>
                                                <span>{occupancy.numOfAdults} Adult{parseInt(occupancy.numOfAdults) !== 1 ? 's' : ''}</span>
                                                {parseInt(occupancy.numOfChildren) > 0 && (
                                                  <>
                                                    <span className="text-blue-400">•</span>
                                                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 2a3 3 0 100 6 3 3 0 000-6zM4 18a6 6 0 1112 0H4z" clipRule="evenodd" /></svg>
                                                    <span>{occupancy.numOfChildren} Child{parseInt(occupancy.numOfChildren) !== 1 ? 'ren' : ''}</span>
                                                  </>
                                                )}
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      )}
                                      <div className="absolute top-0 right-0 flex flex-wrap gap-1">
                                        {groupedRate.rate.refundable && <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">Refundable</span>}
                                        {groupedRate.rate.totalRate < (groupedRate.rate.publishedRate || groupedRate.rate.baseRate * 1.2) && <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded">Best Deal</span>}
                                      </div>
                                      <div className="space-y-1 mb-2">
                                        <div className="flex items-center gap-2 text-xs text-gray-600"><svg className="w-3 h-3 text-secondary-color" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /></svg><span>{formatText(groupedRate.rate.boardBasis.description)}</span></div>
                                        {groupedRate.room.facilities.slice(0, 2).map((facility, idx) => (
                                          <div key={idx} className="flex items-center gap-2 text-xs text-gray-600"><svg className="w-3 h-3 text-secondary-color" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /></svg><span>{formatText(facility.name)}</span></div>
                                        ))}
                                      </div>
                                      <div className="text-xs text-secondary-color">{groupedRate.rate.refundable ? 'Free cancellation available' : 'Cancellation policy applies'}</div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                          {/* Right Side - Common Pricing Section */}
                          <div className="lg:w-64 bg-gray-50 p-6 border-t lg:border-t-0 lg:border-l border-gray-200">
                            <div className="sticky top-4">
                              <div className="text-center space-y-3">
                                <div className="text-sm font-medium text-gray-600">Total for {recommendation.groupedRates.length} option{recommendation.groupedRates.length !== 1 ? 's' : ''}</div>
                                {hasRealDiscounts && <div className="text-sm text-gray-500 line-through">{getConvertedCurrency(totalOriginalSum, selectedCurrency)}</div>}
                                <div className="text-3xl font-bold text-primary-color">{getConvertedCurrency(totalSumOfBaseRates, selectedCurrency)}</div>
                                <div className="text-xs text-gray-500">+ {getConvertedCurrency(totalTaxesSum, selectedCurrency)} Tax and Fees</div>
                                {hasRealDiscounts && <div className="text-xs text-green-600 font-medium">You save {getConvertedCurrency((totalOriginalSum - totalSumOfBaseRates), selectedCurrency)}</div>}
                                <button
                                  className="w-full bg-primary-color hover:bg-primary-color-dark text-white font-semibold py-3 px-4 rounded-lg transition-colors mt-4 disabled:opacity-50 disabled:cursor-not-allowed"
                                  onClick={() => handleBookNow(bestRate, recommendation)}
                                  disabled={bookingInProgress === `${recommendation.recommendationId}-${bestRate.rate.id}`}
                                >
                                  {bookingInProgress === `${recommendation.recommendationId}-${bestRate.rate.id}` ? (
                                    <div className="flex items-center justify-center gap-2"><div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div><span>Booking...</span></div>
                                  ) : ('Book Now')}
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {/* --- NEW: "Show More/Less" button for recommendations --- */}
                  {roomGroup.roomdata.length > INITIAL_RECOMMENDATIONS_TO_SHOW && (
                    <div className="text-center p-4">
                      <button
                        onClick={() => handleToggleRecommendations(roomGroup.stdRoomId)}
                        className="text-sm font-semibold text-primary-color hover:underline focus:outline-none"
                      >
                        {areRecommendationsExpanded
                          ? 'Show Less Options'
                          : `Show ${roomGroup.roomdata.length - INITIAL_RECOMMENDATIONS_TO_SHOW} More Options`}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* --- NEW: "Show More/Less" button for the main list of room groups --- */}
      {filteredRoomsData.length > INITIAL_ROOM_GROUPS_TO_SHOW && (
        <div className="mt-8 text-center">
          <button
            onClick={() => setShowAllRoomGroups(prev => !prev)}
            className="bg-white hover:bg-gray-100 text-primary-color font-semibold py-2 px-6 border border-primary-color rounded-lg shadow-sm transition-colors"
          >
            {showAllRoomGroups
              ? 'Show Less Room Types'
              : `Show ${filteredRoomsData.length - INITIAL_ROOM_GROUPS_TO_SHOW} More Room Types`}
          </button>
        </div>
      )}

      {/* Room Details Modal */}
      <RoomDetailsModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        roomData={selectedRoomData}
      />
    </div>
  );
}

export default RoomRatesDisplay;