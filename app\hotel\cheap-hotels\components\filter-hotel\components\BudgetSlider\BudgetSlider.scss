@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

.budgetSlider {
  padding: 12px 16px 8px 13px;
  border-radius: 8px;
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  h3 {
    font-size: 14px;
    font-weight: bold;
    color: #000;
    margin-bottom: 12px;
  }

  p {
    font-size: 14px;
    color: #333;
    margin-bottom: 15px;
  }

  .sliderContainer {
    position: relative;
    height: 50px;
    padding-top: 5px;
    top: 14px;

    //     .histogramBackground {
    //       position: absolute;
    //       top: -32px;
    //       left: 0;
    //       right: 0;
    //       height: 100%;
    //       background: linear-gradient(
    //         to right,
    //         rgba(100, 100, 100, 0.25) 0%,
    //         rgba(70, 70, 70, 0.15) 100%
    //       );
    //       pointer-events: none;
    //       mask-image: url("data:image/svg+xml;utf8,\
    // <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 120 40'>\
    // <path d='\
    // M-23 40V36h3V40\
    // M-21 40V34h3V40\
    // M-19 40V34h3V40\
    // M-17 40V36h3V40\
    // M-15 40V34h3V40\
    // M-13 40V34h3V40\
    // M-12 40V34h3V40\
    // M-11 40V34h3V40\
    // M-9 40V34h3V40\
    // M-7 40V30h3V40\
    // M-5 40V32h3V40\
    // M-3 40V30h3V40\
    // M-2 40V28h3V40\
    // M-1 40V28h3V40\
    // M0 40V8h3V40\
    // M3 40V22h3V40\
    // M6 40V35h3V40\
    // M9 40V12h3V40\
    // M12 40V28h3V40\
    // M15 40V5h3V40\
    // M18 40V18h3V40\
    // M21 40V31h3V40\
    // M24 40V14h3V40\
    // M27 40V25h3V40\
    // M30 40V9h3V40\
    // M33 40V37h3V40\
    // M36 40V19h3V40\
    // M39 40V26h3V40\
    // M42 40V13h3V40\
    // M45 40V22h3V40\
    // M48 40V30h3V40\
    // M51 40V18h3V40\
    // M54 40V35h3V40\
    // M57 40V10h3V40\
    // M60 40V28h3V40\
    // M63 40V17h3V40\
    // M66 40V31h3V40\
    // M69 40V8h3V40\
    // M72 40V26h3V40\
    // M75 40V19h3V40\
    // M78 40V12h3V40\
    // M81 40V30h3V40\
    // M84 40V22h3V40\
    // M87 40V5h3V40\
    // M90 40V35h3V40\
    // M93 40V14h3V40\
    // M96 40V25h3V40\
    // M99 40V18h3V40\
    // M102 40V31h3V40\
    // M105 40V13h3V40\
    // M108 40V28h3V40\
    // M111 40V9h3V40\
    // M114 40V21h3V40\
    // M116 40V11h3V40\
    // M119 40V16h3V40\
    // M121 40V26h3V40\
    // M123 40V28h3V40\
    // M126 40V30h3V40\
    // M128 40V30h3V40\
    // M130 40V34h3V40\
    // M132 40V34h3V40\
    // M134 40V34h3V40\
    // M136 40V34h3V40\
    // M138 40V34h3V40\
    // M140 40V34h3V40\
    // M142 40V34h3V40\
    // M1 40V16h3V40' fill='%23000'/>\
    // </svg>");
    //       mask-size: 100% 100%;
    //       mask-repeat: no-repeat;
    //     }

    .histogram {
      position: absolute;
      top: -21px;
      left: 0;
      right: 0;
      height: 40px;
      width: 100%;
      pointer-events: none;
    }

    .sliderTrack {
      position: absolute;
      top: 15px;
      left: 0;
      right: 0;
      height: 4px;
      background: #e0e0e0;
      border-radius: 2px;
      z-index: z-index(auto);
    }

    .sliderProgress {
      position: absolute;
      top: 15px;
      height: 4px;
      background: #0066ff;
      border-radius: 2px;
      z-index: z-index(base);
    }
  }

  /* Base slider styles */
  .slider {
    -webkit-appearance: none;
    appearance: none;
    position: absolute;
    top: 7px;
    width: 100%;
    height: 4px;
    background: transparent;
    border-radius: 2px;
    outline: none;
    margin: 0;
    padding: 0;
    /* Critical fix for Firefox */
    pointer-events: none;
  }

  /* Fixing pointer events for all browsers */
  .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #0066ff;
    cursor: grab;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    pointer-events: auto;
    z-index: z-index(base)+1;
  }

  .slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #0066ff;
    cursor: grab;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    pointer-events: auto;
    z-index: z-index(base)+1;
  }

  /* Track appearance */
  .slider::-webkit-slider-runnable-track {
    width: 100%;
    height: 4px;
    cursor: pointer;
    background: transparent;
    border-radius: 2px;
  }

  .slider::-moz-range-track {
    width: 100%;
    height: 4px;
    cursor: pointer;
    background: transparent;
    border-radius: 2px;
  }

  /* Specific slider styles */
  .minSlider {
    z-index: z-index(base)+1; /* Higher z-index for min slider */
    pointer-events: auto; /* Enable pointer events only on thumb */
  }

  .maxSlider {
    z-index: z-index(base);
    pointer-events: auto; /* Enable pointer events only on thumb */
  }

  /* Active state styles */
  .slider:active::-webkit-slider-thumb {
    cursor: grabbing;
    transform: scale(1.1);
  }

  .slider:active::-moz-range-thumb {
    cursor: grabbing;
    transform: scale(1.1);
  }
}
