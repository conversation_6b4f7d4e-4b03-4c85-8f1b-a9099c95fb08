"use client";
import React, { Dispatch, SetStateAction, useState } from "react";
import "./OverviewComponent.scss";
import { useTranslation } from "@/app/hooks/useTranslation";
import { AboutSection, AmenityDetails } from "@/app/HotelDetail/hotel-detail-result.model";
import { Facility, HotelDetailApiResponse } from "@/app/HotelDetail/hotel-detail-api-response.model";

  type OverviewComponentProps = {
    scrollToFacilities: () => void;
    setIsMobileFacilitiesActive: Dispatch<SetStateAction<boolean>>;
    address?: string;
    facilities?: Facility[]; // Backend facilities
    amenities?: string[]; // Backend amenities
    hotelData?: HotelDetailApiResponse; // Full hotel data
    // Legacy props (deprecated - kept for backward compatibility)
    amenityDetails?: AmenityDetails;
    aboutSection?: AboutSection;
    ratingView?: any;
    userRatingCategory?: string;
  };

function OverviewComponent({
  scrollToFacilities,
  setIsMobileFacilitiesActive,
  address,
  facilities,
  amenities,
  hotelData,
  // Legacy props (for backward compatibility)
  amenityDetails,
  aboutSection,
  ratingView,
  userRatingCategory
} : OverviewComponentProps) {
  const { t } = useTranslation();
  const [isDescExpanded, setIsDescExapanded] = useState<boolean>(false);
  const [isMobileView, setIsMobileView] = useState<boolean>(
    typeof window !== "undefined" ? window.innerWidth <= 950 : false
  );

  // Handle window resize
  React.useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth <= 950);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Helper function to calculate average rating from reviews
  const calculateAverageRating = (reviews: any[]) => {
    if (!reviews || reviews.length === 0) return 0;
    const sum = reviews.reduce((acc: number, review: any) => acc + review.rating, 0);
    return (sum / reviews.length).toFixed(1);
  };

  // Helper function to get rating category based on score
  const getRatingCategory = (rating: number) => {
    if (rating >= 4.5) return "Excellent";
    if (rating >= 4.0) return "Very Good";
    if (rating >= 3.5) return "Good";
    if (rating >= 3.0) return "Fair";
    return "Poor";
  };

  // Get enhanced about section data from backend (with legacy fallback)
  const overviewDescription = hotelData?.descriptions?.find(desc => desc.description_type === "overview");
  const fullText = overviewDescription?.content?.description ||
                   aboutSection?.descriptionInfo?.[0].description[0] ||
                   "No description available";
  const highlights = overviewDescription?.content?.highlights || [];

  // Check if text needs truncation (only show Read More if text is longer than 297 characters)
  const TRUNCATION_LIMIT = 297;
  const shouldShowReadMore = fullText.length > TRUNCATION_LIMIT;

  // Calculate live ratings from backend reviews (with legacy fallback)
  const liveAverageRating = calculateAverageRating(hotelData?.reviews || []);
  const liveReviewCount = hotelData?.reviews?.length || 0;
  const liveRatingCategory = getRatingCategory(parseFloat(liveAverageRating.toString()));

  // Enhanced location data (with legacy fallback)
  const fullLocation = hotelData ?
    `${hotelData.address}, ${hotelData.city}, ${hotelData.country}` :
    (address || "Location not available");

  // Create icon mapping for facilities
  const getIconForFacility = (facilityName: string) => {
    const iconMap: { [key: string]: string } = {
      "Swimming Pool": "fa-swimming-pool",
      "Fitness Center": "fa-dumbbell",
      "Spa & Wellness": "fa-spa",
      "Dining": "fa-utensils",
      "Business Services": "fa-briefcase",
      "Free WiFi": "fa-wifi",
      "Restaurant": "fa-utensils",
      "Business Center": "fa-building",
      "Spa": "fa-spa",
      "Pool": "fa-swimming-pool"
    };

    return iconMap[facilityName] || "fa-star";
  };

  // Use backend facilities data (preferred) or fallback to amenityDetails or simple amenities
  const facilitiesData = facilities || amenityDetails?.amenities || amenities || [];
  const slicedfacilities = facilitiesData.slice(0, 6);

  

  return (
    <div className="overview-container">
      <div className="about-section">
        <div className="overview-header">{t('hotel.detail.about')}</div>

        <div className="overview-description">
          {
            isDescExpanded || !shouldShowReadMore
              ? fullText
              : `${fullText.substring(0, TRUNCATION_LIMIT)}...`
          }
        </div>

        {shouldShowReadMore && (
          <div className="overviewBtn" onClick={() => setIsDescExapanded(!isDescExpanded)}>
            {
              isDescExpanded ? t('hotel.detail.readLess') : t('hotel.detail.readMore')
            }
           {
            isDescExpanded ? ( <span className="material-icons">keyboard_arrow_up</span>) : ( <span className="material-icons">keyboard_arrow_down</span>)
           }
          </div>
        )}
      </div>

      {/* Hotel Highlights Section */}
      {highlights.length > 0 && (
        <div className="highlights-section" style={{ marginBottom: '20px' }}>
          <div className="overview-header">Hotel Highlights</div>
          <div className="highlights-list" style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '10px',
            marginTop: '10px'
          }}>
            {highlights.map((highlight, index) => (
              <div className="highlight-item" key={index} style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 12px',
                backgroundColor: '#f0f9ff',
                borderRadius: '20px',
                fontSize: '14px',
                color: '#0369a1'
              }}>
                <i className="fa-solid fa-check-circle" style={{ color: '#059669' }}></i>
                <span>{highlight}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Rating and Location Section - Only show on mobile after about-section */}
      {isMobileView && (
        <div className="mobile-rating-location-section">
          <div className="mobile-review">
            <div className="rating">
              {liveAverageRating || ratingView?.averageRating || "N/A"}
            </div>
            <div className="rating-detail">
              <p className="detail detail1">
                {liveRatingCategory || userRatingCategory || "No rating"}
              </p>
              <p className="detail detail2">
                {liveReviewCount || ratingView?.ratingCount || 0} {t("hotel.detail.ratings")}
              </p>
            </div>
          </div>

          <div className="mobile-location">
            <div className="icon">
              <i className="fa-solid fa-location-dot"></i>
            </div>
            <div className="details">
              <div className="detail detail1">
                {fullLocation}
              </div>
              <div className="detail detail2">
                {t("hotel.detail.viewOnMap")}
              </div>
            </div>
          </div>
        </div>
      )}

      {facilitiesData.length > 0 && (
        <div className="facilities-section">
          <div className="overview-header">
              Popular Facilities
          </div>

        <div className="popular-facilties">
            {
                slicedfacilities.map((facility, index) => {
                  // Handle both Facility objects and simple strings
                  const facilityName = typeof facility === 'string' ? facility : facility.name;
                  const iconClass = getIconForFacility(facilityName);

                  return (
                    <div className="facility" key={index}>
                      <div className="icon">
                        <i className={`fa-solid ${iconClass}`}></i>
                      </div>
                      <div className="label">
                        {facilityName}
                      </div>
                    </div>
                  );
                })
            }
        </div>

        {/* Only show the overview button if there are more than 6 facilities */}
        {facilitiesData.length > 6 && (
          <div className="overviewBtn" onClick={() => {
            if (window.innerWidth <= 950) {
              setIsMobileFacilitiesActive(true);
            } else {
              scrollToFacilities();
            }
          }}>
            {`View ${facilitiesData.length - slicedfacilities.length}+ more`} <span className="material-icons">keyboard_arrow_down</span>
          </div>
        )}





        </div>
      )}

    </div>
  );
}

export default OverviewComponent;
