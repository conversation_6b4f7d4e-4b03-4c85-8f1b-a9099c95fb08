// Utility functions for managing selected room data in localStorage

export interface SelectedRoomData {
  room: {
    id: string;
    name: string;
    description: string;
    maxGuestAllowed: string;
    maxAdultAllowed: string;
    maxChildrenAllowed: string;
    facilities: Array<{ name: string }>;
    images: Array<{ links: Array<{ url: string }> | null }>;
    views: string[];
    smokingAllowed: boolean;
  };
  rate: {
    id: string;
    baseRate: number;
    totalRate: number;
    publishedRate: number;
    currency: string;
    refundable: boolean;
    boardBasis: {
      description: string;
      type: string;
    };
    occupancies: Array<{
      roomId: string;
      stdRoomId: string;
      numOfAdults: string;
      numOfChildren: string;
    }>;
    taxes: Array<{
      amount: number;
      description: string;
      isIncludedInBaseRate: boolean;
    }>;
    policies: Array<{
      text: string;
      type: string;
    }>;
    cancellationPolicies: Array<{
      rules: Array<{
        end: string;
        estimatedValue: number;
        start: string;
        value: number;
        valueType: string;
      }>;
    }>;
    includes: string[] | null;
    additionalCharges: Array<{
      charge: {
        amount: number;
        currency: string;
        description: string;
        frequency: string;
        type: string;
        unit: string;
      };
      text: string;
    }>;
    providerId: string;
    providerName: string;
  };
  recommendationId: string;
  selectedAt: string;
  calculatedValues: {
    taxesAndFees: number;
    taxBreakdown: Array<{
      description: string;
      amount: number;
    }>;
    savings: number;
    formattedTotalRate: string;
  };
}

const STORAGE_KEY = 'selectedRoomDetails';

/**
 * Store selected room details in localStorage
 */
export const storeSelectedRoom = (roomData: SelectedRoomData): boolean => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(roomData));
    console.log('🏨 Selected room stored in localStorage:', roomData);
    return true;
  } catch (error) {
    console.error('Error storing selected room details:', error);
    return false;
  }
};

/**
 * Retrieve selected room details from localStorage
 */
export const getSelectedRoom = (): SelectedRoomData | null => {
  try {
    const storedData = localStorage.getItem(STORAGE_KEY);
    if (!storedData) {
      return null;
    }
    return JSON.parse(storedData) as SelectedRoomData;
  } catch (error) {
    console.error('Error retrieving selected room details:', error);
    return null;
  }
};

/**
 * Clear selected room details from localStorage
 */
export const clearSelectedRoom = (): boolean => {
  try {
    localStorage.removeItem(STORAGE_KEY);
    console.log('🏨 Selected room cleared from localStorage');
    return true;
  } catch (error) {
    console.error('Error clearing selected room details:', error);
    return false;
  }
};

/**
 * Check if there's a selected room in localStorage
 */
export const hasSelectedRoom = (): boolean => {
  try {
    return localStorage.getItem(STORAGE_KEY) !== null;
  } catch (error) {
    console.error('Error checking for selected room:', error);
    return false;
  }
};

/**
 * Get formatted room summary for display
 */
export const getSelectedRoomSummary = (): string | null => {
  const roomData = getSelectedRoom();
  if (!roomData) return null;

  const { room, rate, calculatedValues } = roomData;
  return `${room.name} - ${rate.boardBasis.description} - ₹${calculatedValues.formattedTotalRate}`;
};
