import { BookingInitRequest, HotelBookingResponse } from "@/app/BookingPreviewPage/booking-init.model";
import apiService from "../api-service";

/**
 * Service for initializing hotel booking session
 * This API call is typically made before the payment process to:
 * - Secure a booking token
 * - Validate rate availability
 * - Initialize the booking session
 */
export const initializeHotelBooking = async (
  searchKey: string,
  hotelId: string,
  recommendationId: string
): Promise<HotelBookingResponse> => {
  const payload: BookingInitRequest = {
    search_key: searchKey,
    hotel_id: hotelId,
    recommendation_id: recommendationId,
  };

  try {
    const response = await apiService.post<HotelBookingResponse>(
      "provider/rooms",
      payload
    );

    // Log successful booking initialization
    console.log('✅ Booking initialization service - API response received:', {
      provider: response.provider,
      hotelName: response.data.hotel.hotelName,
      token: response.data.token,
      totalRate: response.data.totalRate,
      currency: response.data.currency
    });

    return response;
  } catch (error) {
    console.error('❌ Booking initialization service - API call failed:', error);
    throw error;
  }
};


export const getPaymentDetails = async ()  => {
  const body = {
    "booking_reference": "TEST_ORDER_546767",
    "amount": 10.50,
    "currency": "INR",
    "customer_email": "<EMAIL>",
    "return_url": "http://localhost:3000/BookingConfirmation"
}
  try {
    const response = await apiService.post2<any>("/api/v1/payment/cashfree/initiate",body);
    return response;
  } catch (error) {
    console.error('❌ Payment details service - API call failed:', error);
    throw error;
  }
}