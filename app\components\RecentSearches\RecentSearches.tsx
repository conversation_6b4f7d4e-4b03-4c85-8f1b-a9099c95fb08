"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { HotelSearchFormData } from "@/models/hotel/search-page.model";
import { getRecentSearches } from "@/helpers/hotel/search-page-helper";
import { selectLocationAPi } from "@/api/hotel/search-page-service";
import { useCommonContext } from "@/app/contexts/commonContext";
import { formatDateForDisplay } from "../utilities/date-util";

interface RecentSearchDisplay {
  id: string;
  destination: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  rooms: number;
  children?: number;
}

function RecentSearches() {
  const [recentSearches, setRecentSearches] = useState<RecentSearchDisplay[]>([]);
  const router = useRouter();
  const { setHotelSearchFormData } = useCommonContext();
  // const { t } = useTranslation();



  // Convert HotelSearchFormData to display format
const convertToDisplayFormat = useCallback(
  (searches: HotelSearchFormData[] = []): RecentSearchDisplay[] => {
    if (!Array.isArray(searches) || searches.length === 0) return [];

    return searches
      .filter((search): search is HotelSearchFormData => Boolean(search && search.locationId))
      .map((search, index) => ({
        id: search.locationId || `search_${index}`,
        destination: search.fullLocationData || search.searchQuery || "",
        checkIn: search.checkInDate ? formatDateForDisplay(search.checkInDate) : "",
        checkOut: search.checkOutDate ? formatDateForDisplay(search.checkOutDate) : "",
        guests: search.travelers?.adults ?? 0,
        rooms: search.travelers?.rooms ?? 0,
        children: search.travelers?.children ?? 0,
      }));
  },
  []
);




  // Load recent searches using the helper function
  useEffect(() => {
    const searches = getRecentSearches();
    const displaySearches = convertToDisplayFormat(searches);
    setRecentSearches(displaySearches);
  }, [convertToDisplayFormat]);

  const handleSearchClick = async (search: RecentSearchDisplay) => {
    console.log("Clicked recent search:", search);

    // Find the original search data from getRecentSearches
    const originalSearches = getRecentSearches();
    const originalSearch = originalSearches.find(s =>
      (s.locationId && s.locationId === search.id) ||
      (s.fullLocationData || s.searchQuery) === search.destination
    );

    if (originalSearch) {
      // Use the original search data to preserve all details
      const searchFormData: HotelSearchFormData = {
        ...originalSearch,
        // Ensure we have the required fields
        searchQuery: originalSearch.searchQuery || search.destination,
        fullLocationData: originalSearch.fullLocationData || search.destination,
      };

      // Store the search data in sessionStorage and localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem("hotelSearchFormData", JSON.stringify(searchFormData));
        localStorage.setItem("hotelSearchFormData", JSON.stringify(searchFormData));
      }

      // Set the context data so the search bar gets populated immediately
      setHotelSearchFormData(searchFormData);

      // Don't set loading state here - let the search results page handle it
      // setIsLoading(true);

      // Call selectLocationApi if we have a locationId to properly set up the location
      if (originalSearch.locationId) {
        try {
          await selectLocationAPi(originalSearch.locationId);
          console.log("Location API called successfully for:", originalSearch.locationId);
        } catch (error) {
          console.error("Error calling selectLocationApi:", error);
        }
      }

      // Navigate to search results page with location in URL
      const locationSlug = search.destination.toLowerCase().replace(/\s+/g, '-');
      router.push(`/hotel/cheap-hotels/${encodeURIComponent(locationSlug)}`);
    }
  };

  const handleRemoveSearch = (e: React.MouseEvent, searchId: string) => {
    e.stopPropagation();

    // Get original searches and remove the one with matching ID
    const originalSearches = getRecentSearches();
    const updatedOriginalSearches = originalSearches.filter(search =>
      (search.locationId !== searchId) &&
      ((search.fullLocationData || search.searchQuery) !== recentSearches.find(s => s.id === searchId)?.destination)
    );

    // Update localStorage with the filtered original searches
    if (typeof window !== 'undefined') {
      localStorage.setItem('recentSearches', JSON.stringify(updatedOriginalSearches));
    }

    // Update display
    const updatedDisplaySearches = convertToDisplayFormat(updatedOriginalSearches);
    setRecentSearches(updatedDisplaySearches);
  };

  const handleClearAll = () => {
    setRecentSearches([]);

    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('recentSearches');
    }
  };

  if (recentSearches.length === 0) {
    return null;
  }

  return (
    <div className="w-full  mt-[30px] md:mt-10 ">
      <div className="flex flex-row justify-between items-center mb-6 md:mb-8  sm:items-start sm:gap-2">
        <h3 className="text-lg font-semibold text-[#1a1a1a] m-0 md:text-2xl">Recent searches</h3>
        <button className="bg-transparent border border-gray-300 text-gray-600 text-[13px] font-medium px-3 py-[6px] rounded-md cursor-pointer transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 hover:text-gray-700 sm:self-end sm:text-xs sm:px-[10px] sm:py-[5px] md:text-sm md:px-4 md:py-2" onClick={handleClearAll}>
          Clear all
        </button>
      </div>

      <div className="relative">
        <div className="flex gap-3 overflow-x-auto pb-2 scroll-smooth [-webkit-overflow-scrolling:touch] [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden md:gap-[10px]">
          {recentSearches.map((search) => (
            <div
              key={search.id}
              className="flex-[0_0_auto] min-w-[240px] max-w-[280px] bg-white border border-gray-300 rounded-lg px-4 py-3 cursor-pointer transition-all duration-200 flex justify-between items-center hover:border-[#003b95] hover:shadow-[0_2px_8px_rgba(0,59,149,0.1)] hover:bg-blue-50 active:scale-[0.98] md:min-w-[220px] md:max-w-[260px] md:px-[14px] md:py-[10px] sm:min-w-[200px] sm:max-w-[240px] sm:px-3 sm:py-[10px]"
              onClick={() => handleSearchClick(search)}
              title={`Search hotels in ${search.destination}`}
            >
              <div className="flex-1 min-w-0">
                <div className="text-sm font-semibold text-[#1a1a1a] mb-1 whitespace-nowrap overflow-hidden text-ellipsis sm:text-[13px]">{search.destination}</div>
                <div className="flex items-center text-xs text-gray-600 whitespace-nowrap overflow-hidden text-ellipsis sm:text-[11px]">
                  <span className="font-medium">{search.checkIn} - {search.checkOut}</span>
                  <span className="mx-[6px] text-gray-500 sm:mx-1">•</span>
                  <span>{search.guests} adults</span>
                  {search.children && search.children > 0 && (
                    <>
                      <span className="mx-[6px] text-gray-500 sm:mx-1">•</span>
                      <span>{search.children} children</span>
                    </>
                  )}
                  <span className="mx-[6px] text-gray-500 sm:mx-1">•</span>
                  <span>{search.rooms} {search.rooms === 1 ? 'room' : 'rooms'}</span>
                </div>
              </div>
              <button
                className="bg-transparent border-0 text-gray-500 text-xs cursor-pointer p-1 rounded flex-shrink-0 ml-2 transition-all duration-200 hover:bg-gray-100 hover:text-gray-600 sm:text-[11px] sm:p-[3px]"
                onClick={(e) => handleRemoveSearch(e, search.id)}
                aria-label="Remove search"
              >
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default RecentSearches;
