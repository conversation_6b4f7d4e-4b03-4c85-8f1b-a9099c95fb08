'use client';
import './style.scss';
import { getBookingWithReference } from '@/api/hotel/booking-service';
import { useCommonContext } from '@/app/contexts/commonContext';
import { simplifyBookingData } from '@/helpers/hotel/booking-helper';
import { getConvertedCurrency } from '@/helpers/hotel/currency-helper';
import { SimplifiedHotelBooking } from '@/models/hotel/booking.model';
import { HotelSearchData } from '@/models/hotel/search-page.model';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useState } from 'react'

function Page() {
    const router = useRouter();
    const [bookingReference, setBookingReference] = useState<string>('');
    const [bookingDetails, setBookingDetails] = useState<SimplifiedHotelBooking>();
    const [isLoading,setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const { selectedCurrency  , setHotelSearchData } = useCommonContext();

    const handlePrint = () => {
      if (typeof window !== 'undefined') {
        window.print();
      }
    };

    const handleRetry = () => {
      router.push('/BookingPreviewPage');
    };

    const formatDate = (dateString: string): string => {
      try {
        if (!dateString) return 'Date not available';
        
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          console.warn('Invalid date string provided to formatDate:', dateString);
          return 'Invalid date';
        }
        
        return date.toLocaleDateString('en-US', {
          weekday: 'short',
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      } catch (error) {
        console.error('Error formatting date:', { dateString, error });
        return 'Date not available';
      }
    };

    const getBookingDetails = useCallback(async (reference: string , searchData:HotelSearchData) => {
      setIsLoading(true);
      try {
        const response = await getBookingWithReference(reference);
        
        if (response && response.booking_reference && searchData) {
          const convertedBooking = simplifyBookingData(response, searchData);
          
          if (convertedBooking && convertedBooking.id) {
            setBookingDetails(convertedBooking);
          }
        }
      } catch (error) {
        setError('Failed to load booking details. Please try again. ' + error);
      } finally {
        setIsLoading(false);
      }
    }, []);

    useEffect(() => {
      if (typeof window !== "undefined") {
          const searchData = localStorage.getItem('hotelSearchData');
          setHotelSearchData(JSON.parse(localStorage.getItem('hotelSearchData') || '{}'));
          const path = window.location.pathname;
          const pathSegments = path.split('/');
          const reference = pathSegments[pathSegments.length - 1];
          if(reference){
            getBookingDetails(reference , JSON.parse(searchData || '{}'));
            setBookingReference(reference);
          }
      }
    }, [getBookingDetails,setHotelSearchData]);

      // Render loading state
  const renderLoadingContent = () => (
    <div className="loading-container">
      <div className="loading-spinner"></div>
      <p className="loading-text">Loading your booking details...</p>
    </div>
  );

  // Render error state
  const renderErrorContent = () => (
    <div className="error-container">
      <i className="fa-solid fa-triangle-exclamation error-icon"></i>
      <h2 className="error-title">Unable to Load Booking Details</h2>
      <p className="error-message">{error}</p>
      <div className="error-actions">
        <button onClick={() => window.location.reload()} className="btn primary">
          <i className="fa-solid fa-rotate-right"></i> Try Again
        </button>
        <Link href="/profile" className="btn secondary">
          <i className="fa-solid fa-user"></i> My Bookings
        </Link>
        <Link href="/contact" className="btn secondary">
          <i className="fa-solid fa-headset"></i> Contact Support
        </Link>
      </div>
    </div>
  );

  const renderSuccessContent = () => {
    if (!bookingDetails) return null;
    return (
      <>
        <div className="confirmation-header success">
          <i className="fa-solid fa-circle-check"></i>
          <h1>Booking Confirmed</h1>
          <p>Your reservation has been successfully confirmed.</p>
        </div>
        <div className="confirmation-code">
          <p>Confirmation Code: <strong>{bookingDetails.booking_reference}</strong></p>
        </div>
        <div className="confirmation-details">
          <div className="section">
            <h2>Reservation Details</h2>
            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-hotel"></i>
                  <div className="detail-text">
                    <label>Hotel</label>
                    <p>{bookingDetails.hotelName}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-bed"></i>
                  <div className="detail-text">
                    <label>Room Type</label>
                    <p>{bookingDetails.roomName}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-in</label>
                    <p>{formatDate(bookingDetails.checkInDate)}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-out</label>
                    <p>{formatDate(bookingDetails.checkOutDate)}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-user"></i>
                  <div className="detail-text">
                    <label>Guests</label>
                    <p>{bookingDetails.guests.adults} Adults</p>
                    <p>{bookingDetails.guests.children} Children</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-moon"></i>
                  <div className="detail-text">
                    <label>Length of Stay</label>
                    <p>{bookingDetails.nights} Nights</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="section">
            <h2>Payment Summary</h2>
            <div className="payment-summary">
              <div className="payment-row">
                <span>Room Rate ({bookingDetails.nights} nights)</span>
                <span>{getConvertedCurrency(bookingDetails.totalRate , selectedCurrency)}</span>
              </div>
              <div className="payment-row">
                <span>Taxes & Fees</span>
                <span>{getConvertedCurrency(bookingDetails.taxes[0].amount , selectedCurrency)}</span>
              </div>
              <div className="payment-row total">
                <span>Total Amount</span>
                <span>{getConvertedCurrency(bookingDetails.totalRate , selectedCurrency)}</span>
              </div>
              <div className="payment-method">
                <span>Payment Status:</span>
                <span>{bookingDetails.payment_status}</span>
              </div>
            </div>
          </div>

          <div className="section info-section">
            <div className="info-item">
              <i className="fa-solid fa-circle-info"></i>
              <p>Check-in time starts at {bookingDetails.checkInTime}, and check-out time is until {bookingDetails.checkOutTime}.</p>
            </div>
            <div className="info-item">
              <i className="fa-solid fa-envelope"></i>
              <p>A confirmation email has been sent to {bookingDetails.billing_contact.email}.</p>
            </div>
            {bookingDetails && (
              <div className="info-item">
                <i className="fa-solid fa-phone"></i>
                <p>Hotel Contact: 8754488415</p>
              </div>
            )}
          </div>

          <div className="actions">
            <Link
              href={bookingReference ? `/Itinerary?bookingReference=${bookingReference}` : "/Itinerary"}
              className="btn secondary"
            >
              View Itinerary
            </Link>
            <Link href="/profile" className="btn secondary">
              My Bookings
            </Link>
            <button onClick={handlePrint} className="btn primary">
              <i className="fa-solid fa-print"></i> Print
            </button>
          </div>
        </div>
      </>
    )
  }

    // Render the booking failed UI
  const renderFailedContent = () => {
    if (!bookingDetails) return null;

    return (
      <>
        <div className="confirmation-header failed">
          <i className="fa-solid fa-circle-xmark"></i>
          <h1>Booking Failed</h1>
          <p>We encountered a problem with your reservation.</p>
        </div>

        <div className="error-message">
          <p>{error || 'Your booking could not be completed at this time.'}</p>
        </div>

        <div className="confirmation-details">
          <div className="section">
            <h2>Booking Details</h2>
            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-hotel"></i>
                  <div className="detail-text">
                    <label>Hotel</label>
                    <p>{bookingDetails.hotelName}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-bed"></i>
                  <div className="detail-text">
                    <label>Room Type</label>
                    <p>{bookingDetails.roomName}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="detail-row">
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-in</label>
                    <p>{formatDate(bookingDetails.checkInDate)}</p>
                  </div>
                </div>
              </div>
              <div className="detail-col">
                <div className="detail-item">
                  <i className="fa-solid fa-calendar-days"></i>
                  <div className="detail-text">
                    <label>Check-out</label>
                    <p>{formatDate(bookingDetails.checkOutDate)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="section payment-failed-section">
            <div className="info-item">
              <i className="fa-solid fa-triangle-exclamation"></i>
              <p>Your booking was not completed. Please try again or use a different payment method.</p>
            </div>
            <div className="info-item">
              <i className="fa-solid fa-credit-card"></i>
              <p>No charges have been made to your payment method.</p>
            </div>
            {bookingDetails.payment_status && (
              <div className="info-item">
                <i className="fa-solid fa-info-circle"></i>
                <p>Payment Status: {bookingDetails.payment_status}</p>
              </div>
            )}
          </div>

          <div className="actions">
            <button onClick={handleRetry} className="btn primary">
              <i className="fa-solid fa-rotate-right"></i> Try Again
            </button>
            <Link href="/contact" className="btn secondary">
              <i className="fa-solid fa-headset"></i> Contact Support
            </Link>
          </div>
        </div>
      </>
    );
  };

  const renderContent = () => {
    if (isLoading) {
      return renderLoadingContent();
    }

    if (error && !bookingDetails) {
      return renderErrorContent();
    }

    if (bookingDetails?.payment_status === 'UNPAID') {
      return renderSuccessContent();
    } else {
      return renderFailedContent();
    }
  };


  return (
    <div className={`booking-confirmation ${bookingDetails?.payment_status}`}>
      {renderContent()}

      {/* Only show footer if not in loading or error state */}
      {!isLoading && (error ? bookingDetails : true) && (
        <div className="footer">
          <p>Thank you for choosing KindAli</p>
          <div className="contact">
            <span><i className="fa-solid fa-phone"></i> **************</span>
            <span><i className="fa-solid fa-envelope"></i> <EMAIL></span>
          </div>
        </div>
      )}
    </div>
  )
}

export default Page