"use client";
import Image from "next/image";
import React, { useEffect, useState, useRef } from "react";
import "./image-showcase.scss";
import { CarouselImage } from "../../../detail-page-internal.model";

interface ImageShowCaseProps {
  images: CarouselImage[];
}

function ImageShowCase({ images }: ImageShowCaseProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [slideDirection, setSlideDirection] = useState<'next' | 'prev'>('next');
  const [disableTransition, setDisableTransition] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const thumbnailContainerRef = useRef<HTMLDivElement>(null);
  const thumbnailRefs = useRef<Array<HTMLDivElement | null>>([]);

  // Check if screen is mobile-sized
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 950);
    };
    
    // Initial check
    checkMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Initialize thumbnailRefs array when images change
  useEffect(() => {
    thumbnailRefs.current = Array(images.length).fill(null);
  }, [images]);

  // Update current index when scrolling stops
  useEffect(() => {
    if (isMobile && scrollContainerRef.current) {
      const handleScrollEnd = () => {
        if (scrollContainerRef.current) {
          const scrollPosition = scrollContainerRef.current.scrollLeft;
          const itemWidth = scrollContainerRef.current.offsetWidth;
          const newIndex = Math.round(scrollPosition / itemWidth);
          if (newIndex !== currentIndex && newIndex >= 0 && newIndex < images.length) {
            setCurrentIndex(newIndex);
          }
        }
      };

      const scrollContainer = scrollContainerRef.current;
      let scrollTimeout: NodeJS.Timeout;

      const handleScroll = () => {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(handleScrollEnd, 50);
      };

      scrollContainer.addEventListener('scroll', handleScroll);
      return () => {
        scrollContainer.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeout);
      };
    }
  }, [isMobile, currentIndex, images]);

  // Ensure scroll position matches current index and direction
  useEffect(() => {
    if (isMobile && scrollContainerRef.current) {
      const itemWidth = scrollContainerRef.current.offsetWidth;
      scrollContainerRef.current.scrollTo({
        left: currentIndex * itemWidth,
        behavior: 'smooth'
      });
    }
    
    // Scroll thumbnail to center regardless of navigation direction
    scrollThumbnailToCenter();
  }, [currentIndex, isMobile]);

  // Improved thumbnail scrolling function that uses refs
  const scrollThumbnailToCenter = () => {
    if (!thumbnailContainerRef.current || images.length === 0) return;
    
    const container = thumbnailContainerRef.current;
    const activeThumb = thumbnailRefs.current[currentIndex];
    
    if (!activeThumb) return;
    
    const containerWidth = container.offsetWidth;
    const thumbWidth = activeThumb.offsetWidth;
    const thumbLeft = activeThumb.offsetLeft;
    
    // Calculate center position
    const idealScrollPosition = thumbLeft - (containerWidth / 2) + (thumbWidth / 2);
    
    // Ensure scroll position is within bounds
    const maxScroll = container.scrollWidth - containerWidth;
    const scrollTo = Math.max(0, Math.min(idealScrollPosition, maxScroll));
    
    // Perform the scroll with smooth behavior
    container.scrollTo({
      left: scrollTo,
      behavior: 'smooth'
    });
  };

  const nextSlide = () => {
    setSlideDirection('next'); // Set direction to next
    setDisableTransition(false);
    if (images && images.length > 0) {
      const newIndex = (currentIndex + 1) % images.length;
      setCurrentIndex(newIndex);
    }
  };
  
  const prevSlide = () => {
    setSlideDirection('prev'); // Set direction to prev
    setDisableTransition(false);
    if (images && images.length > 0) {
      const newIndex = (currentIndex - 1 + images.length) % images.length;
      setCurrentIndex(newIndex);
    }
  };

  const handleThumbnailClick = (index: number) => {
    // Set slide direction based on which way we're moving
    if (index > currentIndex) {
      setSlideDirection('next');
    } else if (index < currentIndex) {
      setSlideDirection('prev');
    }
    
    setDisableTransition(false); // Allow transitions for better UX
    setCurrentIndex(index);
  };

  // Safety check to prevent errors if images array is empty
  if (!images || images.length === 0) {
    return (
      <div className="image-showcase-container">
        <div className="image-showcase">
          <div className="no-images">No images available</div>
        </div>
      </div>
    );
  }

  return (
    <div className="image-showcase-container">
      <div className="navigation-container">
        {/* Left Arrow */}
        <button className="arrow left" onClick={prevSlide}>
          <i className="fa-solid fa-chevron-left"></i>
        </button>
        
        <div className={`image-showcase ${isMobile ? 'mobile' : ''}`}>
          {/* Main Image Display */}
          {isMobile ? (
            // Mobile horizontal scroll container
            <div 
              className="mobile-scroll-container" 
              ref={scrollContainerRef}
            >
              {images.map((image, index) => (
                <div className="mobile-image-item" key={index}>
                  <Image 
                    src={image?.url} 
                    alt={image?.caption || "hotel image"} 
                    fill 
                    style={{ objectFit: "cover" }}
                  />
                </div>
              ))}
            </div>
          ) : (
            // Desktop single image view
            <div className={`main-image-container ${slideDirection} ${disableTransition ? "no-transition" : ""}`}>
              <div className="main-image" key={currentIndex}>
                <Image 
                  src={images[currentIndex]?.url} 
                  alt={images[currentIndex]?.caption || "hotel image"} 
                  fill 
                  style={{ objectFit: "cover" }}  
                  priority
                />
              </div>
            </div>
          )}
          
          {/* Image Counter - show on both mobile and desktop */}
          <div className="image-counter">
            {currentIndex + 1} / {images.length}
          </div>

          <div className="thumbnail-container" ref={thumbnailContainerRef}>
            {images.map((image, index) => (
              <div
                key={index}
                className={`thumbnail ${index === currentIndex ? "active" : ""}`}
                onClick={() => handleThumbnailClick(index)}
                ref={el => {
                  thumbnailRefs.current[index] = el;
                }}

              >
                <Image 
                  src={image?.url} 
                  alt={image?.caption || "hotel image"} 
                  fill 
                  style={{ objectFit: "cover" }} 
                />
              </div>
            ))}
          </div>
        </div>
        
        {/* Right Arrow */}
        <button className="arrow right" onClick={nextSlide}>
          <i className="fa-solid fa-chevron-right"></i>
        </button>
      </div>
    </div>
  );
}

export default ImageShowCase;