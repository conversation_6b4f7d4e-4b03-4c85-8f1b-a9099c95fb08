// src/components/RecommendationCardItem/RecommendationCardItem.tsx

import React from 'react';
import { GroupedRate } from '@/models/hotel/rooms-and-rates.model';
import Image from 'next/image';

// Helper function to format text (can be moved to a utils file)
const formatText = (text: string) => {
  if (!text) return '';
  return text
    .toLowerCase()
    .split(/[\s-_]+/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

interface RecommendationCardItemProps {
  groupedRate: GroupedRate;
  onClick: (groupedRate: GroupedRate) => void;
}

const RecommendationCardItem: React.FC<RecommendationCardItemProps> = ({ groupedRate, onClick }) => {

  const imageUrl = groupedRate.room.images?.[0]?.links?.[0]?.url || '/assets/img/no-image-placeholder.png';


  return (
    <div
      className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
      onClick={() => onClick(groupedRate)}
    >
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Room Image */}
        <div className="relative sm:w-32 h-24 flex-shrink-0 overflow-hidden rounded-lg">
          <Image
            src={imageUrl}
            alt={groupedRate.room.name || 'Placeholder image for room'}
            layout="fill"
            objectFit="cover"
          />
        </div>

        {/* Room Details */}
        <div className="flex-1 relative">
          {/* Room Type */}
          <div className="mb-2">
            <h4 className="font-semibold text-primary-color text-sm pr-20"> {/* Added padding for badges */}
              {groupedRate.room.name}
            </h4>
          </div>

          {/* Occupancy Information */}
          {groupedRate.rate.occupancies?.length > 0 && (
            <div className="mb-2">
              <div className="flex flex-wrap gap-2">
                {groupedRate.rate.occupancies.map((occupancy, occIndex) => (
                  <div key={occIndex} className="flex items-center gap-1 text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded-full">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" /></svg>
                    <span>{occupancy.numOfAdults} Adult{parseInt(occupancy.numOfAdults) !== 1 ? 's' : ''}</span>
                    {parseInt(occupancy.numOfChildren) > 0 && (
                      <>
                        <span className="text-blue-400">•</span>
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 2a3 3 0 100 6 3 3 0 000-6zM4 18a6 6 0 1112 0H4z" clipRule="evenodd" /></svg>
                        <span>{occupancy.numOfChildren} Child{parseInt(occupancy.numOfChildren) !== 1 ? 'ren' : ''}</span>
                      </>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Badges */}
          <div className="absolute top-0 right-0 flex flex-wrap gap-1">
            {groupedRate.rate.refundable && (
              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">Refundable</span>
            )}
            {groupedRate.rate.totalRate < (groupedRate.rate.publishedRate || groupedRate.rate.baseRate * 1.2) && (
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded">Best Deal</span>
            )}
          </div>

          {/* Amenities */}
          <div className="space-y-1 mb-2">
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <svg className="w-3 h-3 text-secondary-color" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /></svg>
              <span>{formatText(groupedRate.rate.boardBasis.description)}</span>
            </div>
            {groupedRate.room.facilities.slice(0, 2).map((facility, idx) => (
              <div key={idx} className="flex items-center gap-2 text-xs text-gray-600">
                <svg className="w-3 h-3 text-secondary-color" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /></svg>
                <span>{formatText(facility.name)}</span>
              </div>
            ))}
          </div>

          {/* Policies */}
          <div className="text-xs text-secondary-color">
            {groupedRate.rate.refundable ? 'Free cancellation available' : 'Cancellation policy applies'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecommendationCardItem;