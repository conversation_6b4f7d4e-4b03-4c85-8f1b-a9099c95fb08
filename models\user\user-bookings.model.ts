// User Bookings API Models
// Based on the API response structure from /api/v1/booking/user/{userId}

/**
 * Main API response type - array of user bookings
 */
export type UserBookingsApiResponse = UserBookingItem[];

/**
 * Individual booking item from the API response
 */
export interface UserBookingItem {
  id: number;
  booking_reference: string;
  service_type: string;
  status: string;
  payment_status: string;
  user_id: number;
  created_at: string;
  updated_at: string;
  hotel_booking: HotelBookingDetails;
}

/**
 * Hotel booking details nested within the user booking
 */
export interface HotelBookingDetails {
  id: number;
  master_booking_reference: string;
  provider_booking_id: string;
  booking_ref_id: string;
  hotel_id: string;
  search_key: string;
  rate_ids: string[];
  rooms_allocations: RoomAllocation[];
  billing_contact: BillingContact;
  credit_card_info: any;
  special_requests: any;
  provider_response: ProviderResponse;
}

/**
 * Room allocation details
 */
export interface RoomAllocation {
  roomid: string;
  rateid: string;
  guests: Guest[];
}

/**
 * Guest information
 */
export interface Guest {
  type: string;
  title: string;
  firstname: string;
  lastname: string;
  age: number;
  email: string;
}

/**
 * Billing contact information
 */
export interface BillingContact {
  title: string;
  firstName: string;
  lastName: string;
  age: number;
  contact: ContactInfo;
}

/**
 * Contact information
 */
export interface ContactInfo {
  phone: string;
  address: Address;
  email: string;
}

/**
 * Address information
 */
export interface Address {
  line1: string;
  line2: string;
  city: City;
  state: State;
  country: Country;
  postalCode: string;
}

/**
 * City information
 */
export interface City {
  name: string;
  code: string;
}

/**
 * State information
 */
export interface State {
  name: string;
  code: string;
}

/**
 * Country information
 */
export interface Country {
  name: string;
  code: string;
}

/**
 * Provider response containing detailed hotel booking data
 */
export interface ProviderResponse {
  hotelBooking: HotelBookingProviderData;
}

/**
 * Detailed hotel booking data from provider
 */
export interface HotelBookingProviderData {
  billingContact: BillingContact;
  bookingId: string;
  bookingStatus: string;
  bookingType: string;
  cancellationDate: string;
  channelId: string;
  correlationId: string;
  countryOfResidence: string;
  creationDate: string;
  culture: string;
  email: string;
  guestNames: string;
  guests: string[];
  hotel: Hotel;
  hotel_id: string;
  isModified: boolean;
  nationality: string;
  providerId: string;
  providerName: string;
  rates: Rate[];
  rooms: Room[];
  roomsAllocations: ProviderRoomAllocation[];
  search_key: string;
  supplierRates: Rate[];
  totalRate: number;
  tripEndDate: string;
  tripStartDate: string;
}

/**
 * Hotel information
 */
export interface Hotel {
  category: string;
  chainName: string;
  checkinInfo: CheckinInfo;
  checkoutInfo: CheckoutInfo;
  contact: HotelContact;
  descriptions: Description[];
  distance: string;
  facilities: Facility[];
  geoCode: GeoCode;
  heroImage: string;
  id: string;
  name: string;
  policies: Policy[];
  providerHotelId: string;
  providerId: string;
  relevanceScore: string;
  starRating: string;
}

/**
 * Check-in information
 */
export interface CheckinInfo {
  beginTime: string;
  minAge: string;
}

/**
 * Check-out information
 */
export interface CheckoutInfo {
  time: string;
}

/**
 * Hotel contact information
 */
export interface HotelContact {
  address: HotelAddress;
  email: string;
  phone: string;
}

/**
 * Hotel address
 */
export interface HotelAddress {
  city: City;
  country: Country;
  line1: string;
  state: any;
}

/**
 * Hotel description
 */
export interface Description {
  text: string;
  type: string;
}

/**
 * Hotel facility
 */
export interface Facility {
  name: string;
}

/**
 * Geographic coordinates
 */
export interface GeoCode {
  lat: string;
  long: string;
}

/**
 * Hotel policy
 */
export interface Policy {
  text: string;
  type: string;
}

/**
 * Rate information
 */
export interface Rate {
  IsPANMandatory: boolean;
  IsPassportMandatory: boolean;
  additionalCharges: AdditionalCharge[];
  allGuestsInfoRequired: boolean;
  availability: string;
  baseRate: number;
  boardBasis: BoardBasis;
  cancellationPolicies: CancellationPolicy[];
  cardRequired: boolean;
  conversionRate?: ConversionRate;
  currency: string;
  dailyRates: DailyRate[];
  depositRequired: boolean;
  deposits: any[];
  distributionChannel: string;
  distributionType: string;
  guaranteeRequired: boolean;
  id: string;
  isChildConvertedToAdult: boolean;
  isContractedRate: boolean;
  minSellingRate: number;
  needsPriceCheck: boolean;
  occupancies: Occupancy[];
  offers: any[];
  onlineCancellable: boolean;
  payAtHotel: boolean;
  policies: Policy[];
  providerHotelId: string;
  providerId: string;
  providerName: string;
  publishedBaseRate: number;
  publishedRate: number;
  refundability: string;
  refundable: boolean;
  specialRequestSupported: boolean;
  taxes: Tax[];
  totalRate: number;
  traderInformation: TraderInformation;
  type: string;
  usdconversionRate?: ConversionRate;
}

/**
 * Additional charge information
 */
export interface AdditionalCharge {
  charge: Charge;
  text: string;
}

/**
 * Charge details
 */
export interface Charge {
  amount: number;
  currency: string;
  description: string;
  frequency: string;
  type: string;
  unit: string;
}

/**
 * Board basis information
 */
export interface BoardBasis {
  description: string;
  type: string;
}

/**
 * Cancellation policy
 */
export interface CancellationPolicy {
  rules: CancellationRule[];
}

/**
 * Cancellation rule
 */
export interface CancellationRule {
  end: string;
  estimatedValue: number;
  start: string;
  value: number;
  valueType: string;
}

/**
 * Conversion rate information
 */
export interface ConversionRate {
  conversionFactor: number;
  fromCurrency: string;
  toCurrency: string;
}

/**
 * Daily rate information
 */
export interface DailyRate {
  amount: number;
  date: string;
  discount: number;
  taxIncluded: boolean;
}

/**
 * Occupancy information
 */
export interface Occupancy {
  childAges: any[];
  numOfAdults: string;
  numOfChildren: string;
  roomId: string;
}

/**
 * Tax information
 */
export interface Tax {
  amount: number;
  description: string;
  isIncludedInBaseRate: boolean;
}

/**
 * Trader information
 */
export interface TraderInformation {
  trader: any[];
}

/**
 * Room information
 */
export interface Room {
  beds: Bed[];
  description: string;
  facilities: Facility[];
  id: string;
  maxGuestAllowed: string;
  name: string;
  smokingAllowed: boolean;
  views: any[];
}

/**
 * Bed information
 */
export interface Bed {
  count: string;
}

/**
 * Provider room allocation
 */
export interface ProviderRoomAllocation {
  guests: ProviderGuest[];
  rateId: string;
  roomId: string;
}

/**
 * Provider guest information
 */
export interface ProviderGuest {
  age: string;
  email: string;
  firstName: string;
  lastName: string;
  title: string;
  type: string;
}

/**
 * Query parameters for filtering and pagination
 */
export interface BookingQueryParams {
  status?: string;
  service_type?: string;
  payment_status?: string;
  limit?: number;
  offset?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

/**
 * Booking status types
 */
export type BookingStatus = 'CONFIRMED' | 'PENDING' | 'FAILED' | 'CANCELLED' | 'COMPLETED';

/**
 * Payment status types
 */
export type PaymentStatus = 'PAID' | 'UNPAID' | 'PENDING' | 'FAILED' | 'REFUNDED';

/**
 * Service types
 */
export type ServiceType = 'HOTEL' | 'FLIGHT' | 'CAR' | 'ACTIVITY';

/**
 * Transformed booking item for UI display
 */
export interface TransformedBookingItem {
  id: string;
  type: "hotel";
  name: string;
  location: string;
  date: string;
  status: "confirmed" | "pending" | "cancelled" | "failed";
  price: number;
  currency: string;
  reference: string;
  amenities?: string[];
  checkIn: string;
  checkOut: string;
  guests: string[];
  hotelImage?: string;
  cancellationPolicy?: string;
  paymentStatus: string;
  bookingDate: string;
  providerName: string;
  roomDetails: {
    name: string;
    description: string;
    facilities: string[];
  }[];
}
