"use client";
import React from 'react';

function HotelSortShimmer() {
  return (
    <>
      {/* --- Desktop Shimmer --- */}
      <div className="hidden md:block bg-white rounded-lg shadow-sm border border-gray-200 animate-pulse">
        <div className="flex items-center h-12">
          {/* Updated background for the label area */}
          <div className="px-5 h-full flex items-center bg-gray-100 border-r border-gray-200">
            <div className="h-4 w-16 bg-gray-300 rounded"></div>
          </div>
          <div className="flex flex-1 justify-around items-center h-full px-4">
            <div className="h-5 w-20 bg-gray-200 rounded-md"></div>
            <div className="h-5 w-20 bg-gray-200 rounded-md"></div>
            <div className="h-5 w-20 bg-gray-200 rounded-md"></div>
          </div>
        </div>
      </div>

      {/* --- Mobile Shimmer --- */}
      <div className="md:hidden w-full max-h-[calc(100vh-235px)] overflow-y-auto border-t border-gray-200 animate-pulse">
        {/* Create an array to easily repeat shimmer items */}
        {Array.from({ length: 5 }).map((_, index) => (
          <div
            key={index}
            className="w-full p-4 flex items-center justify-between border-b border-gray-100"
          >
            <div className="h-4 w-3/4 bg-gray-200 rounded"></div>
            <div className="h-5 w-5 bg-gray-200 rounded-full"></div>
          </div>
        ))}
      </div>
    </>
  );
}

export default HotelSortShimmer;