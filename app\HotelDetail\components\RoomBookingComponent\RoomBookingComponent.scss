@use "@styles/variable" as *;
@use "/styles/zIndex" as *;
.room-booking-component {
  // max-width: 1280px;
  // padding: 20px;
  // margin: 0 auto;
  .booking-section-bottom-0 {
    background-color: #ffffff;
    width: 100%;
    padding: 10px 20px;
    display: flex
;
    justify-content: end;
    position: fixed;
    bottom: -3px;
    left: 0;
    right: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 400;

    .totalAmtLabelValue .value {
    font-size: 20px;
    font-weight: 700;
}

    .bookingBtn {
    width: 180px;
    min-height: 50px;
    padding: 0 20px;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    background-color: #003b95;
    border-radius: 10px;
    transition: background-color 0.2s ease;
}
}

  .route-path {
    display: flex;
    align-items: center;
    gap: 5px;
    flex-wrap: nowrap;
    margin-bottom: 16px;
    padding: 16px 8px 0 8px;

    font-size: 12px;
    span {
      color: $primary_color;
    }

    .fa-greater-than {
      font-size: 9px;
      margin-bottom: -2px;
    }

    @media (max-width: $isMobile) {
      display: none;
    }
  }

  // Mobile Header Styles
  .mobile-header {
    width: 100%;
    background-color: #ffffff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: z-index(sticky) + 1; 
    position: sticky;
    top: 10px;

    // &.sticky {
    //   position: fixed;
    //   top: 0;
    //   left: 0;
    //   right: 0;
    //   z-index: z-index(sticky) + 1;
    // }

    .mobile-header-content {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      gap: 15px;

      .back-button {
        background: none;
        border: none;
        font-size: 18px;
        color: $primary-color;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.2s ease;
        flex-shrink: 0;

        &:hover {
          background-color: rgba(0, 59, 149, 0.1);
        }

        &:active {
          background-color: rgba(0, 59, 149, 0.2);
        }
      }

      .mobile-header-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;
        min-width: 0; // Allow flex item to shrink

        .hotel-name-with-rating {
          display: flex;
          align-items: center;
          gap: 8px;
          min-width: 0; // Allow flex item to shrink

          .hotel-name {
            font-size: 16px;
            font-weight: 600;
            color: #17181c;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
            //flex: 1;
            min-width: 0; // Allow flex item to shrink
          }

          .rating-stars {
            display: flex;
            align-items: center;
            gap: 1px;
            flex-shrink: 0; // Prevent stars from shrinking

            i.fa-star {
              font-size: 10px;
              color: $rating_color;
            }
          }
        }

        .mobile-date-range {
          // Compact date range styling is handled in the DateRangeDisplay component
          margin: 0;
        }
      }
    }
  }

  // Mobile header spacer for scroll detection
  .mobile-header-spacer {
    height: 1px;
    width: 100%;
  }

  .hotel-info-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px 0 30px 0;

    .info-header {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      @media (max-width: $breakpoint-md) {
        flex-direction: column;
      }

      .heading {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 0 15px;

        h2 {
          font-size: 24px;
          font-weight: 700;

          @media (max-width: $breakpoint-md) {
            font-size: 28px;
          }

          @media (max-width: $breakpoint-sm) {
            font-size: 24px;
          }

          @media (max-width: $breakpoint-xs) {
            font-size: 20px;
          }
        }

        .rating {
          font-size: 10px;
          color: $rating_color;
          margin-left: 8px;

          .fa-star {
            margin-right: 2px;
          }
        }
      }
      .buttons {
        display: flex;
        flex-direction: row;
        align-items: center;

        @media (max-width: $breakpoint-md) {
          padding-bottom: 15px;
        }
      }
    }

    .review-location {
      display: flex;
      flex-direction: row;
      justify-content: start;
      gap: 60px;

      @media (max-width: $isMobile) {
        flex-direction: column;
        gap: 15px;
      }

      .review,
      .location {
        display: flex;
        flex-direction: row;
      }

      .review {
        .rating {
          padding: 10px;
          margin: 0 10px 0 0;
          background-color: #17181c;
          font-size: 16px;
          color: #fafafa;
          font-weight: 700;
          border-radius: 10px;
          display: flex;
          justify-content: center;
          align-items: center;

          @media (max-width: $breakpoint-md) {
            font-size: 18px;
          }

          @media (max-width: $breakpoint-xs) {
            font-size: 16px;
          }
        }

        .rating-detail {
          .detail1 {
            font-size: 15px;
            font-weight: 600;
            color: #17181c;

            @media (max-width: $breakpoint-sm) {
              font-size: 15px;
            }

            @media (max-width: $breakpoint-xs) {
              font-size: 14px;
            }
          }

          .detail2 {
            font-size: 14px;
            font-weight: 600;
            color: #5e616e;
            @media (max-width: $breakpoint-sm) {
              font-size: 13px;
            }
          }
        }
      }

      .location {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 10px;

        .icon {
          background-color: rgba(8, 119, 103, 0.05);
          color: $primary-color;
          font-size: 26px;
          padding: 5px 12px;
          border-radius: 5px;

          @media (max-width: $breakpoint-sm) {
            font-size: 24px;
          }

          @media (max-width: $breakpoint-xs) {
            font-size: 22px;
          }
        }
        .details {
          .detail1 {
            font-size: 14px;
            font-weight: 400;

            @media (max-width: $breakpoint-sm) {
              font-size: 15px;
            }

            @media (max-width: $breakpoint-xs) {
              font-size: 14px;
            }
          }

          .detail2 {
            color: $primary-color;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;

            @media (max-width: $breakpoint-sm) {
              font-size: 13px;
            }
          }
        }
      }
    }
  }

  .navigation-tabs-container {
    &.sticky {
      position: sticky;
      width: 100%;
      top: 60px;
      background-color: #f2f2f2;
      left: 0;
      z-index: z-index(sticky);
      //border-top: 1px solid rgba(0, 0, 0, 0.2);

      @media(max-width: $isMobile){
        top: 0px;
      }

      // When mobile header is also sticky, position below it
      &.mobile-with-header {
        @media(max-width: $isMobile){
          top: 60px; // Height of mobile header + some padding
        }
      }
    }

    .navigation-tabs {
      display: flex;
      flex-direction: row;
      width: 100%;
      overflow-y: auto;
      //border-bottom: 1px solid rgba(0, 0, 0, 0.2);

      scrollbar-width: none; // Firefox
      -ms-overflow-style: none; // Internet Explorer and Edge

      &::-webkit-scrollbar {
        display: none; // Chrome, Safari, and Opera
      }

      .navigation-tab {
        padding: 10px 15px;
        font-size: 16px;
        font-weight: 500;
        color: #17181c;
        position: relative;
        cursor: pointer;
        user-select: none;

        @media (max-width: $breakpoint-sm) {
          font-size: 15px;
        }

        &:hover {
          background: rgba(0, 0, 0, 0.05);
        }

        &.active {
          //color: #0770e4;
          color: $secondary-color;
        }

        &.active::after {
          content: "";
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 3px;
          //background-color: #0770e4;
          background-color: $secondary-color;
          border-radius: 5px;
        }
      }
    }
  }

     .searchbar-container-list {
      @media (max-width: $isMobile) {
        display: none;
      }
    }
}

.hotel-facilites-container {
  @media (max-width: $isMobile) {
    display: none;
  }
}
