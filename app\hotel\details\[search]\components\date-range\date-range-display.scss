@use "/styles/variable" as *;

.date-range {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;

  // Compact single-line format
  &.compact {
    gap: 0;

    .compact-date-text {
      font-size: 12px;
      font-weight: 500;
      color: #666;
      line-height: 1.2;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .icon {
    background-color: rgba(8, 119, 103, 0.05);
    color: $primary-color;
    font-size: 26px;
    padding: 5px 12px;
    border-radius: 5px;

    @media (max-width: $breakpoint-sm) {
      font-size: 24px;
    }

    @media (max-width: $breakpoint-xs) {
      font-size: 22px;
    }
  }

  .details {
    display: flex;
    flex-direction: column;

    .detail1 {
      font-size: 14px;
      font-weight: 600;
      color: #17181c;

      @media (max-width: $breakpoint-sm) {
        font-size: 15px;
      }

      @media (max-width: $breakpoint-xs) {
        font-size: 14px;
      }
    }

    .detail2 {
      font-size: 14px;
      font-weight: 600;
      color: #5e616e;

      @media (max-width: $breakpoint-sm) {
        font-size: 13px;
      }
    }
  }
}
