'use client'
import React, { useState, useEffect, useCallback } from 'react'
import "./RoomSelection.scss";
import RoomRatesDisplay from '../HotelDetail/components/RoomBookingComponent/RoomRatesDisplay/RoomRatesDisplay';
import { useCommonContext } from '../contexts/commonContext';
import { getRoomsAndRates } from '@/api/hotel/rooms-and-rates-service';
import { transformRoomsAndRates } from '@/utils/roomsAndRatesTransformer';
import { RoomsAndRatesApiResponse, TransformedRoomsData } from '@/models/hotel/rooms-and-rates.model';
import MobileInnerPageHeader from '../components/mobile-inner-page-header/MobileInnerPageHeader';

function Page() {
  const [roomsAndRatesData, setRoomsAndRatesData] = useState<TransformedRoomsData>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { searchKey } = useCommonContext();

  // Get hotel ID from URL
  const getHotelId = useCallback(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('hotelId') || '';
    }
    return '';
  }, []);

  // Fetch rooms and rates data
  const fetchRoomsAndRates = useCallback(async () => {
    const hotelId = localStorage.getItem('selectedHotelId') || getHotelId();
    const keyToUse = searchKey || localStorage.getItem('searchKey');

    // if (!keyToUse || !hotelId) {
    //   console.warn("Missing search key or hotel ID for rooms and rates API");
    //   setIsLoading(false);
    //   return;
    // }
    if(keyToUse && hotelId){
      try {
        setIsLoading(true);
        const roomsAndRatesApiResponse: RoomsAndRatesApiResponse = await getRoomsAndRates(keyToUse, hotelId);
        const transformedData = transformRoomsAndRates(roomsAndRatesApiResponse);
        setRoomsAndRatesData(transformedData);
      } catch (err) {
        console.error("Rooms and rates API call failed:", err);
      } finally {
        setIsLoading(false);
      }
    }
  }, [getHotelId, searchKey]);

  useEffect(() => {
    fetchRoomsAndRates();
  }, [fetchRoomsAndRates]);

  const goBack = () => {
    window.history.back();
  }

  // Loading state
  if (isLoading) {
    return (
      <div className='common-container'>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-color mx-auto mb-4"></div>
            <p className="text-gray-600">Loading room options...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='w-full'>
        <MobileInnerPageHeader onBackClick={() => {goBack()}}>
          <div className="mobile-header-info">
            <h3 className="hotel-name">Rooms</h3>
            <p className='text-gray-600 text-xs'>Select your room</p>
          </div>
        </MobileInnerPageHeader>
      <div className='common-container'>
        <RoomRatesDisplay transformedRoomsData={roomsAndRatesData} />
      </div>
    </div>
  )
}

export default Page