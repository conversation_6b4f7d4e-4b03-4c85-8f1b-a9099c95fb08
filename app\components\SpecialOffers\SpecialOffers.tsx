"use client";
import React from "react";
import Image from "next/image";
import "./SpecialOffers.scss";

interface SpecialOffer {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  promoCode: string;
  image: string;
}

// Promotional banner style offers - exact match to your image
const specialOffers: SpecialOffer[] = [
  {
    id: "1",
    title: "Up to 25% Off",
    subtitle: "On Domestic Hotels",
    description: "*Offers is Valid On UPI Transactions Only",
    promoCode: "YTUPI",
    image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Kerala.jpg"
  },
  {
    id: "2",
    title: "Up to 35% OFF",
    subtitle: "On International Hotels",
    description: "*Offers is Valid Only On Confirmed Hotel Bookings",
    promoCode: "YTICICEMI",
    image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/goa.jpg"
  },
  {
    id: "3",
    title: "Up to 30% Off",
    subtitle: "On Weekend Bookings",
    description: "*Offer Valid On Axis Bank Credit Card EMI Transactions Only",
    promoCode: "YRAXISEMI",
    image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Dubai.png"
  },
  {
    id: "4",
    title: "Up to 40% OFF",
    subtitle: "On Luxury Resorts",
    description: "*Valid on HDFC Bank Debit & Credit Cards",
    promoCode: "YHDFC40",
    image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Maldives.png"
  },
  {
    id: "5",
    title: "Up to 20% Off",
    subtitle: "On Hill Station Hotels",
    description: "*Weekend Special Offer Valid Till Sunday",
    promoCode: "WEEKEND20",
    image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Himachal.png"
  }
];

function SpecialOffers() {
  const handleOfferClick = (offer: SpecialOffer) => {
    console.log("Clicked special offer:", offer);
    // Implement navigation to search with offer applied
  };

  const handleViewDetails = (e: React.MouseEvent, offer: SpecialOffer) => {
    e.stopPropagation();
    console.log("View details for:", offer.promoCode);
    // Implement view details logic
  };

  if (specialOffers.length === 0) {
    return null;
  }

  return (
    <div className="special-offers-main">
      <div className="special-offers-header">
        <h3 className="special-offers-title">Special Offers</h3>
        {/* <button className="bg-transparent border border-gray-300 text-gray-600 text-[13px] font-medium px-3 py-[6px] rounded-md cursor-pointer transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 hover:text-gray-700 sm:self-end sm:text-xs sm:px-[10px] sm:py-[5px] md:text-sm md:px-4 md:py-2">
          View all
        </button> */}
      </div>

      <div className="special-offers-container">
        <div className="special-offers-scroll">
          {specialOffers.map((offer) => (
            <div
              key={offer.id}
              className="offer-card"
              onClick={() => handleOfferClick(offer)}
            >
              <div className="offer-image-container">
                <div className="offer-image-wrapper">
                  <Image
                    src={offer.image}
                    alt={offer.title}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>

              <div className="offer-content">
                <div className="offer-text-content">
                  <h3 className="offer-title">{offer.title}</h3>
                  <p className="offer-subtitle">{offer.subtitle}</p>
                  <p className="offer-description">{offer.description}</p>
                </div>

                <div className="offer-actions">
                  <button className="promo-code-button">
                    {offer.promoCode}
                  </button>
                  <button
                    className="view-details-button"
                    onClick={(e) => handleViewDetails(e, offer)}
                  >
                    View Details
                    <i className="fa-solid fa-chevron-right chevron-icon"></i>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default SpecialOffers;
