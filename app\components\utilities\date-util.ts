export const formatDateForDisplay = (dateIsoString: string | null): string => {
    if (!dateIsoString) return "";
    const date = new Date(dateIsoString);

    // Get day, month, and year
    const day = date.getDate();
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    const year = date.getFullYear().toString().slice(-2); // Get last 2 digits of year

    return `${day} ${month}' ${year}`;
};

export function calculateNights(checkInDate?: string | Date, checkOutDate?: string | Date): number {
  if (!checkInDate || !checkOutDate) return 0;

  const checkIn = new Date(checkInDate).getTime();
  const checkOut = new Date(checkOutDate).getTime();

  if (isNaN(checkIn) || isNaN(checkOut)) return 0;

  return Math.round((checkOut - checkIn) / (1000 * 60 * 60 * 24));
}
