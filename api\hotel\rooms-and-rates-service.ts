import { RoomsAndRatesRequest, RoomsAndRatesApiResponse } from "@/models/hotel/rooms-and-rates.model";
import apiService from "../api-service";

/**
 * Service for fetching rooms and rates data for a specific hotel
 */
export const getRoomsAndRates = async (
  searchKey: string,
  hotelId: string
): Promise<RoomsAndRatesApiResponse> => {
  const payload: RoomsAndRatesRequest = {
    search_key: searchKey,
    hotel_id: hotelId,
  };

  try {
    const response = await apiService.post<RoomsAndRatesApiResponse>(
      "search/rooms-and-rates",
      payload
    );

    // API response received successfully

    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * Utility function to get the cheapest rate from rooms and rates response
 */
export const getCheapestRate = (roomsAndRatesResponse: RoomsAndRatesApiResponse): number | null => {
  if (!roomsAndRatesResponse?.data || roomsAndRatesResponse.data.length === 0) {
    return null;
  }

  let cheapestRate = Infinity;

  roomsAndRatesResponse.data.forEach(recommendation => {
    recommendation.groupedRates.forEach(groupedRate => {
      if (groupedRate.rate.totalRate < cheapestRate) {
        cheapestRate = groupedRate.rate.totalRate;
      }
    });
  });

  return cheapestRate === Infinity ? null : cheapestRate;
};

/**
 * Utility function to get all unique room types from rooms and rates response
 */
export const getRoomTypes = (roomsAndRatesResponse: RoomsAndRatesApiResponse): string[] => {
  if (!roomsAndRatesResponse?.data || roomsAndRatesResponse.data.length === 0) {
    return [];
  }

  const roomTypes = new Set<string>();

  roomsAndRatesResponse.data.forEach(recommendation => {
    recommendation.groupedRates.forEach(groupedRate => {
      roomTypes.add(groupedRate.room.name);
    });
  });

  return Array.from(roomTypes);
};

/**
 * Utility function to filter rooms by refundability
 */
export const getRefundableRooms = (roomsAndRatesResponse: RoomsAndRatesApiResponse): RoomsAndRatesApiResponse => {
  if (!roomsAndRatesResponse?.data || roomsAndRatesResponse.data.length === 0) {
    return { provider: roomsAndRatesResponse?.provider || '', data: [] };
  }

  const filteredData = roomsAndRatesResponse.data.map(recommendation => ({
    ...recommendation,
    groupedRates: recommendation.groupedRates.filter(groupedRate =>
      groupedRate.rate.refundable
    )
  })).filter(recommendation => recommendation.groupedRates.length > 0);

  return {
    provider: roomsAndRatesResponse.provider,
    data: filteredData
  };
};

/**
 * Utility function to sort rooms by price (ascending)
 */
export const sortRoomsByPrice = (roomsAndRatesResponse: RoomsAndRatesApiResponse): RoomsAndRatesApiResponse => {
  if (!roomsAndRatesResponse?.data || roomsAndRatesResponse.data.length === 0) {
    return { provider: roomsAndRatesResponse?.provider || '', data: [] };
  }

  const sortedData = roomsAndRatesResponse.data.map(recommendation => ({
    ...recommendation,
    groupedRates: [...recommendation.groupedRates].sort((a, b) =>
      a.rate.totalRate - b.rate.totalRate
    )
  }));

  return {
    provider: roomsAndRatesResponse.provider,
    data: sortedData
  };
};
