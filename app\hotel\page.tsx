"use client";

import React from "react";
import "../hotel.scss";
import searchBgImage from "../../public/assets/img/searchBg.webp";
import ImageSlider from "../components/ImageSlider/ImageSlider";
import DestinationGrid from "../components/DestinationGrid/DestinationGrid";
import FeatureHighlights from "../components/FeatureHighlights/FeatureHighlights";
import Footer from "@/app/components/footer/Footer";
import LandingPageContainer from "../components/LandingPageContainer/LandingPageContainer";
import RecentSearches from "../components/RecentSearches/RecentSearches";
import SpecialOffers from "../components/SpecialOffers/SpecialOffers";
import MobileAppDownload from "../components/MobileAppDownload/MobileAppDownload";
import FAQ from "../components/FAQ/FAQ";

const features = [
  {
    icon: "fa-bed",
    description: "Choose from more than 1000 top-rated hotels worldwide.",
    priceDetails: "From affordable stays to luxury resorts.",
  },
  {
    icon: "fa-star",
    description: "Find hotels with verified guest reviews and high ratings.",
    priceDetails: "Book with confidence every time.",
  },
  {
    icon: "fa-location-dot",
    description: "Stay at prime locations close to attractions and transit.",
    priceDetails: "Convenient hotel options in every city.",
  },
  {
    icon: "fa-tags",
    description: "Enjoy exclusive discounts on hotel bookings.",
    priceDetails: "Best deals for every budget.",
  },
  {
    icon: "fa-shield-halved",
    description: "Safe and secure hotel booking experience.",
    priceDetails: "Verified listings with secure payments.",
  },
  {
    icon: "fa-calendar-check",
    description: "Flexible bookings with free cancellation options.",
    priceDetails: "Book now, cancel later hassle-free.",
  },
];

function Page() {


  return (
    <div className="hotel-page-container ">
      <LandingPageContainer searchBgImageSrc={searchBgImage.src} />

      <div className="common-container">
        <RecentSearches />
        <SpecialOffers />
        <ImageSlider />
        <DestinationGrid />
      </div>

      <MobileAppDownload />

      <FAQ />

      <div className="common-container">
        <FeatureHighlights features={features} />
      </div>

      <Footer />
    </div>
  );
}

export default Page;
