"use client";

import { Map } from "react-map-gl/mapbox";
import "mapbox-gl/dist/mapbox-gl.css";

interface MapProps {
  center: [number, number];
  zoom: number;
  buttonText: string;
  onButtonClick: () => void;
}

export const MapboxMap: React.FC<MapProps> = ({
  center,
  zoom,
  buttonText,
  onButtonClick,
}) => {
  return (
    <div className="relative w-full h-full rounded-lg">
      <Map
        mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_GL_ACCESS_TOKEN}
        initialViewState={{
          longitude: center[0],
          latitude: center[1],
          zoom,
        }}
        style={{ width: "100%", height: "100%" }}
        mapStyle="mapbox://styles/mapbox/streets-v11"
      />
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10">
        <button
          onClick={onButtonClick}
          className="px-4 py-2 text-sm whitespace-nowrap bg-blue-600 text-white font-semibold rounded-full shadow-md hover:bg-blue-700"
        >
          {buttonText}
        </button>
      </div>
    </div>
  );
};
