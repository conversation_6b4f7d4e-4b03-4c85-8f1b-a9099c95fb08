import { HotelBookingResponse } from "../booking-init.model";

/**
 * Utility functions for handling booking initialization data
 */

/**
 * Store booking initialization data in localStorage
 * @param data - The booking initialization response data
 */
export const storeBookingInitData = (data: HotelBookingResponse): void => {
  try {
    localStorage.setItem('bookingInitData', JSON.stringify(data));
    console.log('✅ Booking initialization data stored in localStorage');
  } catch (error) {
    console.error('❌ Failed to store booking initialization data:', error);
  }
};

/**
 * Retrieve booking initialization data from localStorage
 * @returns The stored booking initialization data or null if not found
 */
export const getStoredBookingInitData = (): HotelBookingResponse | null => {
  try {
    const storedData = localStorage.getItem('bookingInitData');
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      console.log('📋 Retrieved booking initialization data from localStorage');
      return parsedData;
    }
    return null;
  } catch (error) {
    console.error('❌ Failed to retrieve booking initialization data:', error);
    return null;
  }
};

/**
 * Clear booking initialization data from localStorage
 */
export const clearBookingInitData = (): void => {
  try {
    localStorage.removeItem('bookingInitData');
    console.log('🗑️ Booking initialization data cleared from localStorage');
  } catch (error) {
    console.error('❌ Failed to clear booking initialization data:', error);
  }
};

/**
 * Extract key booking information for display/logging
 * @param data - The booking initialization response data
 * @returns Formatted booking summary
 */
export const getBookingSummary = (data: HotelBookingResponse) => {
  return {
    token: data.data.token,
    hotelName: data.data.hotel.hotelName || 'Hotel Name Not Available',
    hotelId: data.data.hotel.id,
    totalRate: data.data.totalRate,
    currency: data.data.currency,
    provider: data.provider,
    ratesCount: data.data.hotel.rates.length,
    roomsCount: data.data.hotel.rooms.length,
    onRequest: data.data.hotel.onRequest
  };
};

/**
 * Validate if booking initialization data is complete and valid
 * @param data - The booking initialization response data
 * @returns Boolean indicating if data is valid
 */
export const isBookingInitDataValid = (data: HotelBookingResponse | null): boolean => {
  if (!data) return false;
  
  return !!(
    data.data?.token &&
    data.data?.hotel?.id &&
    data.data?.totalRate &&
    data.data?.currency &&
    data.provider
  );
};
